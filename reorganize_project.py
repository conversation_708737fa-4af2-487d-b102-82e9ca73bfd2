#!/usr/bin/env python3
"""
项目重构脚本
Project Reorganization Script

将现有的混乱文件结构重新整理为清晰的项目结构
"""

import os
import shutil
import json
from pathlib import Path
from datetime import datetime


class ProjectReorganizer:
    """项目重构器"""
    
    def __init__(self, source_dir='.', target_dir='phone-marking-system-v2'):
        self.source_dir = Path(source_dir)
        self.target_dir = Path(target_dir)
        self.backup_dir = Path(f'backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}')
        
        # 文件分类映射
        self.file_mapping = {
            # 核心服务文件
            'src/services/location/': [
                'location_microservice.py',
                'location_manager.py',
                'import_location_data.py',
                'import_location_mapping.py'
            ],
            'src/services/nlp/': [
                'nlp_microservice.py',
                'smart_text_analyzer.py',
                'intelligent_preprocessor.py'
            ],
            'src/services/gateway/': [
                'api_gateway.py'
            ],
            'src/services/batch/': [
                'batch_processing_service.py',
                'concurrent_processor.py',
                'intelligent_scheduler.py'
            ],
            'src/services/admin/': [
                'web_admin_system.py'
            ],
            
            # 核心模块
            'src/core/database/': [
                'db_manager.py',
                'advanced_sqlite_manager.py',
                'optimized_db_manager.py',
                'fix_database.py',
                'upgrade_database.py'
            ],
            'src/core/cache/': [
                'enhanced_cache.py'
            ],
            'src/core/monitoring/': [
                'service_monitor.py',
                'distributed_tracing.py'
            ],
            'src/core/ml/': [
                'ml_kit_phone_marker.py',
                'brand_optimizer.py'
            ],
            'src/core/automation/': [
                'phone_auto_dialer.py',
                'phone_controller.py'
            ],
            
            # 主程序
            'src/': [
                'main_controller.py',
                'start_microservices.py',
                'local_phone_marker.py'
            ],
            
            # 配置文件
            'config/': [
                'config.json',
                'brand_configs.json',
                'nlp_config.json',
                'nlp_microservice_config.json',
                'scheduler_config.json'
            ],
            
            # 数据库文件
            'data/databases/': [
                '*.db', '*.db-shm', '*.db-wal'
            ],
            
            # 数据文件
            'data/phone_data/': [
                '号段-phone-202506-517574/'
            ],
            
            # 导出结果
            'data/exports/': [
                '*.xlsx'
            ],
            
            # 截图
            'data/screenshots/': [
                'screenshots/'
            ],
            
            # 测试文件
            'tests/unit/': [
                'test_advanced_sqlite_manager.py',
                'test_location_microservice.py',
                'test_nlp_microservice_simple.py'
            ],
            'tests/integration/': [
                'test_complete_functionality.py',
                'test_first_iteration_integration.py',
                'test_location_integration.py',
                'test_second_iteration.py',
                'test_third_iteration.py',
                'test_third_iteration_simple.py'
            ],
            'tests/performance/': [
                'test_phase1_optimizations.py',
                'test_phase2_optimizations.py',
                'test_export_format.py',
                'final_validation_test.py'
            ],
            
            # 文档
            'docs/': [
                'README.md',
                'ML_KIT_README.md',
                'OPTIMIZATION_GUIDE.md',
                'backend_design.md',
                'project_structure.md',
                '*.md'
            ],
            
            # SQL脚本
            'scripts/sql/': [
                'location_database_design.sql'
            ],
            
            # 日志文件
            'logs/': [
                '*.log'
            ]
        }
    
    def create_directory_structure(self):
        """创建目录结构"""
        directories = [
            'src/main.py',
            'src/core/database',
            'src/core/cache', 
            'src/core/monitoring',
            'src/core/ml',
            'src/core/automation',
            'src/core/utils',
            'src/services/location',
            'src/services/nlp',
            'src/services/gateway',
            'src/services/batch',
            'src/services/admin',
            'src/web/static/css',
            'src/web/static/js',
            'src/web/static/images',
            'src/web/templates',
            'src/web/api',
            'config',
            'data/databases',
            'data/phone_data',
            'data/exports',
            'data/screenshots',
            'data/uploads',
            'data/temp',
            'logs/system',
            'logs/services',
            'logs/access',
            'tests/unit',
            'tests/integration', 
            'tests/performance',
            'docs/api',
            'docs/deployment',
            'docs/user_guide',
            'docs/development',
            'scripts/sql',
            'scripts/deployment',
            'docker'
        ]
        
        print("📁 创建目录结构...")
        for directory in directories:
            dir_path = self.target_dir / directory
            dir_path.mkdir(parents=True, exist_ok=True)
            print(f"  ✓ {directory}")
    
    def move_files(self):
        """移动文件到对应目录"""
        print("\n📦 移动文件...")
        
        for target_path, source_files in self.file_mapping.items():
            target_full_path = self.target_dir / target_path
            
            for source_file in source_files:
                if '*' in source_file:
                    # 处理通配符
                    import glob
                    pattern = str(self.source_dir / source_file)
                    matched_files = glob.glob(pattern)
                    
                    for matched_file in matched_files:
                        source_path = Path(matched_file)
                        if source_path.exists():
                            target_file_path = target_full_path / source_path.name
                            self._safe_move(source_path, target_file_path)
                else:
                    source_path = self.source_dir / source_file
                    if source_path.exists():
                        if source_path.is_dir():
                            target_file_path = target_full_path / source_path.name
                            self._safe_move(source_path, target_file_path)
                        else:
                            target_file_path = target_full_path / source_path.name
                            self._safe_move(source_path, target_file_path)
    
    def _safe_move(self, source, target):
        """安全移动文件"""
        try:
            if source.exists():
                target.parent.mkdir(parents=True, exist_ok=True)
                shutil.move(str(source), str(target))
                print(f"  ✓ {source} -> {target}")
        except Exception as e:
            print(f"  ✗ 移动失败 {source}: {e}")
    
    def create_init_files(self):
        """创建__init__.py文件"""
        print("\n📄 创建__init__.py文件...")
        
        init_dirs = [
            'src',
            'src/core',
            'src/core/database',
            'src/core/cache',
            'src/core/monitoring',
            'src/core/ml',
            'src/core/automation',
            'src/core/utils',
            'src/services',
            'src/services/location',
            'src/services/nlp',
            'src/services/gateway',
            'src/services/batch',
            'src/services/admin',
            'src/web',
            'src/web/api',
            'tests',
            'tests/unit',
            'tests/integration',
            'tests/performance'
        ]
        
        for init_dir in init_dirs:
            init_file = self.target_dir / init_dir / '__init__.py'
            if not init_file.exists():
                init_file.write_text('"""{}模块"""\n'.format(init_dir.replace('/', '.')))
                print(f"  ✓ {init_file}")
    
    def create_main_entry(self):
        """创建主入口文件"""
        print("\n🚀 创建主入口文件...")
        
        main_content = '''#!/usr/bin/env python3
"""
电话号码标记识别系统 - 主程序入口
Phone Marking System - Main Entry Point
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
PROJECT_ROOT = Path(__file__).parent
sys.path.insert(0, str(PROJECT_ROOT))

from src.main_controller import MainController


def main():
    """主函数"""
    controller = MainController()
    controller.run_interactive_mode()


if __name__ == '__main__':
    main()
'''
        
        main_file = self.target_dir / 'main.py'
        main_file.write_text(main_content)
        print(f"  ✓ {main_file}")
    
    def create_requirements(self):
        """创建requirements.txt"""
        print("\n📋 创建requirements.txt...")
        
        # 复制现有的requirements.txt
        source_req = self.source_dir / 'requirements.txt'
        target_req = self.target_dir / 'requirements.txt'
        
        if source_req.exists():
            shutil.copy2(source_req, target_req)
            print(f"  ✓ 复制现有requirements.txt")
        else:
            # 创建基础requirements.txt
            basic_requirements = '''# Web框架
fastapi==0.104.1
uvicorn[standard]==0.24.0
jinja2==3.1.2

# 数据处理
pandas==2.1.3
numpy==1.24.4
openpyxl==3.1.2

# 网络请求
requests==2.31.0
httpx==0.25.2

# 认证和安全
pyjwt==2.8.0
passlib[bcrypt]==1.7.4

# 配置管理
pydantic==2.5.0
python-dotenv==1.0.0

# 测试
pytest==7.4.3
pytest-asyncio==0.21.1
'''
            target_req.write_text(basic_requirements)
            print(f"  ✓ 创建基础requirements.txt")
    
    def create_readme(self):
        """创建README.md"""
        print("\n📖 创建README.md...")
        
        readme_content = '''# 电话号码标记识别系统

一个基于微服务架构的企业级电话号码标记识别管理系统。

## 🚀 快速开始

### 安装依赖
```bash
pip install -r requirements.txt
```

### 启动系统
```bash
python main.py
```

### 访问系统
- Web管理界面: http://127.0.0.1:8080
- API网关: http://127.0.0.1:8000
- API文档: http://127.0.0.1:8000/docs

## 📁 项目结构

```
phone-marking-system-v2/
├── src/                    # 源代码
│   ├── core/              # 核心模块
│   ├── services/          # 微服务
│   └── web/               # Web前端
├── config/                # 配置文件
├── data/                  # 数据目录
├── tests/                 # 测试目录
├── docs/                  # 文档目录
└── scripts/               # 脚本目录
```

## 🔧 功能特性

- 大规模批量处理
- 智能文本分析
- 完整权限管理
- 实时监控告警
- 高性能缓存
- 分布式追踪

## 📞 支持

如有问题，请查看docs目录下的文档或提交Issue。
'''
        
        readme_file = self.target_dir / 'README.md'
        readme_file.write_text(readme_content)
        print(f"  ✓ {readme_file}")
    
    def create_gitignore(self):
        """创建.gitignore"""
        print("\n🚫 创建.gitignore...")
        
        gitignore_content = '''# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo

# Database
*.db
*.db-shm
*.db-wal

# Logs
*.log
logs/

# Data
data/uploads/*
data/exports/*
data/temp/*
!data/uploads/.gitkeep
!data/exports/.gitkeep
!data/temp/.gitkeep

# Config
config/local_*.json
.env

# OS
.DS_Store
Thumbs.db

# Backup
backup_*/
'''
        
        gitignore_file = self.target_dir / '.gitignore'
        gitignore_file.write_text(gitignore_content)
        print(f"  ✓ {gitignore_file}")
    
    def create_gitkeep_files(self):
        """创建.gitkeep文件"""
        print("\n📌 创建.gitkeep文件...")
        
        gitkeep_dirs = [
            'data/uploads',
            'data/exports', 
            'data/temp',
            'logs/system',
            'logs/services',
            'logs/access'
        ]
        
        for gitkeep_dir in gitkeep_dirs:
            gitkeep_file = self.target_dir / gitkeep_dir / '.gitkeep'
            gitkeep_file.parent.mkdir(parents=True, exist_ok=True)
            gitkeep_file.write_text('')
            print(f"  ✓ {gitkeep_file}")
    
    def generate_summary(self):
        """生成重构总结"""
        print("\n📊 生成重构总结...")
        
        summary = {
            "重构时间": datetime.now().isoformat(),
            "源目录": str(self.source_dir),
            "目标目录": str(self.target_dir),
            "文件统计": {
                "总文件数": 0,
                "移动成功": 0,
                "移动失败": 0
            },
            "目录结构": {
                "核心模块": "src/core/",
                "微服务": "src/services/",
                "Web前端": "src/web/",
                "配置文件": "config/",
                "数据目录": "data/",
                "测试目录": "tests/",
                "文档目录": "docs/"
            }
        }
        
        summary_file = self.target_dir / 'REORGANIZATION_SUMMARY.json'
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)
        
        print(f"  ✓ {summary_file}")
    
    def run(self):
        """执行重构"""
        print("🔄 开始项目重构...")
        print(f"源目录: {self.source_dir}")
        print(f"目标目录: {self.target_dir}")
        
        # 创建目录结构
        self.create_directory_structure()
        
        # 移动文件
        self.move_files()
        
        # 创建Python包文件
        self.create_init_files()
        
        # 创建主入口
        self.create_main_entry()
        
        # 创建requirements.txt
        self.create_requirements()
        
        # 创建README.md
        self.create_readme()
        
        # 创建.gitignore
        self.create_gitignore()
        
        # 创建.gitkeep文件
        self.create_gitkeep_files()
        
        # 生成重构总结
        self.generate_summary()
        
        print(f"\n✅ 项目重构完成！")
        print(f"新项目目录: {self.target_dir}")
        print(f"请查看 {self.target_dir}/REORGANIZATION_SUMMARY.json 了解详情")


if __name__ == '__main__':
    reorganizer = ProjectReorganizer()
    reorganizer.run()
