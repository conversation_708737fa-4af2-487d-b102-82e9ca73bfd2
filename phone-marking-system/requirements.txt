# Web框架
fastapi==0.104.1
uvicorn[standard]==0.24.0
jinja2==3.1.2
python-multipart==0.0.6

# 数据库
sqlite3
sqlalchemy==2.0.23
alembic==1.12.1

# 缓存
redis==5.0.1

# 数据处理
pandas==2.1.3
numpy==1.24.4
openpyxl==3.1.2
xlrd==2.0.1

# 网络请求
requests==2.31.0
httpx==0.25.2
aiohttp==3.9.1

# 认证和安全
pyjwt==2.8.0
passlib[bcrypt]==1.7.4
python-jose[cryptography]==3.3.0

# 日志和监控
structlog==23.2.0
prometheus-client==0.19.0

# 文本处理和NLP
jieba==0.42.1
scikit-learn==1.3.2
nltk==3.8.1

# 异步处理
asyncio
aiofiles==23.2.1
celery==5.3.4

# 配置管理
pydantic==2.5.0
pydantic-settings==2.1.0
python-dotenv==1.0.0

# 测试
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
httpx==0.25.2

# 开发工具
black==23.11.0
flake8==6.1.0
mypy==1.7.1
pre-commit==3.6.0

# 数据验证
marshmallow==3.20.1
cerberus==1.3.5

# 时间处理
python-dateutil==2.8.2
pytz==2023.3

# 文件处理
pathlib2==2.3.7
chardet==5.2.0

# 图像处理（可选）
pillow==10.1.0

# 加密
cryptography==41.0.8

# 进度条
tqdm==4.66.1

# 邮件发送（可选）
smtplib
email-validator==2.1.0

# 系统监控
psutil==5.9.6

# 数据序列化
msgpack==1.0.7
pickle

# 正则表达式增强
regex==2023.10.3

# 并发处理
concurrent.futures
threading
multiprocessing

# 网络工具
socket
urllib3==2.1.0

# 数学计算
math
statistics

# 系统工具
os
sys
pathlib
shutil
glob

# 时间工具
time
datetime
calendar

# 随机数
random
uuid

# JSON处理
json
yaml

# 环境变量
environ

# 信号处理
signal

# 进程管理
subprocess

# 内存管理
gc
weakref

# 数据结构
collections
dataclasses
enum
typing

# 错误处理
traceback
warnings

# 编码处理
base64
hashlib
hmac

# 压缩
gzip
zipfile
tarfile

# 网络协议
http
urllib
ssl

# 线程和协程
threading
asyncio
concurrent.futures

# 文件系统
tempfile
shutil
pathlib

# 配置解析
configparser
argparse

# 日志
logging
logging.handlers

# 数据库连接池（可选）
sqlalchemy-pool==2.0.23

# 分布式任务队列（可选）
rq==1.15.1

# 消息队列（可选）
pika==1.3.2

# 缓存后端（可选）
diskcache==5.6.3

# 性能分析（可选）
cProfile
line_profiler==4.1.1

# 内存分析（可选）
memory_profiler==0.61.0

# 代码质量
pylint==3.0.3
bandit==1.7.5

# 文档生成
sphinx==7.2.6
sphinx-rtd-theme==1.3.0

# API文档
fastapi-users==12.1.2
fastapi-pagination==0.12.13

# 中间件
starlette==0.27.0

# 模板引擎
jinja2==3.1.2
markupsafe==2.1.3

# 静态文件服务
whitenoise==6.6.0

# 跨域处理
fastapi-cors==0.0.6

# 限流
slowapi==0.1.9

# 健康检查
fastapi-health==0.4.0

# 配置验证
pydantic-yaml==1.2.0

# 数据迁移
yoyo-migrations==8.2.0

# 备份工具
backup-utils==1.0.0

# 监控集成
sentry-sdk==1.38.0

# 性能监控
newrelic==9.2.0

# 日志聚合
loguru==0.7.2

# 分布式锁
redis-py-cluster==2.1.3

# 任务调度
apscheduler==3.10.4

# 消息通知
pushbullet.py==0.12.0

# 短信发送（可选）
twilio==8.11.0

# 邮件模板
premailer==3.10.0

# 二维码生成（可选）
qrcode==7.4.2

# 条形码生成（可选）
python-barcode==0.15.1

# Excel高级处理
xlsxwriter==3.1.9

# PDF处理（可选）
reportlab==4.0.7
pypdf2==3.0.1

# 图表生成（可选）
matplotlib==3.8.2
seaborn==0.13.0
plotly==5.17.0

# 地理位置处理
geopy==2.4.1

# IP地址处理
ipaddress
geoip2==4.7.0

# 用户代理解析
user-agents==2.2.0

# 设备检测
mobile-detect==0.1.3

# 文件类型检测
python-magic==0.4.27

# 数据压缩
lz4==4.3.2
zstandard==0.22.0

# 内容检测
chardet==5.2.0
langdetect==1.0.9

# 机器学习（可选）
tensorflow==2.15.0
torch==2.1.1
transformers==4.36.0

# 自然语言处理
spacy==3.7.2
textblob==0.17.1

# 数据可视化
bokeh==3.3.2
dash==2.14.2

# 爬虫工具（可选）
scrapy==2.11.0
beautifulsoup4==4.12.2

# 代理工具
requests-oauthlib==1.3.1
requests-cache==1.1.1
