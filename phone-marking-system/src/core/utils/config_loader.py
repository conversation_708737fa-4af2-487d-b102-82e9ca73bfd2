"""
配置加载器
Configuration Loader

统一的配置文件加载和管理工具
支持JSON、YAML等格式的配置文件
提供配置验证、合并、环境变量替换等功能
"""

import os
import json
import logging
from typing import Dict, Any, Optional, Union
from pathlib import Path


class ConfigLoader:
    """
    配置加载器
    
    功能特性:
    - 支持多种配置文件格式
    - 环境变量替换
    - 配置验证
    - 配置合并
    - 默认值处理
    """
    
    def __init__(self, base_path: Optional[str] = None):
        """
        初始化配置加载器
        
        Args:
            base_path: 配置文件基础路径
        """
        self.base_path = Path(base_path) if base_path else Path.cwd()
        self.logger = logging.getLogger(__name__)
        
        # 支持的文件格式
        self.supported_formats = {
            '.json': self._load_json,
            '.yaml': self._load_yaml,
            '.yml': self._load_yaml
        }
    
    def load_config(self, config_path: Union[str, Path]) -> Dict[str, Any]:
        """
        加载配置文件
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            配置字典
        """
        config_path = Path(config_path)
        
        # 如果是相对路径，基于base_path解析
        if not config_path.is_absolute():
            config_path = self.base_path / config_path
        
        if not config_path.exists():
            raise FileNotFoundError(f"配置文件不存在: {config_path}")
        
        # 根据文件扩展名选择加载器
        file_ext = config_path.suffix.lower()
        if file_ext not in self.supported_formats:
            raise ValueError(f"不支持的配置文件格式: {file_ext}")
        
        try:
            loader = self.supported_formats[file_ext]
            config = loader(config_path)
            
            # 环境变量替换
            config = self._replace_env_vars(config)
            
            self.logger.info(f"成功加载配置文件: {config_path}")
            return config
            
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {config_path}, 错误: {e}")
            raise
    
    def _load_json(self, config_path: Path) -> Dict[str, Any]:
        """加载JSON配置文件"""
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def _load_yaml(self, config_path: Path) -> Dict[str, Any]:
        """加载YAML配置文件"""
        try:
            import yaml
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except ImportError:
            raise ImportError("需要安装PyYAML库来支持YAML配置文件")
    
    def _replace_env_vars(self, config: Any) -> Any:
        """
        递归替换配置中的环境变量
        
        支持格式: ${ENV_VAR} 或 ${ENV_VAR:default_value}
        """
        if isinstance(config, dict):
            return {key: self._replace_env_vars(value) for key, value in config.items()}
        elif isinstance(config, list):
            return [self._replace_env_vars(item) for item in config]
        elif isinstance(config, str):
            return self._replace_env_var_string(config)
        else:
            return config
    
    def _replace_env_var_string(self, value: str) -> str:
        """替换字符串中的环境变量"""
        import re
        
        # 匹配 ${VAR} 或 ${VAR:default} 格式
        pattern = r'\$\{([^}:]+)(?::([^}]*))?\}'
        
        def replace_match(match):
            var_name = match.group(1)
            default_value = match.group(2) if match.group(2) is not None else ''
            
            return os.getenv(var_name, default_value)
        
        return re.sub(pattern, replace_match, value)
    
    def save_config(self, config: Dict[str, Any], config_path: Union[str, Path]) -> bool:
        """
        保存配置到文件
        
        Args:
            config: 配置字典
            config_path: 配置文件路径
            
        Returns:
            是否保存成功
        """
        config_path = Path(config_path)
        
        # 如果是相对路径，基于base_path解析
        if not config_path.is_absolute():
            config_path = self.base_path / config_path
        
        # 确保目录存在
        config_path.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            file_ext = config_path.suffix.lower()
            
            if file_ext == '.json':
                with open(config_path, 'w', encoding='utf-8') as f:
                    json.dump(config, f, indent=2, ensure_ascii=False)
            elif file_ext in ['.yaml', '.yml']:
                try:
                    import yaml
                    with open(config_path, 'w', encoding='utf-8') as f:
                        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
                except ImportError:
                    raise ImportError("需要安装PyYAML库来支持YAML配置文件")
            else:
                raise ValueError(f"不支持的配置文件格式: {file_ext}")
            
            self.logger.info(f"成功保存配置文件: {config_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存配置文件失败: {config_path}, 错误: {e}")
            return False
    
    def merge_configs(self, *configs: Dict[str, Any]) -> Dict[str, Any]:
        """
        合并多个配置字典
        
        Args:
            *configs: 要合并的配置字典
            
        Returns:
            合并后的配置字典
        """
        result = {}
        
        for config in configs:
            result = self._deep_merge(result, config)
        
        return result
    
    def _deep_merge(self, base: Dict[str, Any], update: Dict[str, Any]) -> Dict[str, Any]:
        """深度合并两个字典"""
        result = base.copy()
        
        for key, value in update.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._deep_merge(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def validate_config(self, config: Dict[str, Any], schema: Dict[str, Any]) -> bool:
        """
        验证配置是否符合模式
        
        Args:
            config: 要验证的配置
            schema: 配置模式
            
        Returns:
            是否验证通过
        """
        try:
            # 这里可以集成jsonschema等库进行更严格的验证
            # 目前实现简单的必需字段检查
            return self._validate_required_fields(config, schema)
        except Exception as e:
            self.logger.error(f"配置验证失败: {e}")
            return False
    
    def _validate_required_fields(self, config: Dict[str, Any], schema: Dict[str, Any]) -> bool:
        """验证必需字段"""
        required_fields = schema.get('required', [])
        
        for field in required_fields:
            if field not in config:
                self.logger.error(f"缺少必需的配置字段: {field}")
                return False
        
        return True
    
    def get_config_value(self, config: Dict[str, Any], key_path: str, default: Any = None) -> Any:
        """
        使用点号分隔的路径获取配置值
        
        Args:
            config: 配置字典
            key_path: 键路径，如 'database.host'
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key_path.split('.')
        value = config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def set_config_value(self, config: Dict[str, Any], key_path: str, value: Any) -> Dict[str, Any]:
        """
        使用点号分隔的路径设置配置值
        
        Args:
            config: 配置字典
            key_path: 键路径，如 'database.host'
            value: 要设置的值
            
        Returns:
            更新后的配置字典
        """
        keys = key_path.split('.')
        current = config
        
        # 创建嵌套字典结构
        for key in keys[:-1]:
            if key not in current:
                current[key] = {}
            current = current[key]
        
        # 设置最终值
        current[keys[-1]] = value
        
        return config


# 全局配置加载器实例
_config_loader = None


def get_config_loader(base_path: Optional[str] = None) -> ConfigLoader:
    """获取全局配置加载器实例"""
    global _config_loader
    
    if _config_loader is None:
        _config_loader = ConfigLoader(base_path)
    
    return _config_loader


def load_config(config_path: Union[str, Path]) -> Dict[str, Any]:
    """便捷函数：加载配置文件"""
    loader = get_config_loader()
    return loader.load_config(config_path)


def save_config(config: Dict[str, Any], config_path: Union[str, Path]) -> bool:
    """便捷函数：保存配置文件"""
    loader = get_config_loader()
    return loader.save_config(config, config_path)
