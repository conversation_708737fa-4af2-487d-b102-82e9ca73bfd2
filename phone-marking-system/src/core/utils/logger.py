"""
日志工具
Logger Utilities

统一的日志配置和管理工具
支持多种日志格式、输出目标和日志级别
提供结构化日志、日志轮转、性能监控等功能
"""

import os
import sys
import json
import logging
import logging.config
from typing import Dict, Any, Optional
from pathlib import Path
from datetime import datetime


class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器"""
    
    # ANSI颜色代码
    COLORS = {
        'DEBUG': '\033[36m',    # 青色
        'INFO': '\033[32m',     # 绿色
        'WARNING': '\033[33m',  # 黄色
        'ERROR': '\033[31m',    # 红色
        'CRITICAL': '\033[35m', # 紫色
        'RESET': '\033[0m'      # 重置
    }
    
    def format(self, record):
        # 添加颜色
        if hasattr(record, 'levelname'):
            color = self.COLORS.get(record.levelname, self.COLORS['RESET'])
            record.levelname = f"{color}{record.levelname}{self.COLORS['RESET']}"
        
        return super().format(record)


class StructuredFormatter(logging.Formatter):
    """结构化日志格式化器"""
    
    def format(self, record):
        # 创建结构化日志记录
        log_entry = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno,
            'thread': record.thread,
            'thread_name': record.threadName,
            'process': record.process
        }
        
        # 添加异常信息
        if record.exc_info:
            log_entry['exception'] = self.formatException(record.exc_info)
        
        # 添加额外字段
        if hasattr(record, 'extra_fields'):
            log_entry.update(record.extra_fields)
        
        return json.dumps(log_entry, ensure_ascii=False)


class PerformanceFilter(logging.Filter):
    """性能监控过滤器"""
    
    def __init__(self, threshold_ms: float = 1000):
        """
        初始化性能过滤器
        
        Args:
            threshold_ms: 性能阈值（毫秒）
        """
        super().__init__()
        self.threshold_ms = threshold_ms
    
    def filter(self, record):
        # 检查是否有性能信息
        if hasattr(record, 'duration_ms'):
            if record.duration_ms > self.threshold_ms:
                record.msg = f"[SLOW] {record.msg} (耗时: {record.duration_ms:.2f}ms)"
        
        return True


class LoggerManager:
    """
    日志管理器
    
    功能特性:
    - 统一的日志配置
    - 多种输出格式
    - 日志轮转
    - 性能监控
    - 结构化日志
    """
    
    def __init__(self):
        self.configured = False
        self.loggers = {}
    
    def setup_logging(self, config_path: Optional[str] = None, 
                     log_level: Optional[str] = None,
                     enable_colors: bool = True) -> bool:
        """
        设置日志配置
        
        Args:
            config_path: 日志配置文件路径
            log_level: 日志级别
            enable_colors: 是否启用彩色输出
            
        Returns:
            是否配置成功
        """
        try:
            if config_path and os.path.exists(config_path):
                # 从配置文件加载
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                # 确保日志目录存在
                self._ensure_log_directories(config)
                
                # 应用配置
                logging.config.dictConfig(config)
                
            else:
                # 使用默认配置
                self._setup_default_logging(log_level, enable_colors)
            
            self.configured = True
            
            # 记录配置成功
            logger = logging.getLogger(__name__)
            logger.info("日志系统配置成功")
            
            return True
            
        except Exception as e:
            print(f"日志配置失败: {e}")
            # 使用基本配置作为后备
            logging.basicConfig(
                level=logging.INFO,
                format='%(asctime)s [%(levelname)s] %(name)s: %(message)s'
            )
            return False
    
    def _ensure_log_directories(self, config: Dict[str, Any]):
        """确保日志目录存在"""
        handlers = config.get('handlers', {})
        
        for handler_config in handlers.values():
            if 'filename' in handler_config:
                log_file = Path(handler_config['filename'])
                log_file.parent.mkdir(parents=True, exist_ok=True)
    
    def _setup_default_logging(self, log_level: Optional[str] = None, 
                              enable_colors: bool = True):
        """设置默认日志配置"""
        level = getattr(logging, log_level.upper()) if log_level else logging.INFO
        
        # 创建根日志器
        root_logger = logging.getLogger()
        root_logger.setLevel(level)
        
        # 清除现有处理器
        root_logger.handlers.clear()
        
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(level)
        
        if enable_colors and sys.stdout.isatty():
            # 使用彩色格式化器
            console_formatter = ColoredFormatter(
                '%(asctime)s [%(levelname)s] %(name)s: %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
        else:
            # 使用标准格式化器
            console_formatter = logging.Formatter(
                '%(asctime)s [%(levelname)s] %(name)s: %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
        
        console_handler.setFormatter(console_formatter)
        root_logger.addHandler(console_handler)
        
        # 文件处理器（如果可能）
        try:
            log_dir = Path('./logs/system')
            log_dir.mkdir(parents=True, exist_ok=True)
            
            file_handler = logging.handlers.RotatingFileHandler(
                log_dir / 'system.log',
                maxBytes=10*1024*1024,  # 10MB
                backupCount=5,
                encoding='utf-8'
            )
            file_handler.setLevel(level)
            
            file_formatter = logging.Formatter(
                '%(asctime)s [%(levelname)s] %(name)s [%(filename)s:%(lineno)d] %(funcName)s(): %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            file_handler.setFormatter(file_formatter)
            root_logger.addHandler(file_handler)
            
        except Exception:
            # 如果文件处理器创建失败，只使用控制台输出
            pass
    
    def get_logger(self, name: str, **kwargs) -> logging.Logger:
        """
        获取日志器
        
        Args:
            name: 日志器名称
            **kwargs: 额外配置
            
        Returns:
            日志器实例
        """
        if name not in self.loggers:
            logger = logging.getLogger(name)
            
            # 添加性能过滤器
            if kwargs.get('enable_performance_filter', False):
                threshold = kwargs.get('performance_threshold_ms', 1000)
                performance_filter = PerformanceFilter(threshold)
                logger.addFilter(performance_filter)
            
            self.loggers[name] = logger
        
        return self.loggers[name]
    
    def log_performance(self, logger: logging.Logger, operation: str, 
                       duration_ms: float, **extra):
        """
        记录性能日志
        
        Args:
            logger: 日志器
            operation: 操作名称
            duration_ms: 耗时（毫秒）
            **extra: 额外信息
        """
        # 创建带有性能信息的日志记录
        extra_fields = {
            'operation': operation,
            'duration_ms': duration_ms,
            **extra
        }
        
        # 使用LoggerAdapter添加额外字段
        adapter = logging.LoggerAdapter(logger, extra_fields)
        
        if duration_ms > 1000:  # 超过1秒的操作
            adapter.warning(f"操作耗时较长: {operation}")
        else:
            adapter.info(f"操作完成: {operation}")
    
    def log_structured(self, logger: logging.Logger, level: str, 
                      message: str, **fields):
        """
        记录结构化日志
        
        Args:
            logger: 日志器
            level: 日志级别
            message: 日志消息
            **fields: 结构化字段
        """
        # 创建带有额外字段的日志记录
        record = logger.makeRecord(
            logger.name, getattr(logging, level.upper()),
            '', 0, message, (), None
        )
        record.extra_fields = fields
        
        logger.handle(record)


# 全局日志管理器实例
_logger_manager = None


def get_logger_manager() -> LoggerManager:
    """获取全局日志管理器实例"""
    global _logger_manager
    
    if _logger_manager is None:
        _logger_manager = LoggerManager()
    
    return _logger_manager


def setup_logging(config_path: Optional[str] = None, 
                 log_level: Optional[str] = None,
                 enable_colors: bool = True) -> bool:
    """便捷函数：设置日志配置"""
    manager = get_logger_manager()
    return manager.setup_logging(config_path, log_level, enable_colors)


def get_logger(name: str, **kwargs) -> logging.Logger:
    """便捷函数：获取日志器"""
    manager = get_logger_manager()
    return manager.get_logger(name, **kwargs)


def log_performance(operation: str, duration_ms: float, 
                   logger_name: str = __name__, **extra):
    """便捷函数：记录性能日志"""
    manager = get_logger_manager()
    logger = manager.get_logger(logger_name)
    manager.log_performance(logger, operation, duration_ms, **extra)


def log_structured(level: str, message: str, 
                  logger_name: str = __name__, **fields):
    """便捷函数：记录结构化日志"""
    manager = get_logger_manager()
    logger = manager.get_logger(logger_name)
    manager.log_structured(logger, level, message, **fields)
