#!/usr/bin/env python3
"""
电话号码标记识别系统 - 主程序入口
Phone Marking System - Main Entry Point

统一管理所有微服务和功能模块的主程序
提供完整的系统控制、配置管理、服务协调等功能

Author: System
Version: 1.0.0
"""

import os
import sys
import asyncio
import logging
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
PROJECT_ROOT = Path(__file__).parent.parent
sys.path.insert(0, str(PROJECT_ROOT))

from src.core.utils.config_loader import ConfigLoader
from src.core.utils.logger import setup_logging
from src.core.controller import SystemController


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description='电话号码标记识别系统',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  %(prog)s start                    # 启动所有服务
  %(prog)s stop                     # 停止所有服务
  %(prog)s status                   # 查看服务状态
  %(prog)s --config custom.json    # 使用自定义配置文件
  %(prog)s --interactive           # 交互模式
        """
    )
    
    parser.add_argument(
        'action',
        nargs='?',
        choices=['start', 'stop', 'restart', 'status', 'interactive'],
        default='interactive',
        help='执行的操作'
    )
    
    parser.add_argument(
        '--config', '-c',
        default='config/system_config.json',
        help='系统配置文件路径 (默认: config/system_config.json)'
    )
    
    parser.add_argument(
        '--services-config', '-s',
        default='config/services_config.json',
        help='服务配置文件路径 (默认: config/services_config.json)'
    )
    
    parser.add_argument(
        '--database-config', '-d',
        default='config/database_config.json',
        help='数据库配置文件路径 (默认: config/database_config.json)'
    )
    
    parser.add_argument(
        '--log-config', '-l',
        default='config/logging_config.json',
        help='日志配置文件路径 (默认: config/logging_config.json)'
    )
    
    parser.add_argument(
        '--service',
        help='指定要操作的服务名称'
    )
    
    parser.add_argument(
        '--port', '-p',
        type=int,
        help='指定服务端口'
    )
    
    parser.add_argument(
        '--host',
        default='127.0.0.1',
        help='指定服务主机地址 (默认: 127.0.0.1)'
    )
    
    parser.add_argument(
        '--debug',
        action='store_true',
        help='启用调试模式'
    )
    
    parser.add_argument(
        '--daemon',
        action='store_true',
        help='以守护进程模式运行'
    )
    
    parser.add_argument(
        '--version', '-v',
        action='version',
        version='电话号码标记识别系统 v1.0.0'
    )
    
    return parser.parse_args()


def print_banner():
    """打印系统横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                          电话号码标记识别系统                                ║
║                     Phone Number Marking System                             ║
║                                                                              ║
║  版本: v1.0.0                                                               ║
║  架构: 微服务架构                                                            ║
║  技术栈: Python + FastAPI + SQLite + Vue.js                                ║
║                                                                              ║
║  功能特性:                                                                   ║
║  • 大规模号码批量处理                                                        ║
║  • 智能文本分析和分类                                                        ║
║  • 完整的权限管理系统                                                        ║
║  • 实时监控和告警                                                            ║
║  • 高性能缓存优化                                                            ║
║  • 分布式追踪系统                                                            ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """
    print(banner)


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_arguments()
    
    # 打印横幅
    if not args.daemon:
        print_banner()
    
    try:
        # 切换到项目根目录
        os.chdir(PROJECT_ROOT)
        
        # 加载配置
        config_loader = ConfigLoader()
        
        # 加载系统配置
        system_config = config_loader.load_config(args.config)
        services_config = config_loader.load_config(args.services_config)
        database_config = config_loader.load_config(args.database_config)
        
        # 合并配置
        config = {
            **system_config,
            'services': services_config.get('services', {}),
            'database': database_config.get('databases', {}),
            'sharding': database_config.get('sharding', {}),
            'backup': database_config.get('backup', {}),
            'sync': database_config.get('sync', {})
        }
        
        # 设置调试模式
        if args.debug:
            config['system']['debug'] = True
            config['system']['log_level'] = 'DEBUG'
        
        # 设置日志
        setup_logging(args.log_config)
        logger = logging.getLogger(__name__)
        
        logger.info("系统启动中...")
        logger.info(f"配置文件: {args.config}")
        logger.info(f"运行模式: {'守护进程' if args.daemon else '交互模式'}")
        
        # 创建系统控制器
        controller = SystemController(config)
        
        # 根据操作执行相应的功能
        if args.action == 'start':
            if args.service:
                # 启动指定服务
                success = controller.start_service(args.service)
                if success:
                    logger.info(f"服务 {args.service} 启动成功")
                    if not args.daemon:
                        print(f"✅ 服务 {args.service} 启动成功")
                else:
                    logger.error(f"服务 {args.service} 启动失败")
                    if not args.daemon:
                        print(f"❌ 服务 {args.service} 启动失败")
                    sys.exit(1)
            else:
                # 启动所有服务
                success = controller.start_all_services()
                if success:
                    logger.info("所有服务启动成功")
                    if not args.daemon:
                        print("✅ 所有服务启动成功")
                        controller.print_service_info()
                else:
                    logger.error("部分服务启动失败")
                    if not args.daemon:
                        print("⚠️ 部分服务启动失败")
                    sys.exit(1)
            
            # 如果是守护进程模式，保持运行
            if args.daemon:
                try:
                    controller.run_daemon_mode()
                except KeyboardInterrupt:
                    logger.info("接收到停止信号")
                    controller.stop_all_services()
        
        elif args.action == 'stop':
            if args.service:
                # 停止指定服务
                success = controller.stop_service(args.service)
                if success:
                    logger.info(f"服务 {args.service} 停止成功")
                    print(f"✅ 服务 {args.service} 停止成功")
                else:
                    logger.error(f"服务 {args.service} 停止失败")
                    print(f"❌ 服务 {args.service} 停止失败")
            else:
                # 停止所有服务
                success = controller.stop_all_services()
                if success:
                    logger.info("所有服务停止成功")
                    print("✅ 所有服务停止成功")
                else:
                    logger.error("部分服务停止失败")
                    print("⚠️ 部分服务停止失败")
        
        elif args.action == 'restart':
            if args.service:
                # 重启指定服务
                success = controller.restart_service(args.service)
                if success:
                    logger.info(f"服务 {args.service} 重启成功")
                    print(f"✅ 服务 {args.service} 重启成功")
                else:
                    logger.error(f"服务 {args.service} 重启失败")
                    print(f"❌ 服务 {args.service} 重启失败")
            else:
                # 重启所有服务
                controller.stop_all_services()
                success = controller.start_all_services()
                if success:
                    logger.info("所有服务重启成功")
                    print("✅ 所有服务重启成功")
                    controller.print_service_info()
                else:
                    logger.error("服务重启失败")
                    print("❌ 服务重启失败")
        
        elif args.action == 'status':
            # 显示服务状态
            controller.print_service_info()
            status = controller.get_system_status()
            
            print("\n📊 系统统计信息:")
            print(f"  运行时间: {status['system_info']['uptime_formatted']}")
            print(f"  活跃服务: {len([s for s in status['services'].values() if s['status'] == 'running'])}")
            print(f"  系统组件: 监控{'✅' if status['system_components']['monitor_enabled'] else '❌'} "
                  f"缓存{'✅' if status['system_components']['cache_enabled'] else '❌'} "
                  f"追踪{'✅' if status['system_components']['tracing_enabled'] else '❌'}")
        
        elif args.action == 'interactive':
            # 交互模式
            if not args.daemon:
                controller.run_interactive_mode()
            else:
                logger.warning("守护进程模式不支持交互模式")
                print("⚠️ 守护进程模式不支持交互模式")
        
    except KeyboardInterrupt:
        print("\n\n🛑 用户中断，正在停止系统...")
        if 'controller' in locals():
            controller.stop_all_services()
        sys.exit(0)
    
    except Exception as e:
        error_msg = f"系统运行异常: {e}"
        if 'logger' in locals():
            logger.error(error_msg, exc_info=True)
        else:
            print(f"❌ {error_msg}")
        
        if 'controller' in locals():
            controller.stop_all_services()
        sys.exit(1)


if __name__ == '__main__':
    main()
