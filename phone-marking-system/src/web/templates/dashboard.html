{% extends "base.html" %}

{% block title %}仪表板 - 电话号码标记识别系统{% endblock %}
{% block page_title %}仪表板{% endblock %}

{% block extra_css %}
<style>
    .stats-card {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
        color: white;
        border: none;
        transition: transform 0.3s ease;
    }
    
    .stats-card:hover {
        transform: translateY(-5px);
    }
    
    .stats-card.success {
        background: linear-gradient(135deg, var(--success-color) 0%, #047857 100%);
    }
    
    .stats-card.warning {
        background: linear-gradient(135deg, var(--warning-color) 0%, #b45309 100%);
    }
    
    .stats-card.info {
        background: linear-gradient(135deg, var(--info-color) 0%, #0e7490 100%);
    }
    
    .stats-number {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }
    
    .stats-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }
    
    .stats-icon {
        font-size: 3rem;
        opacity: 0.3;
        position: absolute;
        right: 1rem;
        top: 1rem;
    }
    
    .chart-container {
        position: relative;
        height: 300px;
    }
    
    .activity-item {
        display: flex;
        align-items: center;
        padding: 0.75rem 0;
        border-bottom: 1px solid var(--border-color);
    }
    
    .activity-item:last-child {
        border-bottom: none;
    }
    
    .activity-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        font-size: 1.1rem;
    }
    
    .activity-icon.success {
        background-color: rgba(5, 150, 105, 0.1);
        color: var(--success-color);
    }
    
    .activity-icon.warning {
        background-color: rgba(217, 119, 6, 0.1);
        color: var(--warning-color);
    }
    
    .activity-icon.info {
        background-color: rgba(8, 145, 178, 0.1);
        color: var(--info-color);
    }
    
    .activity-content {
        flex: 1;
    }
    
    .activity-title {
        font-weight: 500;
        margin-bottom: 0.25rem;
    }
    
    .activity-time {
        font-size: 0.85rem;
        color: var(--secondary-color);
    }
    
    .device-status {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 1rem;
        background-color: var(--light-color);
        border-radius: 8px;
        margin-bottom: 1rem;
    }
    
    .device-info {
        display: flex;
        align-items: center;
    }
    
    .device-icon {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        background-color: white;
        color: var(--primary-color);
    }
    
    .device-details h6 {
        margin: 0;
        font-weight: 600;
    }
    
    .device-details small {
        color: var(--secondary-color);
    }
</style>
{% endblock %}

{% block content %}
<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card">
            <div class="card-body position-relative">
                <div class="stats-number" id="totalProcessed">{{ stats.total_processed or 0 }}</div>
                <div class="stats-label">总处理数量</div>
                <i class="bi bi-graph-up stats-icon"></i>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card success">
            <div class="card-body position-relative">
                <div class="stats-number" id="successRate">{{ "%.1f"|format(stats.success_rate or 0) }}%</div>
                <div class="stats-label">成功率</div>
                <i class="bi bi-check-circle stats-icon"></i>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card warning">
            <div class="card-body position-relative">
                <div class="stats-number" id="activeTasks">{{ stats.active_tasks or 0 }}</div>
                <div class="stats-label">活跃任务</div>
                <i class="bi bi-clock stats-icon"></i>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card info">
            <div class="card-body position-relative">
                <div class="stats-number" id="onlineDevices">{{ stats.online_devices or 0 }}</div>
                <div class="stats-label">在线设备</div>
                <i class="bi bi-phone stats-icon"></i>
            </div>
        </div>
    </div>
</div>

<!-- 图表和活动 -->
<div class="row">
    <!-- 处理趋势图表 -->
    <div class="col-xl-8 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title">处理趋势</h5>
                <div class="btn-group btn-group-sm" role="group">
                    <input type="radio" class="btn-check" name="chartPeriod" id="chart7d" value="7d" checked>
                    <label class="btn btn-outline-primary" for="chart7d">7天</label>
                    
                    <input type="radio" class="btn-check" name="chartPeriod" id="chart30d" value="30d">
                    <label class="btn btn-outline-primary" for="chart30d">30天</label>
                    
                    <input type="radio" class="btn-check" name="chartPeriod" id="chart90d" value="90d">
                    <label class="btn btn-outline-primary" for="chart90d">90天</label>
                </div>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="processingChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 最近活动 -->
    <div class="col-xl-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">最近活动</h5>
            </div>
            <div class="card-body">
                <div id="recentActivities">
                    <div class="activity-item">
                        <div class="activity-icon success">
                            <i class="bi bi-check-circle"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">批量处理任务完成</div>
                            <div class="activity-time">2分钟前</div>
                        </div>
                    </div>
                    
                    <div class="activity-item">
                        <div class="activity-icon info">
                            <i class="bi bi-upload"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">新文件上传</div>
                            <div class="activity-time">5分钟前</div>
                        </div>
                    </div>
                    
                    <div class="activity-item">
                        <div class="activity-icon warning">
                            <i class="bi bi-exclamation-triangle"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">设备连接异常</div>
                            <div class="activity-time">10分钟前</div>
                        </div>
                    </div>
                    
                    <div class="activity-item">
                        <div class="activity-icon success">
                            <i class="bi bi-person-plus"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">新用户注册</div>
                            <div class="activity-time">15分钟前</div>
                        </div>
                    </div>
                </div>
                
                <div class="text-center mt-3">
                    <a href="{{ url_for('system_logs') }}" class="btn btn-outline-primary btn-sm">
                        查看所有活动
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 服务状态和设备状态 -->
<div class="row">
    <!-- 服务状态 -->
    <div class="col-xl-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">服务状态</h5>
            </div>
            <div class="card-body">
                <div id="serviceStatus">
                    {% for service_name, service_info in services.items() %}
                    <div class="device-status">
                        <div class="device-info">
                            <div class="device-icon">
                                <i class="bi bi-server"></i>
                            </div>
                            <div class="device-details">
                                <h6>{{ service_info.name or service_name }}</h6>
                                <small>端口: {{ service_info.port }}</small>
                            </div>
                        </div>
                        <div>
                            <span class="status-indicator status-{{ 'online' if service_info.status == 'running' else 'offline' }}"></span>
                            <span class="badge bg-{{ 'success' if service_info.status == 'running' else 'secondary' }}">
                                {{ '运行中' if service_info.status == 'running' else '已停止' }}
                            </span>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- 设备状态 -->
    <div class="col-xl-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title">设备状态</h5>
                <button class="btn btn-outline-primary btn-sm" onclick="refreshDeviceStatus()">
                    <i class="bi bi-arrow-clockwise"></i> 刷新
                </button>
            </div>
            <div class="card-body">
                <div id="deviceStatus">
                    {% for device in devices %}
                    <div class="device-status">
                        <div class="device-info">
                            <div class="device-icon">
                                <i class="bi bi-{{ 'phone' if device.device_type == 'android' else 'tablet' }}"></i>
                            </div>
                            <div class="device-details">
                                <h6>{{ device.device_name }}</h6>
                                <small>{{ device.device_type.upper() }} • 最后活动: {{ device.last_seen_formatted }}</small>
                            </div>
                        </div>
                        <div>
                            <span class="status-indicator status-{{ device.status }}"></span>
                            <span class="badge bg-{{ 'success' if device.status == 'online' else 'secondary' if device.status == 'offline' else 'danger' }}">
                                {{ {'online': '在线', 'offline': '离线', 'error': '错误'}.get(device.status, device.status) }}
                            </span>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                
                <div class="text-center mt-3">
                    <a href="{{ url_for('device_manage') }}" class="btn btn-outline-primary btn-sm">
                        管理所有设备
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 快速操作 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">快速操作</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('data_import') }}" class="btn btn-primary w-100">
                            <i class="bi bi-upload me-2"></i>
                            导入数据
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('data_export') }}" class="btn btn-success w-100">
                            <i class="bi bi-download me-2"></i>
                            导出结果
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('device_manage') }}" class="btn btn-info w-100">
                            <i class="bi bi-phone me-2"></i>
                            设备管理
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('statistics') }}" class="btn btn-warning w-100">
                            <i class="bi bi-bar-chart me-2"></i>
                            查看统计
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 处理趋势图表
let processingChart;

function initProcessingChart() {
    const ctx = document.getElementById('processingChart').getContext('2d');
    
    processingChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: '处理数量',
                data: [],
                borderColor: 'rgb(37, 99, 235)',
                backgroundColor: 'rgba(37, 99, 235, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4
            }, {
                label: '成功数量',
                data: [],
                borderColor: 'rgb(5, 150, 105)',
                backgroundColor: 'rgba(5, 150, 105, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                },
                title: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    }
                },
                x: {
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    }
                }
            }
        }
    });
    
    // 加载初始数据
    loadChartData('7d');
}

function loadChartData(period) {
    // 模拟数据加载
    fetch(`/api/dashboard/chart-data?period=${period}`)
        .then(response => response.json())
        .then(data => {
            processingChart.data.labels = data.labels;
            processingChart.data.datasets[0].data = data.processed;
            processingChart.data.datasets[1].data = data.success;
            processingChart.update();
        })
        .catch(error => {
            console.error('加载图表数据失败:', error);
            // 使用模拟数据
            const mockData = generateMockChartData(period);
            processingChart.data.labels = mockData.labels;
            processingChart.data.datasets[0].data = mockData.processed;
            processingChart.data.datasets[1].data = mockData.success;
            processingChart.update();
        });
}

function generateMockChartData(period) {
    const days = period === '7d' ? 7 : period === '30d' ? 30 : 90;
    const labels = [];
    const processed = [];
    const success = [];
    
    for (let i = days - 1; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        labels.push(date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' }));
        
        const processedCount = Math.floor(Math.random() * 1000) + 500;
        const successCount = Math.floor(processedCount * (0.85 + Math.random() * 0.1));
        
        processed.push(processedCount);
        success.push(successCount);
    }
    
    return { labels, processed, success };
}

// 图表周期切换
document.querySelectorAll('input[name="chartPeriod"]').forEach(radio => {
    radio.addEventListener('change', function() {
        if (this.checked) {
            loadChartData(this.value);
        }
    });
});

// 刷新设备状态
function refreshDeviceStatus() {
    const button = event.target.closest('button');
    const originalContent = button.innerHTML;
    
    button.innerHTML = '<span class="loading"></span> 刷新中...';
    button.disabled = true;
    
    fetch('/api/devices/status')
        .then(response => response.json())
        .then(data => {
            // 更新设备状态显示
            updateDeviceStatusDisplay(data.devices);
        })
        .catch(error => {
            console.error('刷新设备状态失败:', error);
        })
        .finally(() => {
            button.innerHTML = originalContent;
            button.disabled = false;
        });
}

function updateDeviceStatusDisplay(devices) {
    // 更新设备状态显示逻辑
    console.log('更新设备状态:', devices);
}

// 实时数据更新
function updateDashboardStats() {
    fetch('/api/dashboard/stats')
        .then(response => response.json())
        .then(data => {
            document.getElementById('totalProcessed').textContent = data.total_processed || 0;
            document.getElementById('successRate').textContent = (data.success_rate || 0).toFixed(1) + '%';
            document.getElementById('activeTasks').textContent = data.active_tasks || 0;
            document.getElementById('onlineDevices').textContent = data.online_devices || 0;
        })
        .catch(error => {
            console.error('更新统计数据失败:', error);
        });
}

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    initProcessingChart();
    
    // 每30秒更新一次统计数据
    setInterval(updateDashboardStats, 30000);
});
</script>
{% endblock %}
