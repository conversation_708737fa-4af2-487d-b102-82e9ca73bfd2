<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>{% block title %}电话号码标记识别系统{% endblock %}</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="{{ url_for('static', path='images/favicon.png') }}">
    
    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="{{ url_for('static', path='css/admin.css') }}" rel="stylesheet">
    {% block extra_css %}{% endblock %}
    
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary-color: #64748b;
            --success-color: #059669;
            --warning-color: #d97706;
            --danger-color: #dc2626;
            --info-color: #0891b2;
            --light-color: #f8fafc;
            --dark-color: #1e293b;
            --border-color: #e2e8f0;
            --sidebar-width: 280px;
            --header-height: 70px;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: var(--light-color);
            color: var(--dark-color);
            line-height: 1.6;
        }
        
        /* 侧边栏样式 */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: var(--sidebar-width);
            height: 100vh;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            z-index: 1000;
            transition: transform 0.3s ease;
            overflow-y: auto;
        }
        
        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .sidebar-brand {
            display: flex;
            align-items: center;
            text-decoration: none;
            color: white;
        }
        
        .sidebar-brand i {
            font-size: 2rem;
            margin-right: 0.75rem;
        }
        
        .sidebar-brand-text {
            font-size: 1.25rem;
            font-weight: 600;
        }
        
        .sidebar-nav {
            padding: 1rem 0;
        }
        
        .nav-item {
            margin-bottom: 0.25rem;
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1.5rem;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }
        
        .nav-link:hover,
        .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
            border-left-color: white;
        }
        
        .nav-link i {
            width: 1.5rem;
            margin-right: 0.75rem;
            text-align: center;
        }
        
        /* 主内容区域 */
        .main-content {
            margin-left: var(--sidebar-width);
            min-height: 100vh;
        }
        
        /* 顶部导航栏 */
        .top-navbar {
            background: white;
            height: var(--header-height);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 2rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }
        
        .navbar-left {
            display: flex;
            align-items: center;
        }
        
        .sidebar-toggle {
            background: none;
            border: none;
            font-size: 1.25rem;
            color: var(--secondary-color);
            margin-right: 1rem;
            cursor: pointer;
            display: none;
        }
        
        .page-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--dark-color);
        }
        
        .navbar-right {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .user-dropdown {
            position: relative;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            cursor: pointer;
        }
        
        /* 内容区域 */
        .content-wrapper {
            padding: 2rem;
        }
        
        /* 卡片样式 */
        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            border: 1px solid var(--border-color);
            margin-bottom: 1.5rem;
        }
        
        .card-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
            background: var(--light-color);
            border-radius: 12px 12px 0 0;
        }
        
        .card-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin: 0;
            color: var(--dark-color);
        }
        
        .card-body {
            padding: 1.5rem;
        }
        
        /* 按钮样式 */
        .btn {
            border-radius: 8px;
            font-weight: 500;
            padding: 0.5rem 1rem;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-primary:hover {
            background-color: var(--primary-dark);
            border-color: var(--primary-dark);
        }
        
        /* 表格样式 */
        .table {
            margin-bottom: 0;
        }
        
        .table th {
            background-color: var(--light-color);
            border-bottom: 2px solid var(--border-color);
            font-weight: 600;
            color: var(--dark-color);
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .sidebar-toggle {
                display: block !important;
            }
            
            .content-wrapper {
                padding: 1rem;
            }
        }
        
        /* 加载动画 */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        /* 状态指示器 */
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }
        
        .status-online { background-color: var(--success-color); }
        .status-offline { background-color: var(--secondary-color); }
        .status-error { background-color: var(--danger-color); }
        .status-warning { background-color: var(--warning-color); }
    </style>
</head>
<body>
    <!-- 侧边栏 -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <a href="{{ url_for('dashboard') }}" class="sidebar-brand">
                <i class="bi bi-telephone-fill"></i>
                <div class="sidebar-brand-text">
                    <div>号码标记</div>
                    <small style="font-size: 0.8rem; opacity: 0.8;">识别系统</small>
                </div>
            </a>
        </div>
        
        <ul class="sidebar-nav">
            <li class="nav-item">
                <a href="{{ url_for('dashboard') }}" class="nav-link {% if request.endpoint == 'dashboard' %}active{% endif %}">
                    <i class="bi bi-speedometer2"></i>
                    <span>仪表板</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="{{ url_for('data_import') }}" class="nav-link {% if request.endpoint == 'data_import' %}active{% endif %}">
                    <i class="bi bi-upload"></i>
                    <span>数据导入</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="{{ url_for('data_export') }}" class="nav-link {% if request.endpoint == 'data_export' %}active{% endif %}">
                    <i class="bi bi-download"></i>
                    <span>数据导出</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="{{ url_for('device_manage') }}" class="nav-link {% if request.endpoint == 'device_manage' %}active{% endif %}">
                    <i class="bi bi-phone"></i>
                    <span>设备管理</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="{{ url_for('statistics') }}" class="nav-link {% if request.endpoint == 'statistics' %}active{% endif %}">
                    <i class="bi bi-bar-chart"></i>
                    <span>统计分析</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="{{ url_for('user_manage') }}" class="nav-link {% if request.endpoint == 'user_manage' %}active{% endif %}">
                    <i class="bi bi-people"></i>
                    <span>用户管理</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="{{ url_for('system_logs') }}" class="nav-link {% if request.endpoint == 'system_logs' %}active{% endif %}">
                    <i class="bi bi-journal-text"></i>
                    <span>系统日志</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="{{ url_for('system_settings') }}" class="nav-link {% if request.endpoint == 'system_settings' %}active{% endif %}">
                    <i class="bi bi-gear"></i>
                    <span>系统设置</span>
                </a>
            </li>
        </ul>
    </nav>
    
    <!-- 主内容区域 -->
    <div class="main-content">
        <!-- 顶部导航栏 -->
        <header class="top-navbar">
            <div class="navbar-left">
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="bi bi-list"></i>
                </button>
                <h1 class="page-title">{% block page_title %}仪表板{% endblock %}</h1>
            </div>
            
            <div class="navbar-right">
                <!-- 通知 -->
                <div class="dropdown">
                    <button class="btn btn-link text-secondary" type="button" data-bs-toggle="dropdown">
                        <i class="bi bi-bell" style="font-size: 1.25rem;"></i>
                        <span class="badge bg-danger position-absolute top-0 start-100 translate-middle rounded-pill" style="font-size: 0.6rem;">3</span>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><h6 class="dropdown-header">通知</h6></li>
                        <li><a class="dropdown-item" href="#">新的处理任务完成</a></li>
                        <li><a class="dropdown-item" href="#">设备连接异常</a></li>
                        <li><a class="dropdown-item" href="#">系统更新可用</a></li>
                    </ul>
                </div>
                
                <!-- 用户菜单 -->
                <div class="dropdown user-dropdown">
                    <div class="user-avatar" data-bs-toggle="dropdown">
                        {{ current_user.username[0].upper() if current_user else 'A' }}
                    </div>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><h6 class="dropdown-header">{{ current_user.username if current_user else 'Admin' }}</h6></li>
                        <li><a class="dropdown-item" href="#"><i class="bi bi-person me-2"></i>个人资料</a></li>
                        <li><a class="dropdown-item" href="#"><i class="bi bi-gear me-2"></i>设置</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="{{ url_for('logout') }}"><i class="bi bi-box-arrow-right me-2"></i>退出登录</a></li>
                    </ul>
                </div>
            </div>
        </header>
        
        <!-- 内容区域 -->
        <main class="content-wrapper">
            {% block content %}{% endblock %}
        </main>
    </div>
    
    <!-- JavaScript Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="{{ url_for('static', path='js/admin.js') }}"></script>
    {% block extra_js %}{% endblock %}
    
    <script>
        // 侧边栏切换
        document.getElementById('sidebarToggle').addEventListener('click', function() {
            document.getElementById('sidebar').classList.toggle('show');
        });
        
        // 点击外部关闭侧边栏（移动端）
        document.addEventListener('click', function(e) {
            const sidebar = document.getElementById('sidebar');
            const toggle = document.getElementById('sidebarToggle');
            
            if (window.innerWidth <= 768 && 
                !sidebar.contains(e.target) && 
                !toggle.contains(e.target) && 
                sidebar.classList.contains('show')) {
                sidebar.classList.remove('show');
            }
        });
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化工具提示
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
            
            // 初始化弹出框
            var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
            var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
                return new bootstrap.Popover(popoverTriggerEl);
            });
        });
    </script>
</body>
</html>
