{% extends "base.html" %}

{% block title %}数据导入 - 电话号码标记识别系统{% endblock %}
{% block page_title %}数据导入{% endblock %}

{% block extra_css %}
<style>
    .upload-area {
        border: 2px dashed var(--border-color);
        border-radius: 12px;
        padding: 3rem;
        text-align: center;
        background-color: var(--light-color);
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .upload-area:hover,
    .upload-area.dragover {
        border-color: var(--primary-color);
        background-color: rgba(37, 99, 235, 0.05);
    }
    
    .upload-icon {
        font-size: 4rem;
        color: var(--secondary-color);
        margin-bottom: 1rem;
    }
    
    .upload-area.dragover .upload-icon {
        color: var(--primary-color);
    }
    
    .file-info {
        background-color: white;
        border: 1px solid var(--border-color);
        border-radius: 8px;
        padding: 1rem;
        margin-top: 1rem;
        display: none;
    }
    
    .file-info.show {
        display: block;
    }
    
    .progress-container {
        margin-top: 1rem;
        display: none;
    }
    
    .progress-container.show {
        display: block;
    }
    
    .task-item {
        background-color: white;
        border: 1px solid var(--border-color);
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
    }
    
    .task-item:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
    
    .task-header {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 0.5rem;
    }
    
    .task-title {
        font-weight: 600;
        margin: 0;
        flex: 1;
    }
    
    .task-status {
        font-size: 0.85rem;
    }
    
    .task-progress {
        margin: 0.75rem 0;
    }
    
    .task-stats {
        display: flex;
        gap: 1rem;
        font-size: 0.85rem;
        color: var(--secondary-color);
    }
    
    .task-actions {
        margin-top: 0.75rem;
        display: flex;
        gap: 0.5rem;
    }
    
    .format-option {
        border: 1px solid var(--border-color);
        border-radius: 8px;
        padding: 1rem;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .format-option:hover,
    .format-option.selected {
        border-color: var(--primary-color);
        background-color: rgba(37, 99, 235, 0.05);
    }
    
    .format-icon {
        font-size: 2rem;
        color: var(--primary-color);
        margin-bottom: 0.5rem;
    }
    
    .device-check {
        background-color: var(--light-color);
        border: 1px solid var(--border-color);
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1rem;
    }
    
    .device-check.error {
        background-color: rgba(220, 38, 38, 0.05);
        border-color: var(--danger-color);
    }
    
    .device-check.success {
        background-color: rgba(5, 150, 105, 0.05);
        border-color: var(--success-color);
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <!-- 文件上传区域 -->
    <div class="col-xl-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">上传数据文件</h5>
            </div>
            <div class="card-body">
                <!-- 设备连接检查 -->
                <div id="deviceCheck" class="device-check">
                    <div class="d-flex align-items-center">
                        <div class="spinner-border spinner-border-sm me-2" role="status">
                            <span class="visually-hidden">检查中...</span>
                        </div>
                        <span>正在检查设备连接状态...</span>
                    </div>
                </div>
                
                <!-- 文件格式选择 -->
                <div class="mb-4">
                    <label class="form-label">选择文件格式</label>
                    <div class="row">
                        <div class="col-md-3 mb-2">
                            <div class="format-option" data-format="csv">
                                <div class="format-icon">
                                    <i class="bi bi-filetype-csv"></i>
                                </div>
                                <div class="fw-bold">CSV</div>
                                <small class="text-muted">逗号分隔值</small>
                            </div>
                        </div>
                        <div class="col-md-3 mb-2">
                            <div class="format-option" data-format="excel">
                                <div class="format-icon">
                                    <i class="bi bi-file-earmark-excel"></i>
                                </div>
                                <div class="fw-bold">Excel</div>
                                <small class="text-muted">Excel工作表</small>
                            </div>
                        </div>
                        <div class="col-md-3 mb-2">
                            <div class="format-option" data-format="txt">
                                <div class="format-icon">
                                    <i class="bi bi-file-earmark-text"></i>
                                </div>
                                <div class="fw-bold">TXT</div>
                                <small class="text-muted">纯文本文件</small>
                            </div>
                        </div>
                        <div class="col-md-3 mb-2">
                            <div class="format-option" data-format="json">
                                <div class="format-icon">
                                    <i class="bi bi-file-earmark-code"></i>
                                </div>
                                <div class="fw-bold">JSON</div>
                                <small class="text-muted">JSON格式</small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 上传区域 -->
                <div class="upload-area" id="uploadArea">
                    <div class="upload-icon">
                        <i class="bi bi-cloud-upload"></i>
                    </div>
                    <h5>拖拽文件到此处或点击选择文件</h5>
                    <p class="text-muted mb-3">支持 CSV、Excel、TXT、JSON 格式，最大文件大小 100MB</p>
                    <button type="button" class="btn btn-primary" id="selectFileBtn">
                        <i class="bi bi-folder2-open me-2"></i>选择文件
                    </button>
                    <input type="file" id="fileInput" class="d-none" accept=".csv,.xlsx,.xls,.txt,.json">
                </div>
                
                <!-- 文件信息 -->
                <div class="file-info" id="fileInfo">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-file-earmark text-primary me-2" style="font-size: 1.5rem;"></i>
                                <div>
                                    <div class="fw-bold" id="fileName"></div>
                                    <small class="text-muted" id="fileSize"></small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 text-end">
                            <button type="button" class="btn btn-outline-danger btn-sm" id="removeFileBtn">
                                <i class="bi bi-trash"></i> 移除
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 处理选项 -->
                <div class="mt-4">
                    <h6>处理选项</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="enableLocation" checked>
                                <label class="form-check-label" for="enableLocation">
                                    启用归属地识别
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="enableNLP" checked>
                                <label class="form-check-label" for="enableNLP">
                                    启用NLP文本分析
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="batchSize" class="form-label">批处理大小</label>
                                <select class="form-select" id="batchSize">
                                    <option value="50">50</option>
                                    <option value="100" selected>100</option>
                                    <option value="200">200</option>
                                    <option value="500">500</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 任务名称 -->
                <div class="mb-3">
                    <label for="taskName" class="form-label">任务名称</label>
                    <input type="text" class="form-control" id="taskName" placeholder="请输入任务名称">
                </div>
                
                <!-- 开始处理按钮 -->
                <div class="text-center">
                    <button type="button" class="btn btn-primary btn-lg" id="startProcessBtn" disabled>
                        <i class="bi bi-play-circle me-2"></i>开始处理
                    </button>
                </div>
                
                <!-- 进度显示 -->
                <div class="progress-container" id="progressContainer">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>处理进度</span>
                        <span id="progressText">0%</span>
                    </div>
                    <div class="progress mb-2">
                        <div class="progress-bar" id="progressBar" role="progressbar" style="width: 0%"></div>
                    </div>
                    <div class="row text-center">
                        <div class="col-md-3">
                            <small class="text-muted">总数量</small>
                            <div class="fw-bold" id="totalCount">0</div>
                        </div>
                        <div class="col-md-3">
                            <small class="text-muted">已处理</small>
                            <div class="fw-bold" id="processedCount">0</div>
                        </div>
                        <div class="col-md-3">
                            <small class="text-muted">成功</small>
                            <div class="fw-bold text-success" id="successCount">0</div>
                        </div>
                        <div class="col-md-3">
                            <small class="text-muted">失败</small>
                            <div class="fw-bold text-danger" id="failedCount">0</div>
                        </div>
                    </div>
                    <div class="text-center mt-2">
                        <small class="text-muted" id="estimatedTime">预计剩余时间: --</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 任务列表 -->
    <div class="col-xl-4 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title">处理任务</h5>
                <button class="btn btn-outline-primary btn-sm" onclick="refreshTasks()">
                    <i class="bi bi-arrow-clockwise"></i>
                </button>
            </div>
            <div class="card-body">
                <div id="taskList">
                    <!-- 任务列表将在这里动态加载 -->
                </div>
                
                <div class="text-center">
                    <a href="{{ url_for('data_export') }}" class="btn btn-outline-success btn-sm">
                        <i class="bi bi-download me-1"></i>查看导出
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 使用说明 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">使用说明</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="bi bi-info-circle text-info me-2"></i>文件格式要求</h6>
                        <ul class="list-unstyled">
                            <li><strong>CSV:</strong> 第一列为电话号码，支持标题行</li>
                            <li><strong>Excel:</strong> 第一列为电话号码，支持.xlsx和.xls格式</li>
                            <li><strong>TXT:</strong> 每行一个电话号码</li>
                            <li><strong>JSON:</strong> 数组格式或包含phone字段的对象数组</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="bi bi-lightbulb text-warning me-2"></i>处理说明</h6>
                        <ul class="list-unstyled">
                            <li>• 系统会自动验证电话号码格式</li>
                            <li>• 支持手机号码和固定电话</li>
                            <li>• 处理过程中可以随时取消</li>
                            <li>• 结果支持多种格式导出</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let selectedFormat = 'csv';
let selectedFile = null;
let currentTaskId = null;
let progressInterval = null;

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeUpload();
    checkDeviceConnection();
    loadTasks();
    
    // 每5秒刷新一次任务列表
    setInterval(loadTasks, 5000);
});

// 初始化上传功能
function initializeUpload() {
    const uploadArea = document.getElementById('uploadArea');
    const fileInput = document.getElementById('fileInput');
    const selectFileBtn = document.getElementById('selectFileBtn');
    
    // 格式选择
    document.querySelectorAll('.format-option').forEach(option => {
        option.addEventListener('click', function() {
            document.querySelectorAll('.format-option').forEach(o => o.classList.remove('selected'));
            this.classList.add('selected');
            selectedFormat = this.dataset.format;
            updateFileInputAccept();
        });
    });
    
    // 默认选择CSV
    document.querySelector('.format-option[data-format="csv"]').classList.add('selected');
    
    // 点击选择文件
    selectFileBtn.addEventListener('click', () => fileInput.click());
    uploadArea.addEventListener('click', () => fileInput.click());
    
    // 文件选择
    fileInput.addEventListener('change', handleFileSelect);
    
    // 拖拽上传
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('dragleave', handleDragLeave);
    uploadArea.addEventListener('drop', handleFileDrop);
    
    // 移除文件
    document.getElementById('removeFileBtn').addEventListener('click', removeFile);
    
    // 开始处理
    document.getElementById('startProcessBtn').addEventListener('click', startProcessing);
}

function updateFileInputAccept() {
    const fileInput = document.getElementById('fileInput');
    const acceptMap = {
        'csv': '.csv',
        'excel': '.xlsx,.xls',
        'txt': '.txt',
        'json': '.json'
    };
    fileInput.accept = acceptMap[selectedFormat] || '';
}

function handleDragOver(e) {
    e.preventDefault();
    e.currentTarget.classList.add('dragover');
}

function handleDragLeave(e) {
    e.preventDefault();
    e.currentTarget.classList.remove('dragover');
}

function handleFileDrop(e) {
    e.preventDefault();
    e.currentTarget.classList.remove('dragover');
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        handleFile(files[0]);
    }
}

function handleFileSelect(e) {
    const files = e.target.files;
    if (files.length > 0) {
        handleFile(files[0]);
    }
}

function handleFile(file) {
    // 验证文件大小
    const maxSize = 100 * 1024 * 1024; // 100MB
    if (file.size > maxSize) {
        alert('文件大小超过100MB限制');
        return;
    }
    
    // 验证文件格式
    const validFormats = {
        'csv': ['.csv'],
        'excel': ['.xlsx', '.xls'],
        'txt': ['.txt'],
        'json': ['.json']
    };
    
    const fileExt = '.' + file.name.split('.').pop().toLowerCase();
    if (!validFormats[selectedFormat].includes(fileExt)) {
        alert(`请选择${selectedFormat.toUpperCase()}格式的文件`);
        return;
    }
    
    selectedFile = file;
    showFileInfo(file);
    updateStartButton();
}

function showFileInfo(file) {
    document.getElementById('fileName').textContent = file.name;
    document.getElementById('fileSize').textContent = formatFileSize(file.size);
    document.getElementById('fileInfo').classList.add('show');
    
    // 自动生成任务名称
    const taskName = `${file.name.split('.')[0]}_${new Date().toLocaleString('zh-CN')}`;
    document.getElementById('taskName').value = taskName;
}

function removeFile() {
    selectedFile = null;
    document.getElementById('fileInfo').classList.remove('show');
    document.getElementById('fileInput').value = '';
    document.getElementById('taskName').value = '';
    updateStartButton();
}

function updateStartButton() {
    const startBtn = document.getElementById('startProcessBtn');
    const hasFile = selectedFile !== null;
    const hasTaskName = document.getElementById('taskName').value.trim() !== '';
    
    startBtn.disabled = !hasFile || !hasTaskName;
}

// 检查设备连接
function checkDeviceConnection() {
    const deviceCheck = document.getElementById('deviceCheck');
    
    fetch('/api/devices/check-connection')
        .then(response => response.json())
        .then(data => {
            if (data.connected) {
                deviceCheck.className = 'device-check success';
                deviceCheck.innerHTML = `
                    <div class="d-flex align-items-center">
                        <i class="bi bi-check-circle text-success me-2"></i>
                        <span>设备连接正常，可以开始处理数据</span>
                    </div>
                `;
            } else {
                deviceCheck.className = 'device-check error';
                deviceCheck.innerHTML = `
                    <div class="d-flex align-items-center">
                        <i class="bi bi-exclamation-triangle text-danger me-2"></i>
                        <span>设备连接异常，请检查设备状态后重试</span>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('检查设备连接失败:', error);
            deviceCheck.className = 'device-check error';
            deviceCheck.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="bi bi-exclamation-triangle text-danger me-2"></i>
                    <span>无法检查设备连接状态</span>
                </div>
            `;
        });
}

// 开始处理
function startProcessing() {
    if (!selectedFile) {
        alert('请先选择文件');
        return;
    }
    
    const taskName = document.getElementById('taskName').value.trim();
    if (!taskName) {
        alert('请输入任务名称');
        return;
    }
    
    const formData = new FormData();
    formData.append('file', selectedFile);
    formData.append('task_name', taskName);
    formData.append('file_format', selectedFormat);
    formData.append('enable_location', document.getElementById('enableLocation').checked);
    formData.append('enable_nlp', document.getElementById('enableNLP').checked);
    formData.append('batch_size', document.getElementById('batchSize').value);
    
    // 显示进度容器
    document.getElementById('progressContainer').classList.add('show');
    document.getElementById('startProcessBtn').disabled = true;
    
    fetch('/api/v1/upload', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            currentTaskId = data.task_id;
            startProgressMonitoring();
            loadTasks(); // 刷新任务列表
        } else {
            alert('上传失败: ' + data.message);
            document.getElementById('progressContainer').classList.remove('show');
            document.getElementById('startProcessBtn').disabled = false;
        }
    })
    .catch(error => {
        console.error('上传失败:', error);
        alert('上传失败，请重试');
        document.getElementById('progressContainer').classList.remove('show');
        document.getElementById('startProcessBtn').disabled = false;
    });
}

// 监控处理进度
function startProgressMonitoring() {
    if (!currentTaskId) return;
    
    progressInterval = setInterval(() => {
        fetch(`/api/v1/tasks/${currentTaskId}/progress`)
            .then(response => response.json())
            .then(data => {
                updateProgress(data);
                
                if (data.status === 'completed' || data.status === 'failed' || data.status === 'cancelled') {
                    clearInterval(progressInterval);
                    document.getElementById('startProcessBtn').disabled = false;
                    loadTasks(); // 刷新任务列表
                }
            })
            .catch(error => {
                console.error('获取进度失败:', error);
            });
    }, 1000);
}

function updateProgress(data) {
    const progressBar = document.getElementById('progressBar');
    const progressText = document.getElementById('progressText');
    
    progressBar.style.width = data.progress_percentage + '%';
    progressText.textContent = data.progress_percentage.toFixed(1) + '%';
    
    document.getElementById('totalCount').textContent = data.total_count;
    document.getElementById('processedCount').textContent = data.processed_count;
    document.getElementById('successCount').textContent = data.success_count;
    document.getElementById('failedCount').textContent = data.failed_count;
    
    if (data.estimated_remaining_time) {
        const minutes = Math.floor(data.estimated_remaining_time / 60);
        const seconds = Math.floor(data.estimated_remaining_time % 60);
        document.getElementById('estimatedTime').textContent = `预计剩余时间: ${minutes}分${seconds}秒`;
    }
}

// 加载任务列表
function loadTasks() {
    fetch('/api/v1/tasks')
        .then(response => response.json())
        .then(data => {
            displayTasks(data.tasks || []);
        })
        .catch(error => {
            console.error('加载任务列表失败:', error);
        });
}

function displayTasks(tasks) {
    const taskList = document.getElementById('taskList');
    
    if (tasks.length === 0) {
        taskList.innerHTML = '<p class="text-muted text-center">暂无处理任务</p>';
        return;
    }
    
    taskList.innerHTML = tasks.slice(0, 5).map(task => `
        <div class="task-item">
            <div class="task-header">
                <h6 class="task-title">${task.task_name}</h6>
                <span class="badge bg-${getStatusColor(task.status)} task-status">
                    ${getStatusText(task.status)}
                </span>
            </div>
            
            ${task.status === 'running' ? `
                <div class="task-progress">
                    <div class="progress">
                        <div class="progress-bar" style="width: ${task.progress_percentage}%"></div>
                    </div>
                </div>
            ` : ''}
            
            <div class="task-stats">
                <span>总数: ${task.total_count}</span>
                <span>成功: ${task.success_count}</span>
                <span>失败: ${task.failed_count}</span>
            </div>
            
            <div class="task-actions">
                ${task.status === 'running' ? `
                    <button class="btn btn-outline-danger btn-sm" onclick="cancelTask('${task.task_id}')">
                        <i class="bi bi-stop-circle"></i> 取消
                    </button>
                ` : ''}
                ${task.status === 'completed' ? `
                    <button class="btn btn-outline-success btn-sm" onclick="exportTask('${task.task_id}')">
                        <i class="bi bi-download"></i> 导出
                    </button>
                ` : ''}
            </div>
        </div>
    `).join('');
}

function getStatusColor(status) {
    const colors = {
        'pending': 'secondary',
        'running': 'primary',
        'completed': 'success',
        'failed': 'danger',
        'cancelled': 'warning'
    };
    return colors[status] || 'secondary';
}

function getStatusText(status) {
    const texts = {
        'pending': '等待中',
        'running': '处理中',
        'completed': '已完成',
        'failed': '失败',
        'cancelled': '已取消'
    };
    return texts[status] || status;
}

function cancelTask(taskId) {
    if (confirm('确定要取消这个任务吗？')) {
        fetch(`/api/v1/tasks/${taskId}/cancel`, { method: 'POST' })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    loadTasks();
                } else {
                    alert('取消任务失败');
                }
            })
            .catch(error => {
                console.error('取消任务失败:', error);
                alert('取消任务失败');
            });
    }
}

function exportTask(taskId) {
    window.open(`/api/v1/tasks/${taskId}/export?format=excel`, '_blank');
}

function refreshTasks() {
    loadTasks();
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 监听任务名称输入
document.getElementById('taskName').addEventListener('input', updateStartButton);
</script>
{% endblock %}
