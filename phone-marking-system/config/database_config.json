{"databases": {"main": {"type": "sqlite", "path": "./data/databases/main.db", "config": {"timeout": 30, "check_same_thread": false, "isolation_level": null, "pragma": {"journal_mode": "WAL", "synchronous": "NORMAL", "cache_size": -64000, "foreign_keys": "ON", "temp_store": "MEMORY"}}}, "cache": {"type": "sqlite", "path": "./data/databases/cache.db", "config": {"timeout": 10, "check_same_thread": false, "pragma": {"journal_mode": "MEMORY", "synchronous": "OFF", "cache_size": -32000, "temp_store": "MEMORY"}}}, "logs": {"type": "sqlite", "path": "./data/databases/logs.db", "config": {"timeout": 20, "check_same_thread": false, "pragma": {"journal_mode": "WAL", "synchronous": "NORMAL", "cache_size": -16000}}}, "remote": {"type": "mysql", "enabled": false, "config": {"host": "localhost", "port": 3306, "user": "root", "password": "", "database": "phone_marking_system", "charset": "utf8mb4", "autocommit": true, "pool_size": 10, "max_overflow": 20, "pool_timeout": 30, "pool_recycle": 3600}}}, "sharding": {"enabled": true, "strategy": "functional", "auto_create_shards": true, "shard_configs": {"users": {"table_name": "users", "shard_key": "user_id", "shard_count": 4}, "phone_data": {"table_name": "phone_data", "shard_key": "phone_number", "shard_count": 8}, "processing_results": {"table_name": "processing_results", "shard_key": "task_id", "shard_count": 4}}}, "backup": {"enabled": true, "interval": 86400, "retention_days": 30, "backup_dir": "./data/backups", "compress": true}, "sync": {"enabled": false, "remote_database": "remote", "sync_interval": 3600, "sync_tables": ["phone_data", "processing_results"], "conflict_resolution": "local_wins"}}