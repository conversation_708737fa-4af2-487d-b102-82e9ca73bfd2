{"version": 1, "disable_existing_loggers": false, "formatters": {"standard": {"format": "%(asctime)s [%(levelname)s] %(name)s: %(message)s", "datefmt": "%Y-%m-%d %H:%M:%S"}, "detailed": {"format": "%(asctime)s [%(levelname)s] %(name)s [%(filename)s:%(lineno)d] %(funcName)s(): %(message)s", "datefmt": "%Y-%m-%d %H:%M:%S"}, "json": {"format": "{\"timestamp\": \"%(asctime)s\", \"level\": \"%(levelname)s\", \"logger\": \"%(name)s\", \"message\": \"%(message)s\", \"module\": \"%(module)s\", \"function\": \"%(funcName)s\", \"line\": %(lineno)d}", "datefmt": "%Y-%m-%d %H:%M:%S"}}, "handlers": {"console": {"class": "logging.StreamHandler", "level": "INFO", "formatter": "standard", "stream": "ext://sys.stdout"}, "system_file": {"class": "logging.handlers.RotatingFileHandler", "level": "INFO", "formatter": "detailed", "filename": "./logs/system/system.log", "maxBytes": 10485760, "backupCount": 10, "encoding": "utf-8"}, "error_file": {"class": "logging.handlers.RotatingFileHandler", "level": "ERROR", "formatter": "detailed", "filename": "./logs/system/error.log", "maxBytes": 10485760, "backupCount": 10, "encoding": "utf-8"}, "service_file": {"class": "logging.handlers.RotatingFileHandler", "level": "DEBUG", "formatter": "json", "filename": "./logs/services/services.log", "maxBytes": 10485760, "backupCount": 20, "encoding": "utf-8"}, "access_file": {"class": "logging.handlers.RotatingFileHandler", "level": "INFO", "formatter": "standard", "filename": "./logs/access/access.log", "maxBytes": 10485760, "backupCount": 30, "encoding": "utf-8"}}, "loggers": {"": {"level": "INFO", "handlers": ["console", "system_file", "error_file"], "propagate": false}, "services": {"level": "DEBUG", "handlers": ["service_file"], "propagate": false}, "access": {"level": "INFO", "handlers": ["access_file"], "propagate": false}, "uvicorn": {"level": "INFO", "handlers": ["console", "access_file"], "propagate": false}, "uvicorn.error": {"level": "INFO", "handlers": ["error_file"], "propagate": false}, "uvicorn.access": {"level": "INFO", "handlers": ["access_file"], "propagate": false}}, "root": {"level": "INFO", "handlers": ["console", "system_file"]}}