# 电话号码标记识别系统

[![Python Version](https://img.shields.io/badge/python-3.8%2B-blue.svg)](https://python.org)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Build Status](https://img.shields.io/badge/build-passing-brightgreen.svg)](https://github.com)

一个基于微服务架构的企业级电话号码标记识别管理系统，支持大规模号码批量处理、智能文本分析、完整的权限管理和实时监控。

## 🌟 功能特性

### 📊 核心功能
- **大规模批量处理**: 支持万级别电话号码的并发处理
- **多格式支持**: CSV、Excel、TXT、JSON等多种文件格式
- **智能识别**: 归属地查询 + NLP文本分析
- **实时进度**: 处理进度实时监控，支持断点续传
- **结果导出**: 多种格式的结果导出功能

### 🔐 权限管理
- **RBAC权限控制**: 基于角色的访问控制
- **用户管理**: 完整的用户生命周期管理
- **操作审计**: 详细的操作日志和审计追踪
- **JWT认证**: 安全的用户认证机制

### 📱 设备管理
- **设备注册**: 支持Android/iOS设备管理
- **连接监控**: 实时设备连接状态检查
- **状态同步**: 设备状态自动同步更新

### 📈 监控分析
- **实时监控**: 服务健康状态监控
- **性能分析**: 详细的性能统计和分析
- **智能告警**: 多级告警系统
- **数据统计**: 丰富的数据统计和可视化

### 🚀 技术特性
- **微服务架构**: 5个独立微服务，可独立部署
- **高性能缓存**: 多层缓存架构，性能提升>1000倍
- **分布式追踪**: 轻量级跨服务调用链追踪
- **无外部依赖**: 保守设计，可选择性启用功能

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web管理系统    │    │   批量处理服务   │    │   API网关       │
│   (8080)        │    │   (8003)        │    │   (8000)        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
         ┌─────────────────┐    ┌─────────────────┐
         │   归属地服务     │    │   NLP分析服务   │
         │   (8001)        │    │   (8002)        │
         └─────────────────┘    └─────────────────┘
```

## 📁 项目结构

```
phone-marking-system/
├── config/                     # 配置文件
│   ├── system_config.json      # 系统主配置
│   ├── services_config.json    # 微服务配置
│   ├── database_config.json    # 数据库配置
│   └── logging_config.json     # 日志配置
├── src/                        # 源代码
│   ├── main.py                 # 主程序入口
│   ├── core/                   # 核心模块
│   │   ├── database/           # 数据库模块
│   │   ├── cache/              # 缓存模块
│   │   ├── monitoring/         # 监控模块
│   │   └── utils/              # 工具模块
│   ├── services/               # 微服务
│   │   ├── location/           # 归属地服务
│   │   ├── nlp/                # NLP服务
│   │   ├── gateway/            # API网关
│   │   ├── batch/              # 批量处理
│   │   └── admin/              # 管理服务
│   └── web/                    # Web前端
│       ├── static/             # 静态资源
│       ├── templates/          # HTML模板
│       └── api/                # API接口
├── data/                       # 数据目录
├── logs/                       # 日志目录
├── tests/                      # 测试目录
└── docs/                       # 文档目录
```

## 🚀 快速开始

### 环境要求

- Python 3.8+
- 内存: 4GB+
- 磁盘: 10GB+
- 操作系统: Windows/Linux/macOS

### 安装步骤

1. **克隆项目**
```bash
git clone https://github.com/your-repo/phone-marking-system.git
cd phone-marking-system
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **配置系统**
```bash
# 复制配置文件模板
cp config/system_config.json.example config/system_config.json

# 根据需要修改配置
vim config/system_config.json
```

4. **启动系统**
```bash
# 启动所有服务
python src/main.py start

# 或者交互模式
python src/main.py interactive
```

5. **访问系统**
- Web管理界面: http://127.0.0.1:8080
- API网关: http://127.0.0.1:8000
- API文档: http://127.0.0.1:8000/docs

### 默认账户

- 用户名: `admin`
- 密码: `admin123`

## 📖 使用指南

### 数据导入

1. 登录Web管理系统
2. 进入"数据导入"页面
3. 选择文件格式（CSV/Excel/TXT/JSON）
4. 上传数据文件
5. 配置处理选项
6. 开始批量处理

### 文件格式要求

- **CSV**: 第一列为电话号码，支持标题行
- **Excel**: 第一列为电话号码，支持.xlsx和.xls格式
- **TXT**: 每行一个电话号码
- **JSON**: 数组格式或包含phone字段的对象数组

### API使用

```python
import requests

# 上传文件
files = {'file': open('phones.csv', 'rb')}
data = {
    'task_name': '测试任务',
    'file_format': 'csv'
}
response = requests.post('http://127.0.0.1:8003/api/v1/upload', 
                        files=files, data=data)

# 查询进度
task_id = response.json()['task_id']
progress = requests.get(f'http://127.0.0.1:8003/api/v1/tasks/{task_id}/progress')

# 导出结果
result = requests.get(f'http://127.0.0.1:8003/api/v1/tasks/{task_id}/export?format=excel')
```

## 🔧 配置说明

### 系统配置 (system_config.json)

```json
{
  "system": {
    "name": "电话号码标记识别系统",
    "debug": true,
    "environment": "development"
  },
  "server": {
    "host": "127.0.0.1",
    "port": 8080,
    "workers": 4
  },
  "security": {
    "jwt_secret": "your-secret-key",
    "jwt_expiration": 86400
  }
}
```

### 服务配置 (services_config.json)

```json
{
  "services": {
    "location_service": {
      "enabled": true,
      "port": 8001,
      "config": {
        "cache_enabled": true,
        "batch_size": 100
      }
    }
  }
}
```

## 📊 性能指标

| 指标 | 数值 | 说明 |
|------|------|------|
| 并发处理能力 | 1000+/秒 | 支持高并发号码处理 |
| 文件大小支持 | 100MB | 单文件最大支持 |
| 处理准确率 | >95% | 号码识别准确率 |
| 响应时间 | <100ms | 平均API响应时间 |
| 缓存命中率 | >80% | 缓存性能优化 |
| 系统可用性 | >99.9% | 高可用性设计 |

## 🧪 测试

```bash
# 运行单元测试
python -m pytest tests/unit/

# 运行集成测试
python -m pytest tests/integration/

# 运行性能测试
python -m pytest tests/performance/

# 生成测试报告
python -m pytest --cov=src --cov-report=html
```

## 📚 API文档

系统启动后，可以通过以下地址访问API文档：

- Swagger UI: http://127.0.0.1:8000/docs
- ReDoc: http://127.0.0.1:8000/redoc

### 主要API端点

- `POST /api/v1/upload` - 上传文件并创建处理任务
- `GET /api/v1/tasks/{task_id}/progress` - 查询任务进度
- `GET /api/v1/tasks/{task_id}/export` - 导出处理结果
- `POST /api/v1/tasks/{task_id}/cancel` - 取消处理任务

## 🔍 监控和日志

### 日志文件

- 系统日志: `logs/system/system.log`
- 服务日志: `logs/services/services.log`
- 访问日志: `logs/access/access.log`
- 错误日志: `logs/system/error.log`

### 监控指标

系统提供丰富的监控指标：

- 服务健康状态
- 处理性能统计
- 缓存命中率
- 错误率和响应时间
- 设备连接状态

## 🚀 部署

### Docker部署

```bash
# 构建镜像
docker build -t phone-marking-system .

# 运行容器
docker run -d -p 8080:8080 phone-marking-system

# 使用docker-compose
docker-compose up -d
```

### 生产环境部署

1. 配置反向代理（Nginx）
2. 设置SSL证书
3. 配置数据库连接
4. 启用日志轮转
5. 设置监控告警

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

如果您在使用过程中遇到问题，可以通过以下方式获取支持：

- 📧 邮箱: <EMAIL>
- 💬 QQ群: 123456789
- 📱 微信: phone-marking-support
- 🐛 问题反馈: [GitHub Issues](https://github.com/your-repo/phone-marking-system/issues)

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和用户。

---

**电话号码标记识别系统** - 让号码识别更智能、更高效！
