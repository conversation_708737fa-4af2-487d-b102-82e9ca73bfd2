#!/bin/bash

# 电话号码标记识别系统启动脚本
# Phone Marking System Start Script

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 显示横幅
show_banner() {
    echo -e "${BLUE}"
    cat << "EOF"
╔══════════════════════════════════════════════════════════════════════════════╗
║                          电话号码标记识别系统                                ║
║                     Phone Number Marking System                             ║
║                                                                              ║
║  版本: v1.0.0                                                               ║
║  架构: 微服务架构                                                            ║
║  技术栈: Python + FastAPI + SQLite + Vue.js                                ║
╚══════════════════════════════════════════════════════════════════════════════╝
EOF
    echo -e "${NC}"
}

# 检查Python环境
check_python() {
    log_info "检查Python环境..."
    
    if ! command -v python3 &> /dev/null; then
        log_error "Python3 未安装，请先安装Python 3.8+"
        exit 1
    fi
    
    PYTHON_VERSION=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
    log_info "Python版本: $PYTHON_VERSION"
    
    # 检查Python版本是否满足要求
    if python3 -c 'import sys; exit(0 if sys.version_info >= (3, 8) else 1)'; then
        log_info "✓ Python版本满足要求"
    else
        log_error "Python版本过低，需要Python 3.8+"
        exit 1
    fi
}

# 检查虚拟环境
check_venv() {
    log_info "检查虚拟环境..."
    
    if [[ "$VIRTUAL_ENV" != "" ]]; then
        log_info "✓ 已在虚拟环境中: $VIRTUAL_ENV"
    else
        log_warn "未检测到虚拟环境"
        
        if [[ -d "venv" ]]; then
            log_info "发现虚拟环境目录，正在激活..."
            source venv/bin/activate
            log_info "✓ 虚拟环境已激活"
        else
            log_info "创建虚拟环境..."
            python3 -m venv venv
            source venv/bin/activate
            log_info "✓ 虚拟环境已创建并激活"
        fi
    fi
}

# 安装依赖
install_dependencies() {
    log_info "检查并安装依赖..."
    
    if [[ -f "requirements.txt" ]]; then
        log_info "安装Python依赖包..."
        pip install --upgrade pip
        pip install -r requirements.txt
        log_info "✓ 依赖安装完成"
    else
        log_warn "未找到requirements.txt文件"
    fi
}

# 检查配置文件
check_config() {
    log_info "检查配置文件..."
    
    CONFIG_FILES=(
        "config/system_config.json"
        "config/services_config.json"
        "config/database_config.json"
        "config/logging_config.json"
    )
    
    for config_file in "${CONFIG_FILES[@]}"; do
        if [[ -f "$config_file" ]]; then
            log_info "✓ 配置文件存在: $config_file"
        else
            log_error "配置文件不存在: $config_file"
            exit 1
        fi
    done
}

# 创建必要目录
create_directories() {
    log_info "创建必要目录..."
    
    DIRECTORIES=(
        "data/databases"
        "data/uploads"
        "data/exports"
        "data/temp"
        "logs/system"
        "logs/services"
        "logs/access"
    )
    
    for dir in "${DIRECTORIES[@]}"; do
        if [[ ! -d "$dir" ]]; then
            mkdir -p "$dir"
            log_info "✓ 创建目录: $dir"
        fi
    done
}

# 检查端口占用
check_ports() {
    log_info "检查端口占用..."
    
    PORTS=(8000 8001 8002 8003 8080)
    
    for port in "${PORTS[@]}"; do
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            log_warn "端口 $port 已被占用"
            
            # 询问是否继续
            read -p "是否继续启动？(y/N): " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                log_info "启动已取消"
                exit 1
            fi
        else
            log_info "✓ 端口 $port 可用"
        fi
    done
}

# 初始化数据库
init_database() {
    log_info "初始化数据库..."
    
    # 这里可以添加数据库初始化逻辑
    # python3 src/core/database/init_db.py
    
    log_info "✓ 数据库初始化完成"
}

# 启动系统
start_system() {
    log_info "启动系统..."
    
    # 根据参数决定启动模式
    case "${1:-interactive}" in
        "daemon")
            log_info "以守护进程模式启动..."
            nohup python3 src/main.py start --daemon > logs/system/startup.log 2>&1 &
            echo $! > .pid
            log_info "✓ 系统已在后台启动，PID: $(cat .pid)"
            ;;
        "interactive")
            log_info "以交互模式启动..."
            python3 src/main.py interactive
            ;;
        "start")
            log_info "启动所有服务..."
            python3 src/main.py start
            ;;
        *)
            log_error "未知的启动模式: $1"
            exit 1
            ;;
    esac
}

# 显示服务信息
show_service_info() {
    log_info "服务访问地址:"
    echo -e "${GREEN}  Web管理系统: ${BLUE}http://127.0.0.1:8080${NC}"
    echo -e "${GREEN}  API网关:     ${BLUE}http://127.0.0.1:8000${NC}"
    echo -e "${GREEN}  API文档:     ${BLUE}http://127.0.0.1:8000/docs${NC}"
    echo -e "${GREEN}  归属地服务:  ${BLUE}http://127.0.0.1:8001${NC}"
    echo -e "${GREEN}  NLP服务:     ${BLUE}http://127.0.0.1:8002${NC}"
    echo -e "${GREEN}  批量处理:    ${BLUE}http://127.0.0.1:8003${NC}"
    echo ""
    echo -e "${YELLOW}默认管理员账户:${NC}"
    echo -e "${GREEN}  用户名: ${BLUE}admin${NC}"
    echo -e "${GREEN}  密码:   ${BLUE}admin123${NC}"
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 等待服务启动
    sleep 5
    
    SERVICES=(
        "http://127.0.0.1:8080/health"
        "http://127.0.0.1:8000/health"
        "http://127.0.0.1:8001/health"
        "http://127.0.0.1:8002/health"
        "http://127.0.0.1:8003/health"
    )
    
    for service in "${SERVICES[@]}"; do
        if curl -s "$service" > /dev/null 2>&1; then
            log_info "✓ 服务健康: $service"
        else
            log_warn "✗ 服务异常: $service"
        fi
    done
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  interactive    交互模式启动 (默认)"
    echo "  daemon         守护进程模式启动"
    echo "  start          启动所有服务"
    echo "  --help, -h     显示此帮助信息"
    echo "  --check        仅执行环境检查"
    echo "  --install      仅安装依赖"
    echo ""
    echo "示例:"
    echo "  $0                    # 交互模式启动"
    echo "  $0 daemon             # 守护进程模式启动"
    echo "  $0 start              # 启动所有服务"
    echo "  $0 --check            # 环境检查"
    echo "  $0 --install          # 安装依赖"
}

# 主函数
main() {
    # 显示横幅
    show_banner
    
    # 解析参数
    case "${1:-interactive}" in
        "--help"|"-h")
            show_help
            exit 0
            ;;
        "--check")
            check_python
            check_venv
            check_config
            create_directories
            check_ports
            log_info "✓ 环境检查完成"
            exit 0
            ;;
        "--install")
            check_python
            check_venv
            install_dependencies
            log_info "✓ 依赖安装完成"
            exit 0
            ;;
        "daemon"|"interactive"|"start")
            # 执行完整的启动流程
            check_python
            check_venv
            install_dependencies
            check_config
            create_directories
            check_ports
            init_database
            
            # 启动系统
            start_system "$1"
            
            # 如果是守护进程模式，执行健康检查
            if [[ "$1" == "daemon" ]]; then
                health_check
                show_service_info
                log_info "✓ 系统启动完成"
            fi
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
}

# 错误处理
trap 'log_error "启动过程中发生错误，请检查日志"; exit 1' ERR

# 执行主函数
main "$@"
