"""
基于 fastapi-admin 的管理后台系统
提供完整的电话号码标记识别系统管理界面

功能特性：
1. 用户管理和RBAC权限控制
2. 批量数据导入和处理监控
3. 设备管理和状态监控
4. 数据统计和分析报表
5. 系统日志和审计功能
6. 本地-线上数据同步管理
"""

import os
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
from pathlib import Path

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from tortoise.contrib.fastapi import register_tortoise
from fastapi_admin.app import app as admin_app
from fastapi_admin.enums import Method
from fastapi_admin.file_upload import FileUpload
from fastapi_admin.providers.login import UsernamePasswordProvider
from fastapi_admin.resources import Field, Link, Model, ToolbarAction
from fastapi_admin.widgets import displays, filters, inputs

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PhoneMarkingAdminApp:
    """电话标记系统管理后台"""
    
    def __init__(self):
        self.app = FastAPI(
            title="电话号码标记识别管理系统",
            description="基于 FastAPI-Admin 的现代化管理后台",
            version="2.0.0"
        )
        
        # 设置路径
        self.base_dir = Path(__file__).parent
        self.static_dir = self.base_dir / "static"
        self.upload_dir = self.base_dir / "uploads"
        
        # 确保目录存在
        self.static_dir.mkdir(exist_ok=True)
        self.upload_dir.mkdir(exist_ok=True)
        
        # 配置应用
        self._setup_middleware()
        self._setup_static_files()
        self._setup_database()
        self._setup_admin()
        
    def _setup_middleware(self):
        """配置中间件"""
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
    def _setup_static_files(self):
        """配置静态文件"""
        self.app.mount("/static", StaticFiles(directory=str(self.static_dir)), name="static")
        self.app.mount("/uploads", StaticFiles(directory=str(self.upload_dir)), name="uploads")
        
    def _setup_database(self):
        """配置数据库"""
        # 数据库配置
        DATABASE_URL = os.getenv("DATABASE_URL", "mysql://root:@localhost:3306/phone_marking_system")
        
        register_tortoise(
            self.app,
            db_url=DATABASE_URL,
            modules={"models": ["src.models"]},
            generate_schemas=True,
            add_exception_handlers=True,
        )
        
    def _setup_admin(self):
        """配置管理后台"""
        # Redis配置
        REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379/0")
        
        # 挂载管理后台
        self.app.mount("/admin", admin_app)
        
        # 配置管理后台
        admin_app.add_provider(
            UsernamePasswordProvider(
                login_logo_url="https://preview.tabler.io/static/logo.svg",
                admin_model="User",
            )
        )
        
        # 文件上传配置
        upload = FileUpload(uploads_dir=str(self.upload_dir))
        
        # 注册资源
        self._register_resources()
        
    def _register_resources(self):
        """注册管理资源"""
        
        # 用户管理
        @admin_app.register
        class UserResource(Model):
            label = "用户管理"
            model = "User"
            icon = "fas fa-users"
            page_pre_title = "系统管理"
            page_title = "用户管理"
            filters = [
                filters.Search(name="username", label="用户名", search_mode="contains"),
                filters.Search(name="email", label="邮箱", search_mode="contains"),
                filters.Enum(name="is_active", label="状态", enum={"True": "激活", "False": "禁用"}),
            ]
            fields = [
                "id",
                Field(
                    name="username",
                    label="用户名",
                    input_=inputs.Input(),
                    display=displays.InputDisplay(),
                ),
                Field(
                    name="email",
                    label="邮箱",
                    input_=inputs.Email(),
                    display=displays.EmailDisplay(),
                ),
                Field(
                    name="is_active",
                    label="状态",
                    input_=inputs.Switch(),
                    display=displays.BooleanDisplay(),
                ),
                Field(
                    name="created_at",
                    label="创建时间",
                    display=displays.DatetimeDisplay(),
                ),
            ]
        
        # 电话号码数据管理
        @admin_app.register
        class PhoneDataResource(Model):
            label = "号码数据"
            model = "PhoneData"
            icon = "fas fa-phone"
            page_pre_title = "数据管理"
            page_title = "电话号码数据"
            filters = [
                filters.Search(name="phone_number", label="电话号码", search_mode="contains"),
                filters.Search(name="province", label="省份", search_mode="contains"),
                filters.Search(name="city", label="城市", search_mode="contains"),
                filters.Enum(name="status", label="状态", enum={
                    "pending": "待处理",
                    "processing": "处理中", 
                    "completed": "已完成",
                    "failed": "失败"
                }),
            ]
            fields = [
                "id",
                Field(
                    name="phone_number",
                    label="电话号码",
                    input_=inputs.Input(),
                    display=displays.InputDisplay(),
                ),
                Field(
                    name="province",
                    label="省份",
                    display=displays.InputDisplay(),
                ),
                Field(
                    name="city", 
                    label="城市",
                    display=displays.InputDisplay(),
                ),
                Field(
                    name="mark_count",
                    label="标记数量",
                    display=displays.InputDisplay(),
                ),
                Field(
                    name="status",
                    label="状态",
                    input_=inputs.Select(enum={
                        "pending": "待处理",
                        "processing": "处理中",
                        "completed": "已完成", 
                        "failed": "失败"
                    }),
                    display=displays.InputDisplay(),
                ),
                Field(
                    name="created_at",
                    label="创建时间",
                    display=displays.DatetimeDisplay(),
                ),
            ]
        
        # 批量任务管理
        @admin_app.register
        class BatchTaskResource(Model):
            label = "批量任务"
            model = "BatchTask"
            icon = "fas fa-tasks"
            page_pre_title = "任务管理"
            page_title = "批量处理任务"
            filters = [
                filters.Search(name="task_name", label="任务名称", search_mode="contains"),
                filters.Enum(name="status", label="状态", enum={
                    "pending": "待处理",
                    "running": "运行中",
                    "completed": "已完成",
                    "failed": "失败",
                    "cancelled": "已取消"
                }),
            ]
            fields = [
                "id",
                Field(
                    name="task_name",
                    label="任务名称",
                    input_=inputs.Input(),
                    display=displays.InputDisplay(),
                ),
                Field(
                    name="total_count",
                    label="总数量",
                    display=displays.InputDisplay(),
                ),
                Field(
                    name="processed_count",
                    label="已处理",
                    display=displays.InputDisplay(),
                ),
                Field(
                    name="success_count",
                    label="成功数量",
                    display=displays.InputDisplay(),
                ),
                Field(
                    name="failed_count",
                    label="失败数量",
                    display=displays.InputDisplay(),
                ),
                Field(
                    name="progress",
                    label="进度",
                    display=displays.ProgressDisplay(),
                ),
                Field(
                    name="status",
                    label="状态",
                    input_=inputs.Select(enum={
                        "pending": "待处理",
                        "running": "运行中",
                        "completed": "已完成",
                        "failed": "失败",
                        "cancelled": "已取消"
                    }),
                    display=displays.InputDisplay(),
                ),
                Field(
                    name="created_at",
                    label="创建时间",
                    display=displays.DatetimeDisplay(),
                ),
            ]
        
        # 设备管理
        @admin_app.register
        class DeviceResource(Model):
            label = "设备管理"
            model = "Device"
            icon = "fas fa-mobile-alt"
            page_pre_title = "系统管理"
            page_title = "设备管理"
            filters = [
                filters.Search(name="device_name", label="设备名称", search_mode="contains"),
                filters.Enum(name="status", label="状态", enum={
                    "online": "在线",
                    "offline": "离线",
                    "error": "错误"
                }),
            ]
            fields = [
                "id",
                Field(
                    name="device_name",
                    label="设备名称",
                    input_=inputs.Input(),
                    display=displays.InputDisplay(),
                ),
                Field(
                    name="device_id",
                    label="设备ID",
                    display=displays.InputDisplay(),
                ),
                Field(
                    name="status",
                    label="状态",
                    input_=inputs.Select(enum={
                        "online": "在线",
                        "offline": "离线", 
                        "error": "错误"
                    }),
                    display=displays.InputDisplay(),
                ),
                Field(
                    name="last_heartbeat",
                    label="最后心跳",
                    display=displays.DatetimeDisplay(),
                ),
                Field(
                    name="created_at",
                    label="创建时间",
                    display=displays.DatetimeDisplay(),
                ),
            ]
    
    def run(self, host: str = "127.0.0.1", port: int = 8080, debug: bool = True):
        """运行应用"""
        import uvicorn
        uvicorn.run(self.app, host=host, port=port, reload=debug)


# 创建应用实例
phone_admin_app = PhoneMarkingAdminApp()

if __name__ == "__main__":
    phone_admin_app.run()
