<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}电话号码标记识别管理系统{% endblock %}</title>
    
    <!-- Pear Admin CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/layui@2.8.18/dist/css/layui.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/pear-admin@3.0.0/dist/css/pear.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css">
    
    <!-- 自定义CSS -->
    <style>
        .pear-container {
            background: #f5f5f5;
        }
        .pear-nav-tree .layui-nav-item a {
            color: #333;
        }
        .pear-nav-tree .layui-nav-item a:hover {
            color: #1890ff;
        }
        .stats-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .stats-number {
            font-size: 32px;
            font-weight: bold;
            color: #1890ff;
        }
        .stats-label {
            color: #666;
            margin-top: 8px;
        }
        .device-status-online {
            color: #52c41a;
        }
        .device-status-offline {
            color: #ff4d4f;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body class="pear-container">
    
    <!-- 顶部导航 -->
    <div class="layui-header">
        <div class="layui-logo layui-hide-xs">
            <i class="fas fa-phone-alt"></i>
            电话标记系统
        </div>
        
        <!-- 头部区域 -->
        <ul class="layui-nav layui-layout-left">
            <li class="layui-nav-item layui-show-xs-inline-block layui-hide-sm" lay-header-event="menuLeft">
                <i class="layui-icon layui-icon-spread-left"></i>
            </li>
        </ul>
        
        <ul class="layui-nav layui-layout-right">
            <li class="layui-nav-item layui-hide layui-show-md-inline-block">
                <a href="javascript:;" layadmin-event="refresh" title="刷新">
                    <i class="layui-icon layui-icon-refresh-3"></i>
                </a>
            </li>
            <li class="layui-nav-item" lay-unselect>
                <a href="javascript:;" lay-text="消息中心">
                    <i class="layui-icon layui-icon-notice"></i>
                    <span class="layui-badge-dot"></span>
                </a>
            </li>
            <li class="layui-nav-item" lay-unselect>
                <a href="javascript:;">
                    <cite>{{ user.name if user else '管理员' }}</cite>
                </a>
                <dl class="layui-nav-child">
                    <dd><a href="javascript:;">基本资料</a></dd>
                    <dd><a href="javascript:;">安全设置</a></dd>
                    <dd lay-separator></dd>
                    <dd><a href="/login">退出</a></dd>
                </dl>
            </li>
        </ul>
    </div>
    
    <!-- 侧边菜单 -->
    <div class="layui-side layui-side-menu">
        <div class="layui-side-scroll">
            <div class="layui-logo" lay-href="/">
                <span>管理系统</span>
            </div>
            
            <ul class="layui-nav layui-nav-tree pear-nav-tree" lay-shrink="all" id="LAY-system-side-menu">
                <li data-name="dashboard" class="layui-nav-item {% if request.url.path == '/' %}layui-nav-itemed{% endif %}">
                    <a href="/" lay-tips="仪表板" lay-direction="2">
                        <i class="layui-icon layui-icon-home"></i>
                        <cite>仪表板</cite>
                    </a>
                </li>
                
                <li data-name="data" class="layui-nav-item">
                    <a href="javascript:;" lay-tips="数据管理" lay-direction="2">
                        <i class="layui-icon layui-icon-template"></i>
                        <cite>数据管理</cite>
                        <span class="layui-nav-more"></span>
                    </a>
                    <dl class="layui-nav-child">
                        <dd><a href="/data/import">数据导入</a></dd>
                        <dd><a href="/data/export">数据导出</a></dd>
                    </dl>
                </li>
                
                <li data-name="stats" class="layui-nav-item {% if request.url.path == '/stats' %}layui-nav-itemed{% endif %}">
                    <a href="/stats" lay-tips="统计分析" lay-direction="2">
                        <i class="layui-icon layui-icon-chart"></i>
                        <cite>统计分析</cite>
                    </a>
                </li>
                
                <li data-name="devices" class="layui-nav-item {% if request.url.path == '/devices' %}layui-nav-itemed{% endif %}">
                    <a href="/devices" lay-tips="设备管理" lay-direction="2">
                        <i class="layui-icon layui-icon-cellphone"></i>
                        <cite>设备管理</cite>
                    </a>
                </li>
                
                <li data-name="system" class="layui-nav-item">
                    <a href="javascript:;" lay-tips="系统管理" lay-direction="2">
                        <i class="layui-icon layui-icon-set"></i>
                        <cite>系统管理</cite>
                        <span class="layui-nav-more"></span>
                    </a>
                    <dl class="layui-nav-child">
                        <dd><a href="/users">用户管理</a></dd>
                        <dd><a href="/logs">系统日志</a></dd>
                    </dl>
                </li>
            </ul>
        </div>
    </div>
    
    <!-- 主体内容 -->
    <div class="layui-body">
        <div class="layui-fluid">
            {% block content %}{% endblock %}
        </div>
    </div>
    
    <!-- 底部固定区域 -->
    <div class="layui-footer">
        © 2025 电话号码标记识别管理系统 - Powered by Pear Admin
    </div>
    
    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/layui@2.8.18/dist/layui.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    
    <script>
        layui.use(['element', 'layer', 'util'], function(){
            var element = layui.element;
            var layer = layui.layer;
            var util = layui.util;
            
            // 监听导航点击
            element.on('nav(layadmin-system-side-menu)', function(elem){
                if(elem[0].getAttribute('lay-href')){
                    location.href = elem[0].getAttribute('lay-href');
                }
            });
            
            // 全局AJAX设置
            $.ajaxSetup({
                beforeSend: function(xhr, settings) {
                    // 可以在这里添加token等认证信息
                },
                error: function(xhr, status, error) {
                    layer.msg('请求失败: ' + error, {icon: 2});
                }
            });
        });
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
