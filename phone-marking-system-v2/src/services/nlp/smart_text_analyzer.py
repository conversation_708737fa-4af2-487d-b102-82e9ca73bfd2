"""
智能文本分析器
使用轻量级NLP技术进行电话标记的语义理解和分类
采用分层处理策略，平衡性能和准确性
"""

import re
import json
import os
import logging
import time
from typing import Dict, List, Any, Optional, Tuple
from collections import defaultdict, Counter
from datetime import datetime


class SmartTextAnalyzer:
    """智能文本分析器"""
    
    def __init__(self, config_file: str = 'nlp_config.json'):
        self.logger = logging.getLogger(__name__)
        self.config_file = config_file
        
        # 加载配置
        self.config = self._load_config()
        
        # 初始化简单分词器
        self._init_simple_tokenizer()
        
        # 预定义的分类规则
        self.categories = self._load_categories()
        
        # 关键词权重
        self.keyword_weights = self._load_keyword_weights()
        
        # 性能统计
        self.performance_stats = {
            'total_analyzed': 0,
            'keyword_matches': 0,
            'rule_matches': 0,
            'nlp_analyses': 0,
            'avg_processing_time': 0.0,
            'cache_hits': 0
        }
        
        # 简单缓存
        self.analysis_cache = {}
        self.cache_max_size = 500
        
        self.logger.info("智能文本分析器初始化完成")
    
    def _load_config(self) -> Dict[str, Any]:
        """加载NLP配置"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                self.logger.info("NLP配置加载成功")
                return config
            except Exception as e:
                self.logger.warning(f"加载NLP配置失败: {e}")
        
        # 默认配置
        default_config = {
            'enable_cache': True,
            'cache_ttl': 3600,  # 缓存1小时
            'min_confidence_threshold': 0.6,
            'keyword_match_weight': 0.8,
            'rule_match_weight': 0.7,
            'nlp_analysis_weight': 0.6,
            'enable_deep_analysis': False,  # 默认关闭深度分析
            'max_processing_time': 0.1,  # 最大处理时间100ms
            'fallback_to_keyword': True
        }
        
        self._save_config(default_config)
        return default_config
    
    def _save_config(self, config: Dict[str, Any]):
        """保存NLP配置"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            self.logger.error(f"保存NLP配置失败: {e}")
    
    def _init_simple_tokenizer(self):
        """初始化简单分词器"""
        try:
            # 预定义词典
            self.custom_words = [
                '骚扰电话', '推销电话', '诈骗电话', '广告电话',
                '快递员', '外卖员', '客服', '银行客服',
                '房产中介', '保险推销', '贷款推销',
                '疑似诈骗', '疑似骚扰', '疑似推销',
                '中奖', '验证码', '转账', '汇款'
            ]

            # 按长度排序，优先匹配长词
            self.custom_words.sort(key=len, reverse=True)

            self.logger.debug("简单分词器初始化完成")

        except Exception as e:
            self.logger.warning(f"分词器初始化失败: {e}")

    def _simple_tokenize(self, text: str) -> List[str]:
        """简单分词实现"""
        if not text:
            return []

        words = []
        i = 0

        while i < len(text):
            matched = False

            # 尝试匹配自定义词典
            for word in self.custom_words:
                if text[i:].startswith(word):
                    words.append(word)
                    i += len(word)
                    matched = True
                    break

            if not matched:
                # 单字符处理
                char = text[i]
                if char.strip():  # 忽略空白字符
                    words.append(char)
                i += 1

        return words
    
    def _load_categories(self) -> Dict[str, Dict[str, Any]]:
        """加载分类规则"""
        return {
            'spam': {
                'keywords': [
                    '骚扰', '推销', '广告', '营销', '促销', '优惠',
                    '贷款', '投资', '理财', '保险', '房产', '装修',
                    '培训', '招生', '兼职', '刷单', '代办'
                ],
                'patterns': [
                    r'.*推销.*',
                    r'.*广告.*',
                    r'.*营销.*',
                    r'.*贷款.*',
                    r'.*投资.*理财.*',
                    r'.*保险.*推荐.*'
                ],
                'negative_keywords': ['朋友', '家人', '同事', '客服'],
                'confidence_boost': 0.2
            },
            'fraud': {
                'keywords': [
                    '诈骗', '欺诈', '虚假', '假冒', '冒充',
                    '中奖', '退款', '验证码', '银行卡',
                    '转账', '汇款', '紧急', '公安', '法院'
                ],
                'patterns': [
                    r'.*诈骗.*',
                    r'.*中奖.*',
                    r'.*验证码.*',
                    r'.*紧急.*转账.*',
                    r'.*公安.*法院.*'
                ],
                'negative_keywords': ['正常', '官方', '客服'],
                'confidence_boost': 0.3
            },
            'business': {
                'keywords': [
                    '快递', '外卖', '配送', '送达', '签收',
                    '客服', '服务', '咨询', '预约', '通知',
                    '银行', '医院', '学校', '政府', '官方'
                ],
                'patterns': [
                    r'.*快递.*',
                    r'.*外卖.*',
                    r'.*客服.*',
                    r'.*通知.*',
                    r'.*预约.*'
                ],
                'negative_keywords': ['推销', '广告', '诈骗'],
                'confidence_boost': 0.1
            },
            'personal': {
                'keywords': [
                    '朋友', '家人', '同事', '同学', '老师',
                    '医生', '律师', '邻居', '亲戚', '熟人'
                ],
                'patterns': [
                    r'.*朋友.*',
                    r'.*家人.*',
                    r'.*同事.*',
                    r'.*熟人.*'
                ],
                'negative_keywords': ['推销', '广告', '陌生'],
                'confidence_boost': 0.1
            },
            'unknown': {
                'keywords': ['未知', '陌生', '不明', '无标记'],
                'patterns': [r'.*未知.*', r'.*陌生.*'],
                'negative_keywords': [],
                'confidence_boost': 0.0
            }
        }
    
    def _load_keyword_weights(self) -> Dict[str, float]:
        """加载关键词权重"""
        return {
            # 高权重关键词
            '诈骗': 0.9, '骗子': 0.9, '欺诈': 0.9,
            '推销': 0.8, '广告': 0.8, '营销': 0.8,
            '骚扰': 0.8, '垃圾': 0.8, 'spam': 0.8,
            
            # 中权重关键词
            '贷款': 0.7, '投资': 0.7, '理财': 0.7,
            '保险': 0.6, '房产': 0.6, '装修': 0.6,
            
            # 低权重关键词
            '快递': 0.3, '外卖': 0.3, '客服': 0.3,
            '通知': 0.2, '提醒': 0.2, '服务': 0.2
        }
    
    def analyze_text(self, text: str, phone_number: str = '') -> Dict[str, Any]:
        """分析文本内容"""
        start_time = time.time()
        
        try:
            # 检查缓存
            cache_key = f"{text}_{phone_number}"
            if self.config.get('enable_cache', True) and cache_key in self.analysis_cache:
                self.performance_stats['cache_hits'] += 1
                return self.analysis_cache[cache_key]
            
            # 文本预处理
            cleaned_text = self._preprocess_text(text)
            
            if not cleaned_text:
                return self._create_result('unknown', 0.0, 'empty_text', {})
            
            # 分层分析策略
            result = None
            
            # 第1层：快速关键词匹配
            result = self._quick_keyword_analysis(cleaned_text)
            if result['confidence'] >= self.config.get('min_confidence_threshold', 0.6):
                result['method'] = 'keyword_match'
                self.performance_stats['keyword_matches'] += 1
            else:
                # 第2层：规则匹配
                result = self._rule_based_analysis(cleaned_text)
                if result['confidence'] >= self.config.get('min_confidence_threshold', 0.6):
                    result['method'] = 'rule_match'
                    self.performance_stats['rule_matches'] += 1
                else:
                    # 第3层：NLP分析（可选）
                    if self.config.get('enable_deep_analysis', False):
                        result = self._deep_nlp_analysis(cleaned_text)
                        result['method'] = 'nlp_analysis'
                        self.performance_stats['nlp_analyses'] += 1
                    else:
                        # 使用最佳规则结果
                        result['method'] = 'rule_fallback'
            
            # 添加处理时间
            processing_time = time.time() - start_time
            result['processing_time'] = processing_time
            
            # 更新统计
            self.performance_stats['total_analyzed'] += 1
            total_time = (self.performance_stats['avg_processing_time'] * 
                         (self.performance_stats['total_analyzed'] - 1) + processing_time)
            self.performance_stats['avg_processing_time'] = total_time / self.performance_stats['total_analyzed']
            
            # 缓存结果
            if self.config.get('enable_cache', True):
                self._cache_result(cache_key, result)
            
            return result
            
        except Exception as e:
            self.logger.error(f"文本分析失败: {e}")
            processing_time = time.time() - start_time
            return self._create_result('unknown', 0.0, 'error', {'error': str(e), 'processing_time': processing_time})
    
    def _preprocess_text(self, text: str) -> str:
        """文本预处理"""
        if not text:
            return ''
        
        # 转换为小写
        text = text.lower()
        
        # 移除特殊字符，保留中文、英文、数字
        text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s]', ' ', text)
        
        # 移除多余空格
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
    
    def _quick_keyword_analysis(self, text: str) -> Dict[str, Any]:
        """快速关键词分析"""
        category_scores = defaultdict(float)
        matched_keywords = []
        
        for category, category_info in self.categories.items():
            keywords = category_info.get('keywords', [])
            negative_keywords = category_info.get('negative_keywords', [])
            
            # 检查正面关键词
            for keyword in keywords:
                if keyword in text:
                    weight = self.keyword_weights.get(keyword, 0.5)
                    category_scores[category] += weight
                    matched_keywords.append(keyword)
            
            # 检查负面关键词（降低得分）
            for neg_keyword in negative_keywords:
                if neg_keyword in text:
                    category_scores[category] -= 0.3
        
        # 找到最高得分的类别
        if category_scores:
            best_category = max(category_scores.items(), key=lambda x: x[1])
            confidence = min(1.0, best_category[1])
            
            return self._create_result(
                best_category[0], 
                confidence, 
                'keyword_match',
                {'matched_keywords': matched_keywords, 'scores': dict(category_scores)}
            )
        
        return self._create_result('unknown', 0.0, 'no_keyword_match', {})
    
    def _rule_based_analysis(self, text: str) -> Dict[str, Any]:
        """基于规则的分析"""
        category_scores = defaultdict(float)
        matched_patterns = []
        
        for category, category_info in self.categories.items():
            patterns = category_info.get('patterns', [])
            confidence_boost = category_info.get('confidence_boost', 0.0)
            
            for pattern in patterns:
                try:
                    if re.search(pattern, text):
                        category_scores[category] += 0.6 + confidence_boost
                        matched_patterns.append(pattern)
                except re.error:
                    continue
        
        # 结合关键词分析
        keyword_result = self._quick_keyword_analysis(text)
        if keyword_result['confidence'] > 0:
            category = keyword_result['category']
            category_scores[category] += keyword_result['confidence'] * 0.5
        
        # 找到最高得分的类别
        if category_scores:
            best_category = max(category_scores.items(), key=lambda x: x[1])
            confidence = min(1.0, best_category[1])
            
            return self._create_result(
                best_category[0],
                confidence,
                'rule_match',
                {
                    'matched_patterns': matched_patterns,
                    'scores': dict(category_scores),
                    'keyword_info': keyword_result.get('details', {})
                }
            )
        
        return self._create_result('unknown', 0.0, 'no_rule_match', {})
    
    def _deep_nlp_analysis(self, text: str) -> Dict[str, Any]:
        """深度NLP分析（使用简单分词）"""
        try:
            # 简单分词
            words = self._simple_tokenize(text)

            # 提取特征
            features = self._extract_nlp_features(words)

            # 基于特征的分类
            category_scores = self._classify_by_features(features)

            if category_scores:
                best_category = max(category_scores.items(), key=lambda x: x[1])
                confidence = min(1.0, best_category[1])

                return self._create_result(
                    best_category[0],
                    confidence,
                    'nlp_analysis',
                    {
                        'features': features,
                        'scores': dict(category_scores),
                        'words': words[:10]  # 只保留前10个词
                    }
                )

        except Exception as e:
            self.logger.warning(f"深度NLP分析失败: {e}")

        return self._create_result('unknown', 0.0, 'nlp_analysis_failed', {})
    
    def _extract_nlp_features(self, words: List[str]) -> Dict[str, Any]:
        """提取NLP特征（简化版）"""
        features = {
            'word_count': len(words),
            'unique_words': len(set(words)),
            'avg_word_length': sum(len(word) for word in words) / max(len(words), 1),
            'has_numbers': any(word.isdigit() for word in words),
            'has_english': any(re.search(r'[a-zA-Z]', word) for word in words),
            'chinese_char_count': sum(1 for word in words for char in word if '\u4e00' <= char <= '\u9fff'),
            'custom_word_count': sum(1 for word in words if word in self.custom_words)
        }

        return features
    
    def _classify_by_features(self, features: Dict[str, Any]) -> Dict[str, float]:
        """基于特征进行分类（简化版）"""
        scores = defaultdict(float)

        # 简单的特征规则
        word_count = features.get('word_count', 0)
        custom_word_count = features.get('custom_word_count', 0)

        # 长文本通常是推销
        if word_count > 10:
            scores['spam'] += 0.3

        # 包含数字可能是诈骗或推销
        if features.get('has_numbers', False):
            scores['fraud'] += 0.2
            scores['spam'] += 0.2

        # 包含自定义词汇
        if custom_word_count > 0:
            scores['spam'] += 0.4
            scores['fraud'] += 0.3

        # 中文字符比例高可能是正常业务
        chinese_ratio = features.get('chinese_char_count', 0) / max(word_count, 1)
        if chinese_ratio > 0.8:
            scores['business'] += 0.2

        # 平均词长较长可能是推销
        avg_length = features.get('avg_word_length', 0)
        if avg_length > 3:
            scores['spam'] += 0.2

        return scores
    
    def _create_result(self, category: str, confidence: float, 
                      method: str, details: Dict[str, Any]) -> Dict[str, Any]:
        """创建分析结果"""
        return {
            'category': category,
            'confidence': round(confidence, 3),
            'method': method,
            'details': details,
            'timestamp': datetime.now().isoformat()
        }
    
    def _cache_result(self, key: str, result: Dict[str, Any]):
        """缓存分析结果"""
        if len(self.analysis_cache) >= self.cache_max_size:
            # 简单的LRU：删除一半缓存
            keys_to_remove = list(self.analysis_cache.keys())[:self.cache_max_size // 2]
            for k in keys_to_remove:
                del self.analysis_cache[k]
        
        self.analysis_cache[key] = result
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        total = self.performance_stats['total_analyzed']
        
        return {
            'total_analyzed': total,
            'keyword_match_rate': (self.performance_stats['keyword_matches'] / max(total, 1)) * 100,
            'rule_match_rate': (self.performance_stats['rule_matches'] / max(total, 1)) * 100,
            'nlp_analysis_rate': (self.performance_stats['nlp_analyses'] / max(total, 1)) * 100,
            'cache_hit_rate': (self.performance_stats['cache_hits'] / max(total, 1)) * 100,
            'avg_processing_time_ms': self.performance_stats['avg_processing_time'] * 1000,
            'cache_size': len(self.analysis_cache)
        }
    
    def clear_cache(self):
        """清空缓存"""
        self.analysis_cache.clear()
        self.logger.info("分析缓存已清空")


def test_smart_text_analyzer():
    """测试智能文本分析器"""
    analyzer = SmartTextAnalyzer()
    
    # 测试文本
    test_texts = [
        "这是一个推销电话，向您推荐理财产品",
        "您好，我是快递员，您的包裹到了",
        "恭喜您中奖了，请提供银行卡信息",
        "朋友推荐的餐厅，味道不错",
        "陌生号码，未知来源",
        "银行客服提醒您账户异常",
        "房产中介推荐优质房源"
    ]
    
    print("=== 智能文本分析测试 ===")
    
    for i, text in enumerate(test_texts, 1):
        result = analyzer.analyze_text(text)
        print(f"\n{i}. 文本: {text}")
        print(f"   分类: {result['category']}")
        print(f"   置信度: {result['confidence']}")
        print(f"   方法: {result['method']}")
        print(f"   处理时间: {result.get('processing_time', 0)*1000:.2f}ms")
    
    # 性能统计
    print(f"\n=== 性能统计 ===")
    stats = analyzer.get_performance_stats()
    for key, value in stats.items():
        print(f"{key}: {value}")


if __name__ == '__main__':
    test_smart_text_analyzer()
