"""
归属地数据导入脚本
将手机号段和固话区号CSV文件导入到SQLite数据库中
"""

import sqlite3
import csv
import os
import logging
from typing import Dict, List
import time


class LocationDataImporter:
    """归属地数据导入器"""
    
    def __init__(self, db_path: str = 'phone_marks.db'):
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
        
        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
    
    def import_all_data(self):
        """导入所有归属地数据"""
        self.logger.info("开始导入归属地数据...")
        
        # 初始化数据库结构
        self._init_database()
        
        # 导入手机号段数据
        mobile_file = '号段-phone-202506-517574/qqzeng-phone-202506-517574.csv'
        if os.path.exists(mobile_file):
            self.import_mobile_data(mobile_file)
        else:
            self.logger.error(f"手机号段文件不存在: {mobile_file}")
        
        # 导入固话区号数据
        landline_file = '号段-phone-202506-517574/副本城市号码.csv'
        if os.path.exists(landline_file):
            self.import_landline_data(landline_file)
        else:
            self.logger.error(f"固话区号文件不存在: {landline_file}")
        
        # 创建索引
        self._create_indexes()
        
        # 显示统计信息
        self._show_statistics()
        
        self.logger.info("归属地数据导入完成!")
    
    def _init_database(self):
        """初始化数据库结构"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 读取并执行数据库设计脚本
            if os.path.exists('location_database_design.sql'):
                with open('location_database_design.sql', 'r', encoding='utf-8') as f:
                    sql_script = f.read()
                    cursor.executescript(sql_script)
            else:
                # 如果脚本文件不存在，直接创建表
                self._create_tables_directly(cursor)
            
            conn.commit()
            conn.close()
            self.logger.info("数据库结构初始化完成")
            
        except Exception as e:
            self.logger.error(f"数据库初始化失败: {e}")
            raise
    
    def _create_tables_directly(self, cursor):
        """直接创建数据库表"""
        # 手机号段表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS mobile_location (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                segment TEXT NOT NULL,
                phone_prefix TEXT NOT NULL,
                province TEXT NOT NULL,
                city TEXT NOT NULL,
                isp TEXT,
                tel_code TEXT,
                postal_code TEXT,
                area_code TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(segment)
            )
        """)
        
        # 固话区号表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS landline_location (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                area_code TEXT NOT NULL,
                province TEXT NOT NULL,
                city TEXT NOT NULL,
                main_length INTEGER,
                called_length TEXT,
                region TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(area_code)
            )
        """)
        
        # 缓存表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS location_cache (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                phone_number TEXT NOT NULL,
                phone_type TEXT NOT NULL,
                province TEXT NOT NULL,
                city TEXT NOT NULL,
                isp TEXT,
                area_code TEXT,
                last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(phone_number)
            )
        """)
    
    def import_mobile_data(self, csv_file: str):
        """导入手机号段数据"""
        self.logger.info(f"开始导入手机号段数据: {csv_file}")
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 清空现有数据
            cursor.execute("DELETE FROM mobile_location")
            
            imported_count = 0
            batch_size = 1000
            batch_data = []
            
            with open(csv_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                
                for row in reader:
                    # 提取7位号段
                    phone_prefix = row.get('phone', '')
                    if len(phone_prefix) >= 7:
                        segment = phone_prefix[:7]
                        
                        batch_data.append((
                            segment,
                            phone_prefix,
                            row.get('province', ''),
                            row.get('city', ''),
                            row.get('isp', ''),
                            row.get('tel_code', ''),
                            row.get('postal_code', ''),
                            row.get('area_code', '')
                        ))
                        
                        if len(batch_data) >= batch_size:
                            self._insert_mobile_batch(cursor, batch_data)
                            imported_count += len(batch_data)
                            batch_data = []
                            
                            if imported_count % 10000 == 0:
                                self.logger.info(f"已导入 {imported_count} 条手机号段记录...")
                
                # 导入剩余数据
                if batch_data:
                    self._insert_mobile_batch(cursor, batch_data)
                    imported_count += len(batch_data)
            
            conn.commit()
            conn.close()
            
            self.logger.info(f"手机号段数据导入完成，共导入 {imported_count} 条记录")
            
        except Exception as e:
            self.logger.error(f"导入手机号段数据失败: {e}")
            raise
    
    def _insert_mobile_batch(self, cursor, batch_data: List[tuple]):
        """批量插入手机号段数据"""
        cursor.executemany("""
            INSERT OR REPLACE INTO mobile_location 
            (segment, phone_prefix, province, city, isp, tel_code, postal_code, area_code)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, batch_data)
    
    def import_landline_data(self, csv_file: str):
        """导入固话区号数据"""
        self.logger.info(f"开始导入固话区号数据: {csv_file}")
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 清空现有数据
            cursor.execute("DELETE FROM landline_location")
            
            imported_count = 0
            
            with open(csv_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                
                for row in reader:
                    area_code = row.get('地区区号', '')
                    province = row.get('省份', '')
                    city = row.get('城市', '')
                    
                    if area_code and province and city:
                        cursor.execute("""
                            INSERT OR REPLACE INTO landline_location 
                            (area_code, province, city, main_length, called_length, region)
                            VALUES (?, ?, ?, ?, ?, ?)
                        """, (
                            area_code,
                            province,
                            city,
                            row.get('主叫号码长度', ''),
                            row.get('被叫号码长度', ''),
                            row.get('地域', '')
                        ))
                        
                        imported_count += 1
                        
                        if imported_count % 100 == 0:
                            self.logger.info(f"已导入 {imported_count} 条固话区号记录...")
            
            conn.commit()
            conn.close()
            
            self.logger.info(f"固话区号数据导入完成，共导入 {imported_count} 条记录")
            
        except Exception as e:
            self.logger.error(f"导入固话区号数据失败: {e}")
            raise
    
    def _create_indexes(self):
        """创建数据库索引"""
        self.logger.info("创建数据库索引...")
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 手机号段索引
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_mobile_segment ON mobile_location(segment)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_mobile_province_city ON mobile_location(province, city)")
            
            # 固话区号索引
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_landline_area_code ON landline_location(area_code)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_landline_province_city ON landline_location(province, city)")
            
            # 缓存表索引
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_cache_phone ON location_cache(phone_number)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_cache_updated ON location_cache(last_updated)")
            
            conn.commit()
            conn.close()
            
            self.logger.info("数据库索引创建完成")
            
        except Exception as e:
            self.logger.error(f"创建索引失败: {e}")
    
    def _show_statistics(self):
        """显示数据统计信息"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT COUNT(*) FROM mobile_location")
            mobile_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM landline_location")
            landline_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(DISTINCT province) FROM mobile_location")
            mobile_provinces = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(DISTINCT province) FROM landline_location")
            landline_provinces = cursor.fetchone()[0]
            
            conn.close()
            
            self.logger.info("=== 数据统计信息 ===")
            self.logger.info(f"手机号段记录数: {mobile_count:,}")
            self.logger.info(f"固话区号记录数: {landline_count:,}")
            self.logger.info(f"手机号段覆盖省份数: {mobile_provinces}")
            self.logger.info(f"固话区号覆盖省份数: {landline_provinces}")
            
        except Exception as e:
            self.logger.error(f"获取统计信息失败: {e}")
    
    def test_query_performance(self, test_numbers: List[str] = None):
        """测试查询性能"""
        if not test_numbers:
            test_numbers = [
                '13800138000',  # 手机号
                '02088888888',  # 固话
                '01012345678',  # 北京固话
                '15912345678',  # 手机号
            ]
        
        from location_manager import LocationManager
        location_manager = LocationManager(self.db_path)
        
        self.logger.info("=== 查询性能测试 ===")
        
        for phone in test_numbers:
            start_time = time.time()
            result = location_manager.get_phone_location(phone)
            end_time = time.time()
            
            query_time = (end_time - start_time) * 1000  # 转换为毫秒
            
            self.logger.info(f"号码: {phone}")
            self.logger.info(f"结果: {result['province']} {result['city']} {result.get('isp', '')}")
            self.logger.info(f"查询耗时: {query_time:.2f}ms")
            self.logger.info("---")


def main():
    """主函数"""
    importer = LocationDataImporter()
    
    try:
        # 导入所有数据
        importer.import_all_data()
        
        # 测试查询性能
        importer.test_query_performance()
        
    except Exception as e:
        logging.error(f"导入过程出错: {e}")
        raise


if __name__ == '__main__':
    main()
