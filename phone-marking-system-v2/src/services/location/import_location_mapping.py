import sqlite3
import csv
import os

def import_csv_to_table(db_path, csv_path, table_name):
    with open(csv_path, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        fieldnames = reader.fieldnames
        # 建表
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        columns = ', '.join([f'"{col}" TEXT' for col in fieldnames])
        cursor.execute(f'CREATE TABLE IF NOT EXISTS {table_name} ({columns})')
        # 清空表
        cursor.execute(f'DELETE FROM {table_name}')
        # 插入数据
        placeholders = ', '.join(['?' for _ in fieldnames])
        for row in reader:
            values = [row.get(col, '') for col in fieldnames]
            cursor.execute(f'INSERT INTO {table_name} VALUES ({placeholders})', values)
        conn.commit()
        conn.close()
        print(f'导入 {csv_path} 到 {table_name} 完成，共 {reader.line_num-1} 条')

if __name__ == '__main__':
    db_path = 'phone_marks.db'
    # 手机号段
    import_csv_to_table(db_path, 'qqzeng-phone-202506-517574.csv', 'mobile_segments')
    # 固话区号
    import_csv_to_table(db_path, '号段-phone-202506-517574/副本城市号码.csv', 'landline_segments') 