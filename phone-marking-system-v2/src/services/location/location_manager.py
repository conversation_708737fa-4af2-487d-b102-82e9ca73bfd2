"""
归属地查询管理器
为电话号码标记识别系统提供高效的归属地查询功能
"""

import sqlite3
import logging
import re
from typing import Dict, Optional, Tuple
from datetime import datetime, timedelta


class LocationManager:
    """归属地查询管理器"""
    
    def __init__(self, db_path: str = 'phone_marks.db'):
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
        self._init_database()
    
    def _init_database(self):
        """初始化数据库表结构"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 读取并执行数据库设计脚本
            with open('location_database_design.sql', 'r', encoding='utf-8') as f:
                sql_script = f.read()
                cursor.executescript(sql_script)
            
            conn.commit()
            conn.close()
            self.logger.info("归属地数据库初始化完成")
            
        except Exception as e:
            self.logger.error(f"数据库初始化失败: {e}")
            raise
    
    def get_phone_location(self, phone_number: str) -> Dict[str, str]:
        """
        获取电话号码的归属地信息
        
        Args:
            phone_number: 电话号码
            
        Returns:
            包含省份、城市、运营商等信息的字典
        """
        # 清理电话号码格式
        clean_phone = self._clean_phone_number(phone_number)
        
        if not clean_phone:
            return self._empty_location()
        
        # 首先检查缓存
        cached_result = self._get_from_cache(clean_phone)
        if cached_result:
            return cached_result
        
        # 判断号码类型并查询
        if self._is_mobile_number(clean_phone):
            result = self._get_mobile_location(clean_phone)
        elif self._is_landline_number(clean_phone):
            result = self._get_landline_location(clean_phone)
        else:
            result = self._empty_location()
        
        # 如果查询成功，保存到缓存
        if result['province'] and result['city']:
            self._save_to_cache(clean_phone, result)
        
        return result
    
    def _clean_phone_number(self, phone_number: str) -> str:
        """清理电话号码格式"""
        if not phone_number:
            return ""
        
        # 移除所有非数字字符
        clean = re.sub(r'[^\d]', '', str(phone_number))
        
        # 移除国际区号
        if clean.startswith('86') and len(clean) > 11:
            clean = clean[2:]
        elif clean.startswith('+86'):
            clean = clean[3:]
        
        return clean
    
    def _is_mobile_number(self, phone_number: str) -> bool:
        """判断是否为手机号码"""
        return len(phone_number) == 11 and phone_number.startswith('1')
    
    def _is_landline_number(self, phone_number: str) -> bool:
        """判断是否为固话号码"""
        return len(phone_number) >= 7 and not phone_number.startswith('1')
    
    def _get_mobile_location(self, phone_number: str) -> Dict[str, str]:
        """获取手机号码归属地"""
        if len(phone_number) < 7:
            return self._empty_location()
        
        segment = phone_number[:7]
        
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT province, city, isp, tel_code, area_code
                FROM mobile_location 
                WHERE segment = ?
            """, (segment,))
            
            row = cursor.fetchone()
            conn.close()
            
            if row:
                return {
                    'phone_type': 'mobile',
                    'province': row['province'] or '',
                    'city': row['city'] or '',
                    'isp': row['isp'] or '',
                    'area_code': row['tel_code'] or row['area_code'] or '',
                    'location': f"{row['province']}{row['city']}" if row['province'] and row['city'] else ''
                }
            
        except Exception as e:
            self.logger.error(f"查询手机号归属地失败: {e}")
        
        return self._empty_location()
    
    def _get_landline_location(self, phone_number: str) -> Dict[str, str]:
        """获取固话号码归属地"""
        # 尝试4位和3位区号，同时处理带0前缀和不带0前缀的情况
        for length in [4, 3]:
            if len(phone_number) >= length:
                area_code = phone_number[:length]

                # 先尝试原始区号
                result = self._query_landline_by_area_code(area_code)
                if result['province']:
                    return result

                # 如果原始区号以0开头，尝试去掉0的区号
                if area_code.startswith('0') and len(area_code) > 1:
                    area_code_without_zero = area_code[1:]
                    result = self._query_landline_by_area_code(area_code_without_zero)
                    if result['province']:
                        return result

                # 如果原始区号不以0开头，尝试加上0的区号
                if not area_code.startswith('0'):
                    area_code_with_zero = '0' + area_code
                    result = self._query_landline_by_area_code(area_code_with_zero)
                    if result['province']:
                        return result

        return self._empty_location()
    
    def _query_landline_by_area_code(self, area_code: str) -> Dict[str, str]:
        """根据区号查询固话归属地"""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT province, city, region
                FROM landline_location 
                WHERE area_code = ?
            """, (area_code,))
            
            row = cursor.fetchone()
            conn.close()
            
            if row:
                return {
                    'phone_type': 'landline',
                    'province': row['province'] or '',
                    'city': row['city'] or '',
                    'isp': '',
                    'area_code': area_code,
                    'region': row['region'] or '',
                    'location': f"{row['province']}{row['city']}" if row['province'] and row['city'] else ''
                }
            
        except Exception as e:
            self.logger.error(f"查询固话归属地失败: {e}")
        
        return self._empty_location()
    
    def _get_from_cache(self, phone_number: str) -> Optional[Dict[str, str]]:
        """从缓存中获取归属地信息"""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # 检查缓存是否过期（30天）
            cursor.execute("""
                SELECT phone_type, province, city, isp, area_code, last_updated
                FROM location_cache 
                WHERE phone_number = ? AND last_updated > datetime('now', '-30 days')
            """, (phone_number,))
            
            row = cursor.fetchone()
            conn.close()
            
            if row:
                return {
                    'phone_type': row['phone_type'],
                    'province': row['province'] or '',
                    'city': row['city'] or '',
                    'isp': row['isp'] or '',
                    'area_code': row['area_code'] or '',
                    'location': f"{row['province']}{row['city']}" if row['province'] and row['city'] else ''
                }
            
        except Exception as e:
            self.logger.error(f"缓存查询失败: {e}")
        
        return None
    
    def _save_to_cache(self, phone_number: str, location_info: Dict[str, str]):
        """保存归属地信息到缓存"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT OR REPLACE INTO location_cache 
                (phone_number, phone_type, province, city, isp, area_code, last_updated)
                VALUES (?, ?, ?, ?, ?, ?, datetime('now'))
            """, (
                phone_number,
                location_info.get('phone_type', ''),
                location_info.get('province', ''),
                location_info.get('city', ''),
                location_info.get('isp', ''),
                location_info.get('area_code', '')
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"保存缓存失败: {e}")
    
    def _empty_location(self) -> Dict[str, str]:
        """返回空的归属地信息"""
        return {
            'phone_type': '',
            'province': '',
            'city': '',
            'isp': '',
            'area_code': '',
            'location': ''
        }
    
    def clean_expired_cache(self, days: int = 30):
        """清理过期的缓存数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                DELETE FROM location_cache 
                WHERE last_updated < datetime('now', '-{} days')
            """.format(days))
            
            deleted_count = cursor.rowcount
            conn.commit()
            conn.close()
            
            self.logger.info(f"清理了 {deleted_count} 条过期缓存记录")
            return deleted_count
            
        except Exception as e:
            self.logger.error(f"清理缓存失败: {e}")
            return 0
    
    def get_statistics(self) -> Dict[str, int]:
        """获取归属地数据统计信息"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 统计各表的记录数
            stats = {}
            
            cursor.execute("SELECT COUNT(*) FROM mobile_location")
            stats['mobile_records'] = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM landline_location")
            stats['landline_records'] = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM location_cache")
            stats['cache_records'] = cursor.fetchone()[0]
            
            conn.close()
            return stats
            
        except Exception as e:
            self.logger.error(f"获取统计信息失败: {e}")
            return {}
