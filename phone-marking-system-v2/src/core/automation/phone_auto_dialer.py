#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Phone Auto Dialer - 自动拨号并获取标记信息
根据输入的号码自动拨打电话，获取显示的标记信息并导出
"""

import subprocess
import time
import json
import pandas as pd
import csv
import os
import re
from datetime import datetime
from typing import List, Dict, Optional
import cv2
import numpy as np
from PIL import Image
import pytesseract

class PhoneAutoDialer:
    def __init__(self):
        self.device_connected = self._check_device()
        self.results = []
        
    def _check_device(self) -> bool:
        """检查设备连接状态"""
        try:
            result = subprocess.run(['adb', 'devices'], capture_output=True, text=True)
            devices = result.stdout.strip().split('\n')[1:]
            connected_devices = [d for d in devices if d.strip() and 'device' in d]
            return len(connected_devices) > 0
        except Exception as e:
            print(f"ADB检查失败: {e}")
            return False
    
    def read_phone_numbers(self, file_path: str) -> List[str]:
        """
        从文件读取电话号码
        支持 txt, csv, xlsx 格式
        """
        phone_numbers = []
        
        try:
            if file_path.endswith('.txt'):
                with open(file_path, 'r', encoding='utf-8') as f:
                    for line in f:
                        phone = line.strip()
                        if self._validate_phone(phone):
                            phone_numbers.append(phone)
                            
            elif file_path.endswith('.csv'):
                df = pd.read_csv(file_path)
                # 假设第一列是电话号码
                for phone in df.iloc[:, 0]:
                    if self._validate_phone(str(phone)):
                        phone_numbers.append(str(phone))
                        
            elif file_path.endswith('.xlsx'):
                df = pd.read_excel(file_path)
                # 假设第一列是电话号码
                for phone in df.iloc[:, 0]:
                    if self._validate_phone(str(phone)):
                        phone_numbers.append(str(phone))
                        
        except Exception as e:
            print(f"读取文件失败: {e}")
            
        return phone_numbers
    
    def _validate_phone(self, phone: str) -> bool:
        """验证电话号码格式"""
        # 支持手机号和座机号
        mobile_pattern = r'^1[3-9]\d{9}$'
        landline_pattern = r'^0\d{2,3}-?\d{7,8}$'
        
        return bool(re.match(mobile_pattern, phone) or re.match(landline_pattern, phone))
    
    def make_call(self, phone_number: str, duration: int = 3) -> bool:
        """
        拨打电话
        """
        if not self.device_connected:
            print("设备未连接")
            return False
            
        try:
            print(f"正在拨打: {phone_number}")
            
            # 拨打电话
            subprocess.run(['adb', 'shell', 'am', 'start', '-a', 'android.intent.action.CALL', 
                          '-d', f'tel:{phone_number}'])
            time.sleep(3)
            
            # 等待通话界面显示
            time.sleep(2)
            
            # 截图获取标记信息
            mark_info = self._get_mark_info(phone_number)
            
            # 等待通话建立
            time.sleep(duration)
            
            # 挂断电话
            subprocess.run(['adb', 'shell', 'input', 'keyevent', 'KEYCODE_ENDCALL'])
            time.sleep(2)
            
            # 保存结果
            self.results.append({
                'phone_number': phone_number,
                'mark_info': mark_info,
                'call_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'status': 'success'
            })
            
            print(f"通话完成: {phone_number} - {mark_info}")
            return True
            
        except Exception as e:
            print(f"拨号失败: {e}")
            self.results.append({
                'phone_number': phone_number,
                'mark_info': '拨号失败',
                'call_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'status': 'failed'
            })
            return False
    
    def _get_mark_info(self, phone_number: str) -> str:
        """
        获取号码标记信息
        通过截图和OCR识别
        """
        try:
            # 截图
            screenshot_path = f"screenshot_{phone_number}_{int(time.time())}.png"
            subprocess.run(['adb', 'shell', 'screencap', '/sdcard/screenshot.png'])
            subprocess.run(['adb', 'pull', '/sdcard/screenshot.png', screenshot_path])
            
            # OCR识别标记信息
            mark_info = self._ocr_mark_info(screenshot_path)
            
            # 清理截图文件
            if os.path.exists(screenshot_path):
                os.remove(screenshot_path)
                
            return mark_info
            
        except Exception as e:
            print(f"获取标记信息失败: {e}")
            return "获取失败"
    
    def _ocr_mark_info(self, image_path: str) -> str:
        """
        使用OCR识别标记信息
        """
        try:
            # 读取图片
            image = cv2.imread(image_path)
            if image is None:
                return "图片读取失败"
            
            # 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # 使用OCR识别文字
            text = pytesseract.image_to_string(gray, lang='chi_sim+eng')
            
            # 提取标记信息
            mark_info = self._extract_mark_from_text(text)
            
            return mark_info
            
        except Exception as e:
            print(f"OCR识别失败: {e}")
            return "OCR识别失败"
    
    def _extract_mark_from_text(self, text: str) -> str:
        """
        从OCR文本中提取标记信息
        """
        # 常见的标记模式
        patterns = [
            r'(\d+)\s*人标记为\s*(骚扰电话|诈骗电话|推销电话|快递送餐|客服电话)',
            r'(\d+)\s*人标记\s*(骚扰|诈骗|推销|快递|客服)',
            r'标记\s*(\d+)\s*次',
            r'(\d+)\s*次标记',
            r'(骚扰电话|诈骗电话|推销电话|快递送餐|客服电话)\s*(\d+)\s*人标记'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                if len(match.groups()) == 2:
                    return f"{match.group(1)}人标记为{match.group(2)}"
                elif len(match.groups()) == 1:
                    return f"{match.group(1)}次标记"
        
        # 如果没有找到标准格式，返回包含"标记"的文本
        lines = text.split('\n')
        for line in lines:
            if '标记' in line:
                return line.strip()
        
        return "未检测到标记信息"
    
    def batch_call(self, phone_list: List[str], interval: int = 5) -> List[Dict]:
        """
        批量拨打电话
        """
        print(f"开始批量拨号，共 {len(phone_list)} 个号码")
        
        for i, phone in enumerate(phone_list, 1):
            print(f"\n进度: {i}/{len(phone_list)}")
            self.make_call(phone)
            
            if i < len(phone_list):
                print(f"等待 {interval} 秒...")
                time.sleep(interval)
        
        return self.results
    
    def export_results(self, filename: str = None, format: str = 'excel') -> str:
        """
        导出结果
        """
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            if format == 'excel':
                filename = f"phone_mark_results_{timestamp}.xlsx"
            else:
                filename = f"phone_mark_results_{timestamp}.csv"
        
        if format == 'excel':
            df = pd.DataFrame(self.results)
            df.to_excel(filename, index=False)
        else:
            with open(filename, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=['phone_number', 'mark_info', 'call_time', 'status'])
                writer.writeheader()
                writer.writerows(self.results)
        
        print(f"结果已导出到: {filename}")
        return filename
    
    def run_from_file(self, file_path: str, interval: int = 5, output_format: str = 'excel'):
        """
        从文件读取号码并批量拨号
        """
        print(f"从文件读取号码: {file_path}")
        phone_numbers = self.read_phone_numbers(file_path)
        
        if not phone_numbers:
            print("没有找到有效的电话号码")
            return
        
        print(f"找到 {len(phone_numbers)} 个有效号码")
        
        # 批量拨号
        results = self.batch_call(phone_numbers, interval)
        
        # 导出结果
        self.export_results(format=output_format)
        
        # 显示统计
        success_count = len([r for r in results if r['status'] == 'success'])
        print(f"\n拨号完成！成功: {success_count}/{len(phone_numbers)}")

def main():
    """主函数"""
    dialer = PhoneAutoDialer()
    
    if not dialer.device_connected:
        print("请连接Android设备并开启USB调试")
        return
    
    print("=== 自动拨号标记工具 ===")
    print("1. 手动输入号码")
    print("2. 从文件读取号码")
    print("3. 退出")
    
    choice = input("\n请选择功能 (1-3): ").strip()
    
    if choice == '1':
        # 手动输入号码
        phones_input = input("请输入电话号码列表 (用逗号分隔): ").strip()
        phone_list = [p.strip() for p in phones_input.split(',') if p.strip()]
        
        if phone_list:
            interval = int(input("请输入拨号间隔(秒): ") or "5")
            dialer.batch_call(phone_list, interval)
            dialer.export_results()
        else:
            print("没有输入有效号码")
    
    elif choice == '2':
        # 从文件读取
        file_path = input("请输入文件路径 (支持 .txt, .csv, .xlsx): ").strip()
        
        if os.path.exists(file_path):
            interval = int(input("请输入拨号间隔(秒): ") or "5")
            output_format = input("输出格式 (excel/csv): ").strip() or "excel"
            
            dialer.run_from_file(file_path, interval, output_format)
        else:
            print("文件不存在")
    
    elif choice == '3':
        print("退出程序")
    else:
        print("无效选择")

if __name__ == "__main__":
    main() 