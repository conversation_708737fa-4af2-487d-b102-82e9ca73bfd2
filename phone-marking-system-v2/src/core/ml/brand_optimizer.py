"""
品牌特定的图像预处理优化器
采用保守的方式，逐步优化不同品牌的预处理参数
"""

import json
import os
import logging
from typing import Dict, Any, Optional
import cv2
import numpy as np


class BrandSpecificOptimizer:
    """品牌特定的预处理优化器"""
    
    def __init__(self, config_file: str = 'brand_configs.json'):
        self.config_file = config_file
        self.logger = logging.getLogger(__name__)
        self.brand_configs = self._load_brand_configs()
        self.performance_stats = self._load_performance_stats()
        
    def _load_brand_configs(self) -> Dict[str, Any]:
        """加载品牌配置，如果文件不存在则创建默认配置"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                self.logger.warning(f"加载品牌配置失败，使用默认配置: {e}")
        
        # 默认配置 - 保守的参数设置
        default_configs = {
            'default': {
                'brightness_adjust': 1.0,
                'contrast_enhance': False,
                'denoise_strength': 0.5,
                'preferred_methods': ['grayscale', 'adaptive_threshold'],
                'gaussian_blur_kernel': 3,
                'morphology_kernel': (3, 3),
                'adaptive_threshold_block_size': 11,
                'adaptive_threshold_c': 2
            },
            'huawei': {
                'brightness_adjust': 1.1,
                'contrast_enhance': True,
                'denoise_strength': 0.6,
                'preferred_methods': ['grayscale', 'adaptive_threshold', 'morphology'],
                'gaussian_blur_kernel': 3,
                'morphology_kernel': (3, 3),
                'adaptive_threshold_block_size': 13,
                'adaptive_threshold_c': 3
            },
            'xiaomi': {
                'brightness_adjust': 1.05,
                'contrast_enhance': False,
                'denoise_strength': 0.4,
                'preferred_methods': ['grayscale', 'gaussian_blur', 'adaptive_threshold'],
                'gaussian_blur_kernel': 5,
                'morphology_kernel': (2, 2),
                'adaptive_threshold_block_size': 11,
                'adaptive_threshold_c': 2
            },
            'samsung': {
                'brightness_adjust': 1.0,
                'contrast_enhance': True,
                'denoise_strength': 0.5,
                'preferred_methods': ['grayscale', 'contrast_enhance', 'adaptive_threshold'],
                'gaussian_blur_kernel': 3,
                'morphology_kernel': (3, 3),
                'adaptive_threshold_block_size': 11,
                'adaptive_threshold_c': 2
            },
            'oppo': {
                'brightness_adjust': 1.08,
                'contrast_enhance': False,
                'denoise_strength': 0.5,
                'preferred_methods': ['grayscale', 'adaptive_threshold'],
                'gaussian_blur_kernel': 3,
                'morphology_kernel': (3, 3),
                'adaptive_threshold_block_size': 11,
                'adaptive_threshold_c': 2
            },
            'vivo': {
                'brightness_adjust': 1.08,
                'contrast_enhance': False,
                'denoise_strength': 0.5,
                'preferred_methods': ['grayscale', 'adaptive_threshold'],
                'gaussian_blur_kernel': 3,
                'morphology_kernel': (3, 3),
                'adaptive_threshold_block_size': 11,
                'adaptive_threshold_c': 2
            }
        }
        
        # 保存默认配置
        self._save_brand_configs(default_configs)
        return default_configs
    
    def _load_performance_stats(self) -> Dict[str, Any]:
        """加载性能统计数据"""
        stats_file = 'brand_performance_stats.json'
        if os.path.exists(stats_file):
            try:
                with open(stats_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                self.logger.warning(f"加载性能统计失败: {e}")
        
        return {}
    
    def _save_brand_configs(self, configs: Dict[str, Any]):
        """保存品牌配置"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(configs, f, indent=2, ensure_ascii=False)
        except Exception as e:
            self.logger.error(f"保存品牌配置失败: {e}")
    
    def _save_performance_stats(self):
        """保存性能统计数据"""
        stats_file = 'brand_performance_stats.json'
        try:
            with open(stats_file, 'w', encoding='utf-8') as f:
                json.dump(self.performance_stats, f, indent=2, ensure_ascii=False)
        except Exception as e:
            self.logger.error(f"保存性能统计失败: {e}")
    
    def get_brand_config(self, device_info: Dict[str, Any]) -> Dict[str, Any]:
        """获取品牌特定配置"""
        if not device_info:
            return self.brand_configs['default']
        
        brand = device_info.get('brand', '').lower()
        
        # 品牌名称标准化
        brand_mapping = {
            'huawei': 'huawei',
            'honor': 'huawei',  # 荣耀使用华为配置
            'xiaomi': 'xiaomi',
            'redmi': 'xiaomi',  # 红米使用小米配置
            'samsung': 'samsung',
            'oppo': 'oppo',
            'oneplus': 'oppo',  # 一加使用OPPO配置
            'vivo': 'vivo',
            'iqoo': 'vivo'  # iQOO使用vivo配置
        }
        
        normalized_brand = brand_mapping.get(brand, 'default')
        config = self.brand_configs.get(normalized_brand, self.brand_configs['default']).copy()
        
        self.logger.info(f"为品牌 {brand} 使用配置: {normalized_brand}")
        return config
    
    def optimize_image(self, image: np.ndarray, device_info: Dict[str, Any]) -> np.ndarray:
        """根据品牌配置优化图像"""
        config = self.get_brand_config(device_info)
        
        try:
            # 应用品牌特定的预处理
            optimized_image = image.copy()
            
            # 亮度调整
            if config.get('brightness_adjust', 1.0) != 1.0:
                optimized_image = self._adjust_brightness(optimized_image, config['brightness_adjust'])
            
            # 对比度增强
            if config.get('contrast_enhance', False):
                optimized_image = self._enhance_contrast(optimized_image)
            
            # 降噪
            denoise_strength = config.get('denoise_strength', 0.5)
            if denoise_strength > 0:
                optimized_image = self._denoise(optimized_image, denoise_strength)
            
            return optimized_image
            
        except Exception as e:
            self.logger.error(f"图像优化失败: {e}")
            return image  # 失败时返回原图像，确保系统继续工作
    
    def _adjust_brightness(self, image: np.ndarray, factor: float) -> np.ndarray:
        """调整图像亮度"""
        return cv2.convertScaleAbs(image, alpha=factor, beta=0)
    
    def _enhance_contrast(self, image: np.ndarray) -> np.ndarray:
        """增强对比度"""
        lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
        l, a, b = cv2.split(lab)
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        l = clahe.apply(l)
        enhanced = cv2.merge([l, a, b])
        return cv2.cvtColor(enhanced, cv2.COLOR_LAB2BGR)
    
    def _denoise(self, image: np.ndarray, strength: float) -> np.ndarray:
        """图像降噪"""
        h = int(10 * strength)  # 控制降噪强度
        return cv2.fastNlMeansDenoisingColored(image, None, h, h, 7, 21)
    
    def get_preferred_methods(self, device_info: Dict[str, Any]) -> list:
        """获取品牌推荐的预处理方法"""
        config = self.get_brand_config(device_info)
        return config.get('preferred_methods', ['grayscale', 'adaptive_threshold'])
    
    def record_performance(self, device_info: Dict[str, Any], success: bool, confidence: float):
        """记录性能数据，用于后续优化"""
        if not device_info:
            return
        
        brand = device_info.get('brand', '').lower()
        if brand not in self.performance_stats:
            self.performance_stats[brand] = {
                'total_count': 0,
                'success_count': 0,
                'confidence_sum': 0.0,
                'avg_confidence': 0.0,
                'success_rate': 0.0
            }
        
        stats = self.performance_stats[brand]
        stats['total_count'] += 1
        stats['confidence_sum'] += confidence
        
        if success:
            stats['success_count'] += 1
        
        # 更新统计指标
        stats['success_rate'] = stats['success_count'] / stats['total_count']
        stats['avg_confidence'] = stats['confidence_sum'] / stats['total_count']
        
        # 定期保存统计数据
        if stats['total_count'] % 10 == 0:
            self._save_performance_stats()
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        return {
            'brand_stats': self.performance_stats,
            'total_brands': len(self.performance_stats),
            'recommendations': self._generate_recommendations()
        }
    
    def _generate_recommendations(self) -> Dict[str, str]:
        """基于性能数据生成优化建议"""
        recommendations = {}
        
        for brand, stats in self.performance_stats.items():
            if stats['total_count'] < 5:
                recommendations[brand] = "数据不足，需要更多样本"
            elif stats['success_rate'] < 0.8:
                recommendations[brand] = "成功率偏低，建议调整预处理参数"
            elif stats['avg_confidence'] < 0.7:
                recommendations[brand] = "置信度偏低，建议优化图像处理方法"
            else:
                recommendations[brand] = "性能良好"
        
        return recommendations


def test_brand_optimizer():
    """测试品牌优化器"""
    optimizer = BrandSpecificOptimizer()
    
    # 测试不同品牌的配置
    test_devices = [
        {'brand': 'huawei', 'model': 'MED-AL00'},
        {'brand': 'xiaomi', 'model': 'MI 10'},
        {'brand': 'samsung', 'model': 'Galaxy S21'},
        {'brand': 'unknown', 'model': 'Unknown'}
    ]
    
    for device in test_devices:
        config = optimizer.get_brand_config(device)
        methods = optimizer.get_preferred_methods(device)
        print(f"品牌: {device['brand']}")
        print(f"推荐方法: {methods}")
        print(f"亮度调整: {config['brightness_adjust']}")
        print("---")
    
    # 测试性能记录
    optimizer.record_performance({'brand': 'huawei'}, True, 0.95)
    optimizer.record_performance({'brand': 'xiaomi'}, False, 0.65)
    
    report = optimizer.get_performance_report()
    print("性能报告:", report)


if __name__ == '__main__':
    test_brand_optimizer()
