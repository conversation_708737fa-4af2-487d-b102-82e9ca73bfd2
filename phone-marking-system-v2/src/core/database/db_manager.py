import sqlite3

class LocationDBManager:
    def __init__(self, db_path='phone_marks.db'):
        self.db_path = db_path

    def get_mobile_info(self, phone: str) -> dict:
        """根据手机号前7位查找所有字段"""
        if len(phone) >= 7:
            segment = phone[:7]
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM mobile_segments WHERE segment=?', (segment,))
            row = cursor.fetchone()
            conn.close()
            if row:
                return dict(row)
        return {}

    def get_landline_info(self, phone: str) -> dict:
        """根据固话区号查找所有字段，优先4位，再3位"""
        for length in [4, 3]:
            if len(phone) > length:
                area_code = phone[:length]
                conn = sqlite3.connect(self.db_path)
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM landline_segments WHERE 地区区号=?', (area_code,))
                row = cursor.fetchone()
                conn.close()
                if row:
                    return dict(row)
        return {} 