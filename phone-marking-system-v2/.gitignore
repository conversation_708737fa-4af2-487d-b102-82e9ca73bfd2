# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo

# Database
*.db
*.db-shm
*.db-wal

# Logs
*.log
logs/

# Data
data/uploads/*
data/exports/*
data/temp/*
!data/uploads/.gitkeep
!data/exports/.gitkeep
!data/temp/.gitkeep

# Config
config/local_*.json
.env

# OS
.DS_Store
Thumbs.db

# Backup
backup_*/
