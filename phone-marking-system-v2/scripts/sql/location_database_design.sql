-- 归属地数据库设计方案
-- 为电话号码标记识别系统添加高效的归属地查询功能

-- 1. 手机号段归属地表
CREATE TABLE IF NOT EXISTS mobile_location (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    segment TEXT NOT NULL,           -- 号段前缀 (7位)
    phone_prefix TEXT NOT NULL,      -- 完整号码前缀
    province TEXT NOT NULL,          -- 省份
    city TEXT NOT NULL,              -- 城市
    isp TEXT,                        -- 运营商
    tel_code TEXT,                   -- 区号
    postal_code TEXT,                -- 邮编
    area_code TEXT,                  -- 地区代码
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(segment)
);

-- 为手机号段表创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_mobile_segment ON mobile_location(segment);
CREATE INDEX IF NOT EXISTS idx_mobile_province_city ON mobile_location(province, city);

-- 2. 固话区号归属地表
CREATE TABLE IF NOT EXISTS landline_location (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    area_code TEXT NOT NULL,         -- 地区区号 (3-4位)
    province TEXT NOT NULL,          -- 省份
    city TEXT NOT NULL,              -- 城市
    main_length INTEGER,             -- 主叫号码长度
    called_length TEXT,              -- 被叫号码长度
    region TEXT,                     -- 地域 (南方/北方)
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(area_code)
);

-- 为固话区号表创建索引
CREATE INDEX IF NOT EXISTS idx_landline_area_code ON landline_location(area_code);
CREATE INDEX IF NOT EXISTS idx_landline_province_city ON landline_location(province, city);

-- 3. 归属地查询缓存表 (可选，用于提高性能)
CREATE TABLE IF NOT EXISTS location_cache (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    phone_number TEXT NOT NULL,      -- 完整电话号码
    phone_type TEXT NOT NULL,        -- 号码类型: mobile/landline
    province TEXT NOT NULL,          -- 省份
    city TEXT NOT NULL,              -- 城市
    isp TEXT,                        -- 运营商 (仅手机号)
    area_code TEXT,                  -- 区号
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(phone_number)
);

-- 为缓存表创建索引
CREATE INDEX IF NOT EXISTS idx_cache_phone ON location_cache(phone_number);
CREATE INDEX IF NOT EXISTS idx_cache_updated ON location_cache(last_updated);

-- 4. 创建视图用于统一查询
CREATE VIEW IF NOT EXISTS phone_location_view AS
SELECT 
    phone_number,
    phone_type,
    province,
    city,
    isp,
    area_code,
    'cache' as source
FROM location_cache
UNION ALL
SELECT 
    NULL as phone_number,
    'mobile' as phone_type,
    province,
    city,
    isp,
    tel_code as area_code,
    'mobile_table' as source
FROM mobile_location
UNION ALL
SELECT 
    NULL as phone_number,
    'landline' as phone_type,
    province,
    city,
    NULL as isp,
    area_code,
    'landline_table' as source
FROM landline_location;

-- 5. 性能优化建议
-- 定期清理缓存表中的过期数据
-- DELETE FROM location_cache WHERE last_updated < datetime('now', '-30 days');

-- 6. 数据完整性约束
-- 确保省份和城市字段不为空
-- 区号格式验证可以在应用层实现
