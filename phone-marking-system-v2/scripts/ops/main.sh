#!/bin/bash

# =============================================================================
# 电话标记系统 - 智能运维管理主控制脚本
# 类似宝塔控制面板的一键启动和运维管理系统
# =============================================================================

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# 加载工具函数
source "$SCRIPT_DIR/utils/common.sh"
source "$SCRIPT_DIR/utils/colors.sh"
source "$SCRIPT_DIR/utils/spinner.sh"

# 加载配置
source "$SCRIPT_DIR/config/services.conf"

# 版本信息
VERSION="1.0.0"
SYSTEM_NAME="电话标记系统运维管理"

# 显示欢迎信息
show_welcome() {
    clear
    echo_color "$BLUE" "=================================================================="
    echo_color "$CYAN" "    $SYSTEM_NAME v$VERSION"
    echo_color "$GREEN" "    智能化服务管理 | 一键启动 | 自动监控 | 故障修复"
    echo_color "$BLUE" "=================================================================="
    echo ""
}

# 显示帮助信息
show_help() {
    show_welcome
    echo_color "$YELLOW" "使用方法："
    echo "  $0 <命令> [参数]"
    echo ""
    echo_color "$YELLOW" "可用命令："
    echo_color "$GREEN" "  start          一键启动所有服务"
    echo_color "$GREEN" "  stop           停止所有服务"
    echo_color "$GREEN" "  restart        重启所有服务"
    echo_color "$GREEN" "  status         查看服务状态"
    echo_color "$GREEN" "  health         健康检查"
    echo_color "$GREEN" "  repair         自动修复"
    echo_color "$GREEN" "  logs           查看日志"
    echo_color "$GREEN" "  panel          运维管理面板"
    echo_color "$GREEN" "  install        安装依赖"
    echo_color "$GREEN" "  update         更新系统"
    echo_color "$GREEN" "  backup         备份数据"
    echo_color "$GREEN" "  restore        恢复数据"
    echo ""
    echo_color "$YELLOW" "日志命令示例："
    echo "  $0 logs                    # 查看所有日志"
    echo "  $0 logs mysql              # 查看MySQL日志"
    echo "  $0 logs backend 1h         # 查看后端最近1小时日志"
    echo "  $0 logs advanced           # 进入高级日志管理"
    echo "  $0 logs monitor mysql      # 实时监控MySQL日志"
    echo "  $0 logs search ERROR       # 搜索错误日志"
    echo "  $0 logs analyze backend    # 分析后端错误"
    echo "  $0 logs rotate             # 执行日志轮转"
    echo ""
    echo_color "$YELLOW" "更多信息："
    echo "  $0 help                    # 显示此帮助"
    echo "  $0 version                 # 显示版本信息"
    echo ""
}

# 显示版本信息
show_version() {
    echo_color "$CYAN" "$SYSTEM_NAME"
    echo_color "$GREEN" "版本: $VERSION"
    echo_color "$YELLOW" "项目路径: $PROJECT_ROOT"
    echo_color "$YELLOW" "脚本路径: $SCRIPT_DIR"
    echo ""
}

# 检查权限
check_permissions() {
    if [[ $EUID -eq 0 ]]; then
        echo_color "$YELLOW" "⚠️  检测到以root用户运行，建议使用普通用户"
        read -p "是否继续？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
}

# 初始化环境
init_environment() {
    # 创建必要的目录
    mkdir -p "$SCRIPT_DIR/logs/system"
    mkdir -p "$SCRIPT_DIR/logs/services"
    mkdir -p "$SCRIPT_DIR/logs/operations"
    
    # 设置日志文件
    export SYSTEM_LOG="$SCRIPT_DIR/logs/system/system_$(date +%Y%m%d).log"
    export OPERATION_LOG="$SCRIPT_DIR/logs/operations/operations_$(date +%Y%m%d).log"
    
    # 记录操作
    log_operation "系统初始化" "用户: $(whoami), 命令: $0 $*"
}

# 主函数
main() {
    # 检查参数
    if [[ $# -eq 0 ]]; then
        show_help
        exit 0
    fi
    
    # 初始化
    check_permissions
    init_environment
    
    # 解析命令
    case "$1" in
        "start")
            show_welcome
            echo_color "$GREEN" "🚀 开始启动所有服务..."
            "$SCRIPT_DIR/core/service_manager.sh" start_all
            ;;
        "stop")
            show_welcome
            echo_color "$YELLOW" "🛑 停止所有服务..."
            "$SCRIPT_DIR/core/service_manager.sh" stop_all
            ;;
        "restart")
            show_welcome
            echo_color "$BLUE" "🔄 重启所有服务..."
            "$SCRIPT_DIR/core/service_manager.sh" restart_all
            ;;
        "status")
            show_welcome
            echo_color "$CYAN" "📊 查看服务状态..."
            "$SCRIPT_DIR/core/service_manager.sh" status_all
            ;;
        "health")
            show_welcome
            echo_color "$GREEN" "🏥 执行健康检查..."
            "$SCRIPT_DIR/core/health_checker.sh" check_all
            ;;
        "repair")
            show_welcome
            echo_color "$YELLOW" "🔧 执行自动修复..."
            "$SCRIPT_DIR/core/auto_repair.sh" repair_all
            ;;
        "logs")
            if [[ "$2" == "advanced" ]]; then
                "$SCRIPT_DIR/core/log_manager.sh" advanced
            elif [[ "$2" == "monitor" ]]; then
                "$SCRIPT_DIR/core/log_monitor.sh" start "${@:3}"
            elif [[ "$2" == "analyze" ]]; then
                "$SCRIPT_DIR/core/log_analyzer.sh" errors "$3" "$4"
            elif [[ "$2" == "search" ]]; then
                "$SCRIPT_DIR/core/log_analyzer.sh" search "$3" "$4" "$5"
            elif [[ "$2" == "rotate" ]]; then
                "$SCRIPT_DIR/core/log_rotator.sh" auto
            else
                "$SCRIPT_DIR/core/log_manager.sh" view "$2" "$3"
            fi
            ;;
        "panel")
            show_welcome
            echo_color "$PURPLE" "🎛️  启动运维管理面板..."
            "$SCRIPT_DIR/core/ops_panel.sh"
            ;;
        "install")
            show_welcome
            echo_color "$GREEN" "📦 安装系统依赖..."
            "$SCRIPT_DIR/modules/installer.sh" install_all
            ;;
        "update")
            show_welcome
            echo_color "$BLUE" "⬆️  更新系统..."
            "$SCRIPT_DIR/modules/updater.sh" update_all
            ;;
        "backup")
            show_welcome
            echo_color "$CYAN" "💾 备份系统数据..."
            "$SCRIPT_DIR/modules/backup.sh" backup_all
            ;;
        "restore")
            show_welcome
            echo_color "$YELLOW" "📥 恢复系统数据..."
            "$SCRIPT_DIR/modules/backup.sh" restore "$2"
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        "version"|"-v"|"--version")
            show_version
            ;;
        *)
            echo_color "$RED" "❌ 未知命令: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 捕获中断信号
trap 'echo_color "$YELLOW" "\n⚠️  操作被中断"; exit 130' INT TERM

# 执行主函数
main "$@"
