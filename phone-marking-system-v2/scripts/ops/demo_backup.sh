#!/bin/bash

# =============================================================================
# 备份恢复系统演示脚本
# 演示备份恢复功能的完整流程
# =============================================================================

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 加载依赖
source "$SCRIPT_DIR/utils/common.sh"
source "$SCRIPT_DIR/utils/colors.sh"
source "$SCRIPT_DIR/config/services.conf"

# 创建示例数据
create_sample_data() {
    echo_title "创建示例数据"
    
    # 创建示例配置文件
    echo_progress "创建示例配置文件..."
    mkdir -p "$SCRIPT_DIR/demo_config"
    
    cat > "$SCRIPT_DIR/demo_config/app.conf" << EOF
# 示例应用配置
app_name=phone_marking_system
version=1.0.0
debug=false
port=8080
database_url=mysql://localhost:3306/demo
redis_url=redis://localhost:6379/0
EOF
    
    cat > "$SCRIPT_DIR/demo_config/database.conf" << EOF
# 示例数据库配置
host=localhost
port=3306
username=root
password=
database=phone_marking_system
charset=utf8mb4
EOF
    
    echo_success "✅ 示例配置文件创建完成"
    
    # 创建示例日志文件
    echo_progress "创建示例日志文件..."
    mkdir -p "$SCRIPT_DIR/logs/demo"
    
    cat > "$SCRIPT_DIR/logs/demo/app_$(date +%Y%m%d).log" << EOF
[$(date '+%Y-%m-%d %H:%M:%S')] [INFO] 应用启动
[$(date '+%Y-%m-%d %H:%M:%S')] [INFO] 数据库连接成功
[$(date '+%Y-%m-%d %H:%M:%S')] [WARN] 内存使用率较高: 75%
[$(date '+%Y-%m-%d %H:%M:%S')] [ERROR] 处理请求失败: timeout
[$(date '+%Y-%m-%d %H:%M:%S')] [INFO] 处理完成: 1000条记录
EOF
    
    echo_success "✅ 示例日志文件创建完成"
    
    # 创建示例数据库数据
    echo_progress "创建示例数据库数据..."
    cat > "/tmp/demo_database.sql" << EOF
-- 示例数据库数据
CREATE DATABASE IF NOT EXISTS demo_backup;
USE demo_backup;

CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL,
    email VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

INSERT INTO users (username, email) VALUES
('admin', '<EMAIL>'),
('user1', '<EMAIL>'),
('user2', '<EMAIL>');

CREATE TABLE IF NOT EXISTS phone_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    phone_number VARCHAR(20) NOT NULL,
    location VARCHAR(100),
    operator VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

INSERT INTO phone_records (phone_number, location, operator) VALUES
('13800138000', '北京市', '中国移动'),
('13900139000', '上海市', '中国联通'),
('13700137000', '广州市', '中国电信');
EOF
    
    echo_success "✅ 示例数据库数据创建完成"
}

# 演示配置文件备份
demo_config_backup() {
    echo_title "演示配置文件备份"
    
    # 临时修改配置路径用于演示
    local original_backend_config="$PROJECT_ROOT/dash-fastapi-admin/dash-fastapi-backend/config"
    local demo_config_dir="$SCRIPT_DIR/demo_config"
    
    # 创建临时符号链接
    if [[ ! -d "$original_backend_config" ]]; then
        mkdir -p "$(dirname "$original_backend_config")"
        ln -sf "$demo_config_dir" "$original_backend_config"
        echo_info "创建临时配置链接用于演示"
    fi
    
    echo_subtitle "1. 执行配置文件备份"
    local backup_file=$("$SCRIPT_DIR/core/backup_manager.sh" config "demo_config_$(date +%Y%m%d_%H%M%S)")
    
    if [[ $? -eq 0 ]]; then
        echo_success "✅ 配置文件备份成功"
        echo_info "备份文件: $backup_file"
        
        echo ""
        echo_subtitle "2. 查看备份内容"
        if [[ -f "$backup_file" ]]; then
            echo_info "备份文件详情:"
            ls -lh "$backup_file"
            echo ""
            echo_info "备份文件内容预览:"
            tar -tzf "$backup_file" | head -10
        fi
    else
        echo_error "❌ 配置文件备份失败"
    fi
    
    # 清理临时链接
    if [[ -L "$original_backend_config" ]]; then
        rm -f "$original_backend_config"
        echo_info "清理临时配置链接"
    fi
    
    echo ""
    read -p "按回车键继续..."
}

# 演示日志文件备份
demo_logs_backup() {
    echo_title "演示日志文件备份"
    
    echo_subtitle "1. 执行日志文件备份"
    local backup_file=$("$SCRIPT_DIR/core/backup_manager.sh" logs "demo_logs_$(date +%Y%m%d_%H%M%S)" 1)
    
    if [[ $? -eq 0 ]]; then
        echo_success "✅ 日志文件备份成功"
        echo_info "备份文件: $backup_file"
        
        echo ""
        echo_subtitle "2. 查看备份内容"
        if [[ -f "$backup_file" ]]; then
            echo_info "备份文件详情:"
            ls -lh "$backup_file"
            echo ""
            echo_info "备份文件内容预览:"
            tar -tzf "$backup_file" | head -10
        fi
    else
        echo_error "❌ 日志文件备份失败"
    fi
    
    echo ""
    read -p "按回车键继续..."
}

# 演示数据库备份
demo_database_backup() {
    echo_title "演示数据库备份"
    
    # 检查MySQL连接
    if ! mysql -h"$MYSQL_HOST" -P"$MYSQL_PORT" -u"$MYSQL_USER" -e "SELECT 1" >/dev/null 2>&1; then
        echo_warning "⚠️  MySQL未连接，使用模拟数据演示"
        
        # 创建模拟备份文件
        local backup_dir="$SCRIPT_DIR/backups/database"
        mkdir -p "$backup_dir"
        local backup_file="$backup_dir/demo_database_$(date +%Y%m%d_%H%M%S).sql"
        
        cp "/tmp/demo_database.sql" "$backup_file"
        
        if [[ "$BACKUP_COMPRESS_ENABLED" == "true" ]] && command_exists gzip; then
            gzip "$backup_file"
            backup_file="${backup_file}.gz"
        fi
        
        echo_success "✅ 模拟数据库备份完成"
        echo_info "备份文件: $backup_file"
        
        local file_size=$(stat -c%s "$backup_file" 2>/dev/null || stat -f%z "$backup_file" 2>/dev/null || echo "0")
        show_key_value "文件大小" "$(format_size $file_size)"
    else
        echo_subtitle "1. 创建演示数据库"
        mysql -h"$MYSQL_HOST" -P"$MYSQL_PORT" -u"$MYSQL_USER" < "/tmp/demo_database.sql" 2>/dev/null
        echo_success "✅ 演示数据库创建完成"
        
        echo ""
        echo_subtitle "2. 执行数据库备份"
        local backup_file=$("$SCRIPT_DIR/core/backup_manager.sh" database "demo_database_$(date +%Y%m%d_%H%M%S)")
        
        if [[ $? -eq 0 ]]; then
            echo_success "✅ 数据库备份成功"
            echo_info "备份文件: $backup_file"
        else
            echo_error "❌ 数据库备份失败"
        fi
    fi
    
    echo ""
    read -p "按回车键继续..."
}

# 演示完整备份
demo_full_backup() {
    echo_title "演示完整备份"
    
    echo_subtitle "执行完整备份"
    local backup_dir=$("$SCRIPT_DIR/core/backup_manager.sh" full "demo_full_$(date +%Y%m%d_%H%M%S)")
    
    if [[ $? -eq 0 ]]; then
        echo_success "✅ 完整备份成功"
        echo_info "备份目录: $backup_dir"
        
        echo ""
        echo_subtitle "备份内容概览"
        if [[ -d "$backup_dir" ]]; then
            echo_info "备份目录结构:"
            ls -la "$backup_dir/"
            
            echo ""
            echo_info "备份清单:"
            if [[ -f "$backup_dir/backup_manifest.txt" ]]; then
                cat "$backup_dir/backup_manifest.txt"
            fi
        fi
    else
        echo_warning "⚠️  完整备份部分失败，请查看详细信息"
    fi
    
    echo ""
    read -p "按回车键继续..."
}

# 演示备份列表
demo_backup_list() {
    echo_title "演示备份列表"
    
    echo_subtitle "查看所有备份"
    "$SCRIPT_DIR/core/backup_manager.sh" list
    
    echo ""
    echo_subtitle "备份统计信息"
    "$SCRIPT_DIR/core/backup_manager.sh" stats
    
    echo ""
    read -p "按回车键继续..."
}

# 演示恢复功能
demo_restore() {
    echo_title "演示恢复功能"
    
    echo_subtitle "1. 列出可恢复的备份"
    "$SCRIPT_DIR/core/restore_manager.sh" list
    
    echo ""
    echo_subtitle "2. 配置文件恢复演示"
    
    # 查找最新的配置备份
    local latest_config_backup=$(find "$SCRIPT_DIR/backups/config" -name "*.tar.gz" | sort -r | head -1)
    
    if [[ -n "$latest_config_backup" ]]; then
        echo_info "找到配置备份: $(basename "$latest_config_backup")"
        echo_warning "⚠️  这是演示模式，不会实际恢复文件"
        echo_info "实际恢复命令: ./core/restore_manager.sh config $(basename "$latest_config_backup")"
    else
        echo_warning "未找到配置备份文件"
    fi
    
    echo ""
    echo_subtitle "3. 日志文件恢复演示"
    
    # 查找最新的日志备份
    local latest_log_backup=$(find "$SCRIPT_DIR/backups/logs" -name "*.tar.gz" | sort -r | head -1)
    
    if [[ -n "$latest_log_backup" ]]; then
        echo_info "找到日志备份: $(basename "$latest_log_backup")"
        echo_warning "⚠️  这是演示模式，不会实际恢复文件"
        echo_info "实际恢复命令: ./core/restore_manager.sh logs $(basename "$latest_log_backup")"
    else
        echo_warning "未找到日志备份文件"
    fi
    
    echo ""
    read -p "按回车键继续..."
}

# 演示备份清理
demo_backup_cleanup() {
    echo_title "演示备份清理"
    
    echo_subtitle "清理演示备份文件"
    
    if ask_confirmation "是否清理演示生成的备份文件？"; then
        # 清理演示备份
        find "$SCRIPT_DIR/backups" -name "*demo*" -type f -delete 2>/dev/null
        find "$SCRIPT_DIR/backups" -name "*demo*" -type d -exec rm -rf {} + 2>/dev/null
        
        echo_success "✅ 演示备份文件清理完成"
    else
        echo_info "保留演示备份文件"
    fi
    
    echo ""
    read -p "按回车键继续..."
}

# 清理演示数据
cleanup_demo_data() {
    echo_title "清理演示数据"
    
    if ask_confirmation "是否清理演示生成的数据？"; then
        # 清理示例配置文件
        rm -rf "$SCRIPT_DIR/demo_config"
        
        # 清理示例日志文件
        rm -rf "$SCRIPT_DIR/logs/demo"
        
        # 清理临时文件
        rm -f "/tmp/demo_database.sql"
        
        # 清理演示数据库
        if mysql -h"$MYSQL_HOST" -P"$MYSQL_PORT" -u"$MYSQL_USER" -e "SELECT 1" >/dev/null 2>&1; then
            mysql -h"$MYSQL_HOST" -P"$MYSQL_PORT" -u"$MYSQL_USER" -e "DROP DATABASE IF EXISTS demo_backup" 2>/dev/null
        fi
        
        echo_success "✅ 演示数据清理完成"
    else
        echo_info "保留演示数据"
    fi
}

# 完整演示
full_demo() {
    echo_color "$BLUE" "=================================================================="
    echo_color "$CYAN" "    电话标记系统 - 备份恢复系统演示"
    echo_color "$GREEN" "    智能备份 | 安全恢复 | 自动管理"
    echo_color "$BLUE" "=================================================================="
    echo ""
    
    echo_info "本演示将展示备份恢复系统的各项功能"
    echo_warning "演示过程中会生成示例数据，不会影响实际系统"
    echo ""
    
    if ask_confirmation "是否开始演示？"; then
        create_sample_data
        echo ""
        
        demo_config_backup
        demo_logs_backup
        demo_database_backup
        demo_full_backup
        demo_backup_list
        demo_restore
        demo_backup_cleanup
        cleanup_demo_data
        
        echo_done "🎉 备份恢复系统演示完成！"
        echo ""
        echo_subtitle "功能总结："
        echo_success "✅ 数据库备份恢复 - 支持压缩、增量备份"
        echo_success "✅ 配置文件备份恢复 - 保持目录结构"
        echo_success "✅ 日志文件备份恢复 - 按时间范围备份"
        echo_success "✅ 系统文件备份 - 完整系统状态保存"
        echo_success "✅ 完整备份 - 一键备份所有数据"
        echo_success "✅ 智能恢复 - 安全恢复机制"
        echo_success "✅ 自动管理 - 过期清理、统计分析"
        echo ""
        echo_info "您可以使用以下命令体验完整功能："
        echo "  ./main.sh panel           # 进入运维面板"
        echo "  ./main.sh backup full     # 执行完整备份"
        echo "  ./main.sh restore list    # 查看可恢复备份"
    else
        echo_info "演示已取消"
    fi
}

# 主函数
main() {
    case "${1:-demo}" in
        "demo"|"full")
            full_demo
            ;;
        "create")
            create_sample_data
            ;;
        "config")
            demo_config_backup
            ;;
        "logs")
            demo_logs_backup
            ;;
        "database")
            demo_database_backup
            ;;
        "backup")
            demo_full_backup
            ;;
        "list")
            demo_backup_list
            ;;
        "restore")
            demo_restore
            ;;
        "cleanup")
            cleanup_demo_data
            ;;
        *)
            echo_error "未知命令: $1"
            echo "用法: $0 {demo|create|config|logs|database|backup|list|restore|cleanup}"
            echo ""
            echo "命令说明:"
            echo "  demo/full      # 完整演示"
            echo "  create         # 创建示例数据"
            echo "  config         # 演示配置备份"
            echo "  logs           # 演示日志备份"
            echo "  database       # 演示数据库备份"
            echo "  backup         # 演示完整备份"
            echo "  list           # 演示备份列表"
            echo "  restore        # 演示恢复功能"
            echo "  cleanup        # 清理演示数据"
            exit 1
            ;;
    esac
}

# 如果直接执行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
