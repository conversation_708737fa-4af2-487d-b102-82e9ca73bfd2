#!/bin/bash

# =============================================================================
# 日志管理系统演示脚本
# 生成示例日志数据，演示日志管理功能
# =============================================================================

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 加载依赖
source "$SCRIPT_DIR/utils/common.sh"
source "$SCRIPT_DIR/utils/colors.sh"
source "$SCRIPT_DIR/config/services.conf"

# 生成示例日志数据
generate_sample_logs() {
    echo_title "生成示例日志数据"
    
    # 创建日志目录
    mkdir -p "$SCRIPT_DIR/logs/services"
    mkdir -p "$SCRIPT_DIR/logs/system"
    mkdir -p "$SCRIPT_DIR/logs/operations"
    mkdir -p "$SCRIPT_DIR/logs/errors"
    
    # 生成系统日志
    local system_log="$SCRIPT_DIR/logs/system/system_$(date +%Y%m%d).log"
    echo_progress "生成系统日志: $system_log"
    
    {
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] [INFO] [SYSTEM] 系统启动完成"
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] [INFO] [SYSTEM] 加载配置文件成功"
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] [WARN] [SYSTEM] CPU使用率较高: 75%"
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] [INFO] [SYSTEM] 内存使用率: 45%"
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] [ERROR] [SYSTEM] 磁盘空间不足警告"
    } > "$system_log"
    
    # 生成MySQL日志
    local mysql_log="$SCRIPT_DIR/logs/services/mysql_$(date +%Y%m%d).log"
    echo_progress "生成MySQL日志: $mysql_log"
    
    {
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] [INFO] [MYSQL] MySQL服务启动"
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] [INFO] [MYSQL] 连接数据库成功"
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] [WARN] [MYSQL] 慢查询检测: SELECT * FROM users (1250ms)"
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] [ERROR] [MYSQL] 连接池耗尽"
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] [INFO] [MYSQL] 查询执行成功 (45ms)"
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] [ERROR] [MYSQL] 数据库连接失败: Connection refused"
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] [INFO] [MYSQL] 自动重连成功"
    } > "$mysql_log"
    
    # 生成Redis日志
    local redis_log="$SCRIPT_DIR/logs/services/redis_$(date +%Y%m%d).log"
    echo_progress "生成Redis日志: $redis_log"
    
    {
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] [INFO] [REDIS] Redis服务启动"
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] [INFO] [REDIS] 缓存命中率: 85%"
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] [WARN] [REDIS] 内存使用率过高: 90%"
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] [INFO] [REDIS] 执行FLUSHDB命令"
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] [ERROR] [REDIS] 连接超时"
    } > "$redis_log"
    
    # 生成后端服务日志
    local backend_log="$SCRIPT_DIR/logs/services/backend_$(date +%Y%m%d).log"
    echo_progress "生成后端服务日志: $backend_log"
    
    {
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] [INFO] [BACKEND] FastAPI服务启动在端口9099"
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] [INFO] [BACKEND] API请求: GET /api/health (25ms)"
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] [INFO] [BACKEND] API请求: POST /api/phone/batch (150ms)"
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] [WARN] [BACKEND] API响应时间过长: POST /api/phone/process (1500ms)"
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] [ERROR] [BACKEND] 数据库连接失败"
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] [ERROR] [BACKEND] 文件上传失败: 文件大小超限"
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] [INFO] [BACKEND] 批量处理完成: 1000条记录"
    } > "$backend_log"
    
    # 生成前端服务日志
    local frontend_log="$SCRIPT_DIR/logs/services/frontend_$(date +%Y%m%d).log"
    echo_progress "生成前端服务日志: $frontend_log"
    
    {
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] [INFO] [FRONTEND] Dash应用启动在端口8089"
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] [INFO] [FRONTEND] 用户访问: /dashboard"
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] [INFO] [FRONTEND] 页面渲染完成 (200ms)"
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] [WARN] [FRONTEND] 静态资源加载缓慢"
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] [ERROR] [FRONTEND] JavaScript错误: Uncaught TypeError"
    } > "$frontend_log"
    
    # 生成操作日志
    local operation_log="$SCRIPT_DIR/logs/operations/operations_$(date +%Y%m%d).log"
    echo_progress "生成操作日志: $operation_log"
    
    {
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] 操作: 系统启动 | 详情: 用户: $(whoami), 命令: ./main.sh start"
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] 操作: 服务检查 | 详情: 检查所有服务状态"
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] 操作: 日志查看 | 详情: 查看MySQL日志"
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] 操作: 健康检查 | 详情: 执行全面健康检查"
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] 操作: 自动修复 | 详情: 修复MySQL连接问题"
    } > "$operation_log"
    
    # 生成错误日志
    local error_log="$SCRIPT_DIR/logs/errors/errors_$(date +%Y%m%d).log"
    echo_progress "生成错误日志: $error_log"
    
    {
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] ERROR [MYSQL] 数据库连接失败: Connection refused"
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] ERROR [BACKEND] 文件上传失败: 文件大小超限"
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] ERROR [FRONTEND] JavaScript错误: Uncaught TypeError"
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] ERROR [REDIS] 连接超时"
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] ERROR [SYSTEM] 磁盘空间不足警告"
    } > "$error_log"
    
    echo_success "✅ 示例日志数据生成完成"
}

# 演示基础日志查看功能
demo_basic_viewing() {
    echo_title "演示基础日志查看功能"
    
    echo_subtitle "1. 查看系统日志"
    "$SCRIPT_DIR/core/log_manager.sh" system
    
    echo ""
    read -p "按回车键继续..."
    
    echo_subtitle "2. 查看MySQL服务日志"
    "$SCRIPT_DIR/core/log_manager.sh" view mysql
    
    echo ""
    read -p "按回车键继续..."
    
    echo_subtitle "3. 查看操作日志"
    "$SCRIPT_DIR/core/log_manager.sh" operations
    
    echo ""
    read -p "按回车键继续..."
}

# 演示日志搜索功能
demo_log_search() {
    echo_title "演示日志搜索功能"
    
    echo_subtitle "1. 搜索错误日志"
    "$SCRIPT_DIR/core/log_analyzer.sh" search "ERROR"
    
    echo ""
    read -p "按回车键继续..."
    
    echo_subtitle "2. 在MySQL日志中搜索连接问题"
    "$SCRIPT_DIR/core/log_analyzer.sh" search "connection" "mysql"
    
    echo ""
    read -p "按回车键继续..."
    
    echo_subtitle "3. 搜索性能相关日志"
    "$SCRIPT_DIR/core/log_analyzer.sh" search "ms"
    
    echo ""
    read -p "按回车键继续..."
}

# 演示错误分析功能
demo_error_analysis() {
    echo_title "演示错误分析功能"
    
    echo_subtitle "1. 分析所有服务的错误"
    "$SCRIPT_DIR/core/log_analyzer.sh" errors
    
    echo ""
    read -p "按回车键继续..."
    
    echo_subtitle "2. 分析MySQL服务的错误"
    "$SCRIPT_DIR/core/log_analyzer.sh" errors "mysql"
    
    echo ""
    read -p "按回车键继续..."
}

# 演示趋势分析功能
demo_trend_analysis() {
    echo_title "演示趋势分析功能"
    
    echo_subtitle "1. 分析最近7天的日志趋势"
    "$SCRIPT_DIR/core/log_analyzer.sh" trends "" 7
    
    echo ""
    read -p "按回车键继续..."
    
    echo_subtitle "2. 分析MySQL服务的趋势"
    "$SCRIPT_DIR/core/log_analyzer.sh" trends "mysql" 7
    
    echo ""
    read -p "按回车键继续..."
}

# 演示日志轮转功能
demo_log_rotation() {
    echo_title "演示日志轮转功能"
    
    echo_subtitle "1. 查看日志文件统计"
    "$SCRIPT_DIR/core/log_rotator.sh" stats
    
    echo ""
    read -p "按回车键继续..."
    
    echo_subtitle "2. 压缩旧日志文件"
    "$SCRIPT_DIR/core/log_rotator.sh" compress "$SCRIPT_DIR/logs" 0
    
    echo ""
    read -p "按回车键继续..."
    
    echo_subtitle "3. 再次查看统计信息"
    "$SCRIPT_DIR/core/log_rotator.sh" stats
    
    echo ""
    read -p "按回车键继续..."
}

# 演示性能分析功能
demo_performance_analysis() {
    echo_title "演示性能分析功能"
    
    echo_subtitle "1. 分析后端服务性能"
    "$SCRIPT_DIR/core/log_analyzer.sh" performance "backend"
    
    echo ""
    read -p "按回车键继续..."
}

# 演示报告生成功能
demo_report_generation() {
    echo_title "演示报告生成功能"
    
    echo_subtitle "1. 生成综合分析报告"
    "$SCRIPT_DIR/core/log_analyzer.sh" report
    
    echo ""
    read -p "按回车键继续..."
}

# 完整演示
full_demo() {
    echo_color "$BLUE" "=================================================================="
    echo_color "$CYAN" "    电话标记系统 - 日志管理系统演示"
    echo_color "$GREEN" "    智能日志管理 | 实时监控 | 自动分析"
    echo_color "$BLUE" "=================================================================="
    echo ""
    
    echo_info "本演示将展示日志管理系统的各项功能"
    echo_warning "演示过程中会生成示例数据，不会影响实际系统"
    echo ""
    
    if ask_confirmation "是否开始演示？"; then
        generate_sample_logs
        echo ""
        
        demo_basic_viewing
        demo_log_search
        demo_error_analysis
        demo_trend_analysis
        demo_log_rotation
        demo_performance_analysis
        demo_report_generation
        
        echo_done "🎉 日志管理系统演示完成！"
        echo ""
        echo_subtitle "功能总结："
        echo_success "✅ 基础日志查看 - 支持时间过滤、服务筛选"
        echo_success "✅ 智能日志搜索 - 关键词搜索、正则表达式"
        echo_success "✅ 错误分析 - 自动统计、分类展示"
        echo_success "✅ 趋势分析 - 历史数据、图表展示"
        echo_success "✅ 日志轮转 - 自动压缩、清理过期文件"
        echo_success "✅ 性能分析 - 响应时间、性能指标"
        echo_success "✅ 报告生成 - HTML报告、数据导出"
        echo ""
        echo_info "您可以使用以下命令体验完整功能："
        echo "  ./main.sh logs advanced    # 进入高级日志管理"
        echo "  ./main.sh logs monitor     # 启动实时监控"
        echo "  ./main.sh logs search      # 搜索日志内容"
    else
        echo_info "演示已取消"
    fi
}

# 清理演示数据
cleanup_demo_data() {
    echo_title "清理演示数据"
    
    if ask_confirmation "确定要清理演示生成的日志数据吗？"; then
        local demo_files=(
            "$SCRIPT_DIR/logs/system/system_$(date +%Y%m%d).log"
            "$SCRIPT_DIR/logs/services/mysql_$(date +%Y%m%d).log"
            "$SCRIPT_DIR/logs/services/redis_$(date +%Y%m%d).log"
            "$SCRIPT_DIR/logs/services/backend_$(date +%Y%m%d).log"
            "$SCRIPT_DIR/logs/services/frontend_$(date +%Y%m%d).log"
            "$SCRIPT_DIR/logs/operations/operations_$(date +%Y%m%d).log"
            "$SCRIPT_DIR/logs/errors/errors_$(date +%Y%m%d).log"
        )
        
        for file in "${demo_files[@]}"; do
            if [[ -f "$file" ]]; then
                rm -f "$file"
                echo_success "✅ 已删除: $(basename "$file")"
            fi
        done
        
        # 清理压缩文件
        find "$SCRIPT_DIR/logs" -name "*.log.gz" -delete 2>/dev/null
        
        echo_success "演示数据清理完成"
    else
        echo_info "清理已取消"
    fi
}

# 主函数
main() {
    case "${1:-demo}" in
        "demo"|"full")
            full_demo
            ;;
        "generate")
            generate_sample_logs
            ;;
        "basic")
            demo_basic_viewing
            ;;
        "search")
            demo_log_search
            ;;
        "errors")
            demo_error_analysis
            ;;
        "trends")
            demo_trend_analysis
            ;;
        "rotation")
            demo_log_rotation
            ;;
        "performance")
            demo_performance_analysis
            ;;
        "report")
            demo_report_generation
            ;;
        "cleanup")
            cleanup_demo_data
            ;;
        *)
            echo_error "未知命令: $1"
            echo "用法: $0 {demo|generate|basic|search|errors|trends|rotation|performance|report|cleanup}"
            echo ""
            echo "命令说明:"
            echo "  demo/full      # 完整演示"
            echo "  generate       # 生成示例数据"
            echo "  basic          # 演示基础查看"
            echo "  search         # 演示搜索功能"
            echo "  errors         # 演示错误分析"
            echo "  trends         # 演示趋势分析"
            echo "  rotation       # 演示日志轮转"
            echo "  performance    # 演示性能分析"
            echo "  report         # 演示报告生成"
            echo "  cleanup        # 清理演示数据"
            exit 1
            ;;
    esac
}

# 如果直接执行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
