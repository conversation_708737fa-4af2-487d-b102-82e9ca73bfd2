# =============================================================================
# 服务配置文件
# 定义所有服务的启动参数、健康检查、依赖关系等
# =============================================================================

# 项目根目录
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# =============================================================================
# 基础服务配置
# =============================================================================

# MySQL数据库
MYSQL_SERVICE_NAME="mysql"
MYSQL_HOST="127.0.0.1"
MYSQL_PORT="3306"
MYSQL_USER="root"
MYSQL_PASSWORD=""
MYSQL_DATABASE="phone_marking_system"
# 跨平台服务管理命令
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS使用brew services
    MYSQL_START_CMD="brew services start mysql"
    MYSQL_STOP_CMD="brew services stop mysql"
    MYSQL_STATUS_CMD="brew services list | grep mysql | grep started"
else
    # Linux使用systemctl
    MYSQL_START_CMD="sudo systemctl start mysql"
    MYSQL_STOP_CMD="sudo systemctl stop mysql"
    MYSQL_STATUS_CMD="sudo systemctl is-active mysql"
fi
MYSQL_HEALTH_CHECK="mysql -h$MYSQL_HOST -P$MYSQL_PORT -u$MYSQL_USER -e 'SELECT 1' 2>/dev/null"

# Redis缓存
REDIS_SERVICE_NAME="redis"
REDIS_HOST="127.0.0.1"
REDIS_PORT="6379"
REDIS_PASSWORD=""
REDIS_DATABASE="2"
# 跨平台服务管理命令
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS使用brew services
    REDIS_START_CMD="brew services start redis"
    REDIS_STOP_CMD="brew services stop redis"
    REDIS_STATUS_CMD="brew services list | grep redis | grep started"
else
    # Linux使用systemctl
    REDIS_START_CMD="sudo systemctl start redis"
    REDIS_STOP_CMD="sudo systemctl stop redis"
    REDIS_STATUS_CMD="sudo systemctl is-active redis"
fi
REDIS_HEALTH_CHECK="redis-cli -h $REDIS_HOST -p $REDIS_PORT ping"

# =============================================================================
# 应用服务配置
# =============================================================================

# FastAPI后端服务
BACKEND_SERVICE_NAME="backend"
BACKEND_HOST="0.0.0.0"
BACKEND_PORT="9099"
BACKEND_DIR="$PROJECT_ROOT/dash-fastapi-admin/dash-fastapi-backend"
BACKEND_START_CMD="cd $BACKEND_DIR && python3 -m uvicorn app:app --host 0.0.0.0 --port 9099"
BACKEND_STOP_CMD="pkill -f 'python.*app.py'"
BACKEND_HEALTH_CHECK="curl -s http://$BACKEND_HOST:$BACKEND_PORT/health"
BACKEND_LOG_FILE="$SCRIPT_DIR/logs/services/backend_$(date +%Y%m%d).log"

# Dash前端服务
FRONTEND_SERVICE_NAME="frontend"
FRONTEND_HOST="127.0.0.1"
FRONTEND_PORT="8089"
FRONTEND_DIR="$PROJECT_ROOT/dash-fastapi-admin/dash-fastapi-frontend"
FRONTEND_START_CMD="cd $FRONTEND_DIR && python app.py"
FRONTEND_STOP_CMD="pkill -f 'python.*dash.*app.py'"
FRONTEND_HEALTH_CHECK="curl -s http://$FRONTEND_HOST:$FRONTEND_PORT/"
FRONTEND_LOG_FILE="$SCRIPT_DIR/logs/services/frontend_$(date +%Y%m%d).log"

# =============================================================================
# 微服务配置（可选）
# =============================================================================

# 归属地查询微服务
LOCATION_SERVICE_NAME="location"
LOCATION_HOST="127.0.0.1"
LOCATION_PORT="8001"
LOCATION_DIR="$PROJECT_ROOT/src/services/location"
LOCATION_START_CMD="cd $LOCATION_DIR && python location_microservice.py"
LOCATION_STOP_CMD="pkill -f 'location_microservice.py'"
LOCATION_HEALTH_CHECK="curl -s http://$LOCATION_HOST:$LOCATION_PORT/health"
LOCATION_LOG_FILE="$SCRIPT_DIR/logs/services/location_$(date +%Y%m%d).log"

# NLP文本分析微服务
NLP_SERVICE_NAME="nlp"
NLP_HOST="127.0.0.1"
NLP_PORT="8002"
NLP_DIR="$PROJECT_ROOT/src/services/nlp"
NLP_START_CMD="cd $NLP_DIR && python3 nlp_microservice.py"
NLP_STOP_CMD="pkill -f 'nlp_microservice.py'"
NLP_HEALTH_CHECK="curl -s http://$NLP_HOST:$NLP_PORT/health"
NLP_LOG_FILE="$SCRIPT_DIR/logs/services/nlp_$(date +%Y%m%d).log"

# 批量处理服务
BATCH_SERVICE_NAME="batch"
BATCH_HOST="127.0.0.1"
BATCH_PORT="8003"
BATCH_DIR="$PROJECT_ROOT/src/services/batch"
BATCH_START_CMD="cd $BATCH_DIR && python3 batch_processing_service.py"
BATCH_STOP_CMD="pkill -f 'batch_processing_service.py'"
BATCH_HEALTH_CHECK="curl -s http://$BATCH_HOST:$BATCH_PORT/health"
BATCH_LOG_FILE="$SCRIPT_DIR/logs/services/batch_$(date +%Y%m%d).log"

# API网关
GATEWAY_SERVICE_NAME="gateway"
GATEWAY_HOST="127.0.0.1"
GATEWAY_PORT="8000"
GATEWAY_DIR="$PROJECT_ROOT/src/services/gateway"
GATEWAY_START_CMD="cd $GATEWAY_DIR && python api_gateway.py"
GATEWAY_STOP_CMD="pkill -f 'api_gateway.py'"
GATEWAY_HEALTH_CHECK="curl -s http://$GATEWAY_HOST:$GATEWAY_PORT/health"
GATEWAY_LOG_FILE="$SCRIPT_DIR/logs/services/gateway_$(date +%Y%m%d).log"

# =============================================================================
# 服务启动顺序和依赖关系
# =============================================================================

# 必需服务（按启动顺序）
REQUIRED_SERVICES=(
    "mysql"
    "redis"
    "backend"
    "frontend"
)

# 可选微服务（按启动顺序）
OPTIONAL_SERVICES=(
    "location"
    "nlp"
    "batch"
    "gateway"
)

# 所有服务
ALL_SERVICES=("${REQUIRED_SERVICES[@]}" "${OPTIONAL_SERVICES[@]}")

# 服务依赖关系
# 兼容性声明：支持bash 3.x和4.x以及不同操作系统
if [[ "${BASH_VERSION%%.*}" -ge 4 ]] 2>/dev/null && command -v declare >/dev/null 2>&1; then
    # bash 4.x+ 支持关联数组
    declare -A SERVICE_DEPENDENCIES 2>/dev/null || true
    SERVICE_DEPENDENCIES["backend"]="mysql redis"
    SERVICE_DEPENDENCIES["frontend"]="backend"
    SERVICE_DEPENDENCIES["location"]="mysql redis"
    SERVICE_DEPENDENCIES["nlp"]="mysql redis"
    SERVICE_DEPENDENCIES["batch"]="mysql redis backend"
    SERVICE_DEPENDENCIES["gateway"]="backend location nlp batch"
else
    # bash 3.x 或其他shell兼容模式，使用函数模拟关联数组
    get_service_dependencies() {
        local service="$1"
        case "$service" in
            "backend") echo "mysql redis" ;;
            "frontend") echo "backend" ;;
            "location") echo "mysql redis" ;;
            "nlp") echo "mysql redis" ;;
            "batch") echo "mysql redis backend" ;;
            "gateway") echo "backend location nlp batch" ;;
            *) echo "" ;;
        esac
    }
fi

# =============================================================================
# 监控配置
# =============================================================================

# 健康检查间隔（秒）
HEALTH_CHECK_INTERVAL=30

# 服务启动超时（秒）
SERVICE_START_TIMEOUT=60

# 服务停止超时（秒）
SERVICE_STOP_TIMEOUT=30

# 自动重启最大次数
MAX_RESTART_ATTEMPTS=3

# 重启间隔（秒）
RESTART_INTERVAL=10

# =============================================================================
# 日志配置
# =============================================================================

# 日志保留天数
LOG_RETENTION_DAYS=7

# 日志文件最大大小（MB）
LOG_MAX_SIZE=100

# 是否启用日志轮转
LOG_ROTATION_ENABLED=true

# =============================================================================
# 备份配置
# =============================================================================

# 备份目录
BACKUP_DIR="$SCRIPT_DIR/backups"

# 备份保留天数
BACKUP_RETENTION_DAYS=30

# 数据库备份
DB_BACKUP_ENABLED=true

# 配置文件备份
CONFIG_BACKUP_ENABLED=true

# 日志备份
LOG_BACKUP_ENABLED=false

# =============================================================================
# 告警配置
# =============================================================================

# 是否启用告警
ALERT_ENABLED=true

# 告警方式（email, webhook, log）
ALERT_METHODS="log"

# 邮件告警配置
ALERT_EMAIL=""
SMTP_SERVER=""
SMTP_PORT="587"
SMTP_USER=""
SMTP_PASSWORD=""

# Webhook告警配置
ALERT_WEBHOOK_URL=""

# =============================================================================
# 性能监控配置
# =============================================================================

# CPU使用率告警阈值（%）
CPU_ALERT_THRESHOLD=80

# 内存使用率告警阈值（%）
MEMORY_ALERT_THRESHOLD=85

# 磁盘使用率告警阈值（%）
DISK_ALERT_THRESHOLD=90

# 网络连接数告警阈值
CONNECTION_ALERT_THRESHOLD=1000
