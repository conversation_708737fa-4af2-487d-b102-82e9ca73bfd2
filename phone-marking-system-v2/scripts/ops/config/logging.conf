# =============================================================================
# 日志管理配置文件
# 定义日志级别、格式、轮转策略等配置
# =============================================================================

# =============================================================================
# 日志级别配置
# =============================================================================

# 全局日志级别 (DEBUG, INFO, WARN, ERROR, CRITICAL)
GLOBAL_LOG_LEVEL="INFO"

# 各服务日志级别
MYSQL_LOG_LEVEL="WARN"
REDIS_LOG_LEVEL="WARN"
BACKEND_LOG_LEVEL="INFO"
FRONTEND_LOG_LEVEL="INFO"
LOCATION_LOG_LEVEL="INFO"
NLP_LOG_LEVEL="INFO"
BATCH_LOG_LEVEL="INFO"
GATEWAY_LOG_LEVEL="INFO"

# =============================================================================
# 日志格式配置
# =============================================================================

# 标准日志格式
LOG_FORMAT_STANDARD="[%Y-%m-%d %H:%M:%S] [%LEVEL%] [%SERVICE%] %MESSAGE%"

# 详细日志格式（包含文件名和行号）
LOG_FORMAT_DETAILED="[%Y-%m-%d %H:%M:%S] [%LEVEL%] [%SERVICE%] [%FILE%:%LINE%] %MESSAGE%"

# JSON格式日志
LOG_FORMAT_JSON='{"timestamp":"%Y-%m-%d %H:%M:%S","level":"%LEVEL%","service":"%SERVICE%","message":"%MESSAGE%"}'

# 简单格式日志
LOG_FORMAT_SIMPLE="[%H:%M:%S] %LEVEL% %MESSAGE%"

# 默认使用的格式
DEFAULT_LOG_FORMAT="$LOG_FORMAT_STANDARD"

# =============================================================================
# 日志文件配置
# =============================================================================

# 日志根目录
LOG_ROOT_DIR="$SCRIPT_DIR/logs"

# 各类日志子目录
LOG_SYSTEM_DIR="$LOG_ROOT_DIR/system"
LOG_SERVICES_DIR="$LOG_ROOT_DIR/services"
LOG_OPERATIONS_DIR="$LOG_ROOT_DIR/operations"
LOG_ACCESS_DIR="$LOG_ROOT_DIR/access"
LOG_ERROR_DIR="$LOG_ROOT_DIR/errors"
LOG_AUDIT_DIR="$LOG_ROOT_DIR/audit"
LOG_PERFORMANCE_DIR="$LOG_ROOT_DIR/performance"
LOG_SECURITY_DIR="$LOG_ROOT_DIR/security"

# 日志文件命名模式
LOG_FILE_PATTERN="%SERVICE%_%Y%m%d.log"
LOG_ERROR_FILE_PATTERN="%SERVICE%_error_%Y%m%d.log"
LOG_ACCESS_FILE_PATTERN="%SERVICE%_access_%Y%m%d.log"

# =============================================================================
# 日志轮转配置
# =============================================================================

# 按大小轮转
LOG_ROTATE_SIZE_ENABLED=true
LOG_MAX_SIZE_MB=100
LOG_ROTATE_COUNT=5

# 按时间轮转
LOG_ROTATE_TIME_ENABLED=true
LOG_ROTATE_INTERVAL="daily"  # daily, weekly, monthly

# 压缩配置
LOG_COMPRESS_ENABLED=true
LOG_COMPRESS_DELAY_DAYS=1
LOG_COMPRESS_ALGORITHM="gzip"  # gzip, bzip2, xz

# 清理配置
LOG_CLEANUP_ENABLED=true
LOG_RETENTION_DAYS=30
LOG_CLEANUP_SCHEDULE="0 2 * * *"  # 每天凌晨2点执行清理

# =============================================================================
# 日志输出配置
# =============================================================================

# 控制台输出
CONSOLE_OUTPUT_ENABLED=true
CONSOLE_LOG_LEVEL="INFO"
CONSOLE_COLOR_ENABLED=true

# 文件输出
FILE_OUTPUT_ENABLED=true
FILE_LOG_LEVEL="DEBUG"

# 远程日志服务器（可选）
REMOTE_LOG_ENABLED=false
REMOTE_LOG_SERVER=""
REMOTE_LOG_PORT=514
REMOTE_LOG_PROTOCOL="UDP"  # UDP, TCP

# =============================================================================
# 日志过滤配置
# =============================================================================

# 敏感信息过滤
SENSITIVE_DATA_FILTER_ENABLED=true
SENSITIVE_PATTERNS=(
    "password"
    "token"
    "secret"
    "key"
    "credential"
    "authorization"
)

# IP地址脱敏
IP_MASKING_ENABLED=false
IP_MASK_PATTERN="xxx.xxx.xxx.xxx"

# 用户信息脱敏
USER_MASKING_ENABLED=false
USER_MASK_PATTERN="user_***"

# =============================================================================
# 日志监控配置
# =============================================================================

# 错误监控
ERROR_MONITORING_ENABLED=true
ERROR_THRESHOLD_PER_MINUTE=10
ERROR_ALERT_ENABLED=true

# 性能监控
PERFORMANCE_MONITORING_ENABLED=true
SLOW_LOG_THRESHOLD_MS=1000

# 安全监控
SECURITY_MONITORING_ENABLED=true
FAILED_LOGIN_THRESHOLD=5
SUSPICIOUS_ACTIVITY_PATTERNS=(
    "SQL injection"
    "XSS attack"
    "brute force"
    "unauthorized access"
)

# =============================================================================
# 日志分析配置
# =============================================================================

# 自动分析
AUTO_ANALYSIS_ENABLED=true
ANALYSIS_SCHEDULE="0 1 * * *"  # 每天凌晨1点执行分析

# 报告生成
REPORT_GENERATION_ENABLED=true
REPORT_SCHEDULE="0 6 * * 1"  # 每周一早上6点生成报告
REPORT_FORMAT="HTML"  # HTML, PDF, JSON

# 趋势分析
TREND_ANALYSIS_ENABLED=true
TREND_ANALYSIS_DAYS=30

# =============================================================================
# 告警配置
# =============================================================================

# 邮件告警
EMAIL_ALERT_ENABLED=false
EMAIL_SMTP_SERVER=""
EMAIL_SMTP_PORT=587
EMAIL_USERNAME=""
EMAIL_PASSWORD=""
EMAIL_FROM=""
EMAIL_TO=""

# Webhook告警
WEBHOOK_ALERT_ENABLED=false
WEBHOOK_URL=""
WEBHOOK_TIMEOUT=30

# 短信告警
SMS_ALERT_ENABLED=false
SMS_API_URL=""
SMS_API_KEY=""
SMS_PHONE_NUMBERS=""

# =============================================================================
# 性能优化配置
# =============================================================================

# 异步日志写入
ASYNC_LOGGING_ENABLED=true
LOG_BUFFER_SIZE=1024
LOG_FLUSH_INTERVAL=5

# 日志缓存
LOG_CACHE_ENABLED=true
LOG_CACHE_SIZE_MB=50

# 批量写入
BATCH_WRITE_ENABLED=true
BATCH_SIZE=100
BATCH_TIMEOUT=10

# =============================================================================
# 调试配置
# =============================================================================

# 调试模式
DEBUG_MODE_ENABLED=false
DEBUG_LOG_LEVEL="DEBUG"
DEBUG_TRACE_ENABLED=false

# 详细错误信息
VERBOSE_ERROR_ENABLED=true
STACK_TRACE_ENABLED=true

# 性能分析
PROFILING_ENABLED=false
PROFILING_SAMPLE_RATE=0.1

# =============================================================================
# 集成配置
# =============================================================================

# ELK Stack集成
ELK_INTEGRATION_ENABLED=false
ELASTICSEARCH_URL=""
LOGSTASH_HOST=""
KIBANA_URL=""

# Grafana集成
GRAFANA_INTEGRATION_ENABLED=false
GRAFANA_URL=""
GRAFANA_API_KEY=""

# Prometheus集成
PROMETHEUS_INTEGRATION_ENABLED=false
PROMETHEUS_PUSHGATEWAY=""

# =============================================================================
# 自定义配置
# =============================================================================

# 自定义日志处理器
CUSTOM_HANDLERS_ENABLED=false
CUSTOM_HANDLERS_DIR="$SCRIPT_DIR/handlers"

# 自定义过滤器
CUSTOM_FILTERS_ENABLED=false
CUSTOM_FILTERS_DIR="$SCRIPT_DIR/filters"

# 自定义格式化器
CUSTOM_FORMATTERS_ENABLED=false
CUSTOM_FORMATTERS_DIR="$SCRIPT_DIR/formatters"

# =============================================================================
# 环境特定配置
# =============================================================================

# 开发环境
if [[ "$APP_ENV" == "development" ]]; then
    GLOBAL_LOG_LEVEL="DEBUG"
    CONSOLE_OUTPUT_ENABLED=true
    FILE_OUTPUT_ENABLED=true
    LOG_RETENTION_DAYS=7
fi

# 测试环境
if [[ "$APP_ENV" == "testing" ]]; then
    GLOBAL_LOG_LEVEL="INFO"
    CONSOLE_OUTPUT_ENABLED=false
    FILE_OUTPUT_ENABLED=true
    LOG_RETENTION_DAYS=3
fi

# 生产环境
if [[ "$APP_ENV" == "production" ]]; then
    GLOBAL_LOG_LEVEL="WARN"
    CONSOLE_OUTPUT_ENABLED=false
    FILE_OUTPUT_ENABLED=true
    LOG_RETENTION_DAYS=30
    ERROR_MONITORING_ENABLED=true
    EMAIL_ALERT_ENABLED=true
fi

# =============================================================================
# 配置验证
# =============================================================================

# 验证日志目录
validate_log_directories() {
    local dirs=(
        "$LOG_SYSTEM_DIR"
        "$LOG_SERVICES_DIR"
        "$LOG_OPERATIONS_DIR"
        "$LOG_ACCESS_DIR"
        "$LOG_ERROR_DIR"
        "$LOG_AUDIT_DIR"
        "$LOG_PERFORMANCE_DIR"
        "$LOG_SECURITY_DIR"
    )
    
    for dir in "${dirs[@]}"; do
        if [[ ! -d "$dir" ]]; then
            mkdir -p "$dir"
        fi
    done
}

# 验证配置参数
validate_log_config() {
    # 验证日志级别
    local valid_levels=("DEBUG" "INFO" "WARN" "ERROR" "CRITICAL")
    if [[ ! " ${valid_levels[*]} " =~ " $GLOBAL_LOG_LEVEL " ]]; then
        echo "警告: 无效的日志级别 $GLOBAL_LOG_LEVEL，使用默认值 INFO"
        GLOBAL_LOG_LEVEL="INFO"
    fi
    
    # 验证文件大小
    if [[ ! "$LOG_MAX_SIZE_MB" =~ ^[0-9]+$ ]] || [[ $LOG_MAX_SIZE_MB -lt 1 ]]; then
        echo "警告: 无效的日志文件大小 $LOG_MAX_SIZE_MB，使用默认值 100MB"
        LOG_MAX_SIZE_MB=100
    fi
    
    # 验证保留天数
    if [[ ! "$LOG_RETENTION_DAYS" =~ ^[0-9]+$ ]] || [[ $LOG_RETENTION_DAYS -lt 1 ]]; then
        echo "警告: 无效的日志保留天数 $LOG_RETENTION_DAYS，使用默认值 30天"
        LOG_RETENTION_DAYS=30
    fi
}

# 初始化日志配置
init_log_config() {
    validate_log_directories
    validate_log_config
    
    # 设置环境变量
    export LOG_ROOT_DIR
    export GLOBAL_LOG_LEVEL
    export LOG_MAX_SIZE_MB
    export LOG_RETENTION_DAYS
}

# 如果被source，则自动初始化
if [[ "${BASH_SOURCE[0]}" != "${0}" ]]; then
    init_log_config
fi
