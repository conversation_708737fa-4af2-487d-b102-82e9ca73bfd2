# 运维管理系统完整指南

## 🎯 系统概述

电话标记系统的运维管理系统是一个类似宝塔控制面板的智能化运维解决方案，提供：

- **🚀 一键启动** - 智能检测环境，按顺序启动所有服务
- **📊 实时监控** - 系统资源、服务状态、性能指标监控
- **🔧 自动修复** - 服务异常自动检测和修复
- **📝 日志管理** - 智能日志收集、分析、搜索、轮转
- **💾 备份恢复** - 数据库、配置、日志、系统文件备份恢复
- **🎛️ 运维面板** - 交互式管理界面，功能丰富

## 📁 系统架构

```
scripts/ops/
├── main.sh                   # 主控制脚本
├── install.sh                # 一键安装脚本
├── demo_logs.sh              # 日志演示脚本
├── config/                   # 配置文件
│   ├── services.conf         # 服务配置
│   └── logging.conf          # 日志配置
├── core/                     # 核心功能模块
│   ├── service_manager.sh    # 服务管理
│   ├── health_checker.sh     # 健康检查
│   ├── auto_repair.sh        # 自动修复
│   ├── log_manager.sh        # 日志管理
│   ├── log_analyzer.sh       # 日志分析
│   ├── log_rotator.sh        # 日志轮转
│   ├── log_monitor.sh        # 日志监控
│   ├── backup_manager.sh     # 备份管理
│   ├── restore_manager.sh    # 恢复管理
│   └── ops_panel.sh          # 运维面板
├── utils/                    # 工具函数
│   ├── common.sh             # 通用函数
│   ├── colors.sh             # 颜色输出
│   └── spinner.sh            # 进度显示
├── logs/                     # 日志目录
│   ├── system/               # 系统日志
│   ├── services/             # 服务日志
│   ├── operations/           # 操作日志
│   ├── errors/               # 错误日志
│   └── reports/              # 分析报告
└── backups/                  # 备份目录
    ├── database/             # 数据库备份
    ├── config/               # 配置备份
    ├── logs/                 # 日志备份
    ├── system/               # 系统备份
    └── full/                 # 完整备份
```

## 🚀 快速开始

### 一键安装
```bash
# 进入脚本目录
cd phone-marking-system-v2/scripts/ops

# 运行安装脚本
./install.sh

# 启动所有服务
./main.sh start
```

### 基础命令
```bash
# 服务管理
./main.sh start          # 启动所有服务
./main.sh stop           # 停止所有服务
./main.sh restart        # 重启所有服务
./main.sh status         # 查看服务状态

# 健康检查和修复
./main.sh health         # 健康检查
./main.sh repair         # 自动修复

# 运维面板
./main.sh panel          # 启动运维面板
```

## 📊 功能详解

### 1. 服务管理

#### 智能启动
- 自动检测环境依赖
- 按依赖关系顺序启动
- 实时显示启动进度
- 启动失败自动回滚

#### 服务监控
- 实时状态检查
- 端口连通性检测
- 健康检查接口调用
- 进程存活监控

#### 依赖管理
- 自动解析服务依赖
- 依赖服务优先启动
- 依赖异常自动修复

### 2. 系统监控

#### 资源监控
- CPU使用率监控
- 内存使用率监控
- 磁盘空间监控
- 网络连接监控

#### 性能监控
- 响应时间监控
- 吞吐量统计
- 错误率统计
- 性能趋势分析

#### 告警机制
- 阈值告警
- 邮件通知
- Webhook通知
- 短信通知

### 3. 日志管理

#### 智能收集
- 自动收集各服务日志
- 统一日志格式
- 分类存储管理
- 实时日志流

#### 高级分析
- 关键词搜索
- 错误统计分析
- 趋势分析
- 性能分析
- 异常模式识别

#### 自动维护
- 按大小轮转
- 按时间轮转
- 自动压缩
- 过期清理

### 4. 备份恢复

#### 数据库备份
```bash
# 备份数据库
./main.sh backup database

# 恢复数据库
./main.sh restore database backup_file.sql.gz
```

#### 配置文件备份
```bash
# 备份配置文件
./main.sh backup config

# 恢复配置文件
./main.sh restore config config_backup.tar.gz
```

#### 日志文件备份
```bash
# 备份最近7天的日志
./main.sh backup logs

# 备份最近3天的日志
./main.sh backup logs "" 3

# 恢复日志文件
./main.sh restore logs logs_backup.tar.gz
```

#### 完整备份
```bash
# 完整备份
./main.sh backup full

# 自定义备份名称
./main.sh backup full weekly_backup_20241201
```

### 5. 自动修复

#### 服务修复
- 服务异常自动重启
- 端口冲突自动解决
- 依赖服务自动修复
- 配置文件自动恢复

#### 系统修复
- 临时文件清理
- 日志文件清理
- 缓存清理
- 权限修复

#### 资源修复
- 内存清理
- 磁盘清理
- 网络连接清理

## 🎛️ 运维面板

### 启动面板
```bash
./main.sh panel
```

### 面板功能

#### 1. 服务管理
- 查看所有服务状态
- 启动/停止/重启服务
- 批量操作
- 服务日志查看

#### 2. 系统监控
- 实时系统资源监控
- 性能指标展示
- 历史数据查看
- 告警状态查看

#### 3. 日志管理
- 日志文件浏览
- 实时日志监控
- 日志搜索分析
- 错误统计
- 趋势分析
- 性能分析
- 报告生成

#### 4. 健康检查
- 全面健康检查
- 问题诊断
- 修复建议
- 历史报告

#### 5. 自动修复
- 问题自动检测
- 一键修复
- 修复历史
- 修复统计

#### 6. 备份恢复
- 数据库备份/恢复
- 配置文件备份/恢复
- 日志文件备份/恢复
- 系统文件备份/恢复
- 完整备份/恢复
- 备份列表管理
- 过期备份清理

## ⚙️ 配置管理

### 服务配置 (`config/services.conf`)
```bash
# 服务端口配置
MYSQL_PORT="3306"
REDIS_PORT="6379"
BACKEND_PORT="9099"
FRONTEND_PORT="8089"

# 健康检查配置
HEALTH_CHECK_INTERVAL=30
SERVICE_START_TIMEOUT=60

# 自动重启配置
MAX_RESTART_ATTEMPTS=3
RESTART_INTERVAL=10
```

### 日志配置 (`config/logging.conf`)
```bash
# 日志级别
GLOBAL_LOG_LEVEL="INFO"

# 轮转配置
LOG_MAX_SIZE_MB=100
LOG_RETENTION_DAYS=30
LOG_COMPRESS_ENABLED=true

# 监控配置
ERROR_MONITORING_ENABLED=true
ERROR_THRESHOLD_PER_MINUTE=10
```

## 🔧 高级功能

### 自定义脚本
可以在 `core/` 目录下添加自定义脚本，系统会自动集成。

### 插件扩展
支持通过插件方式扩展功能，插件放在 `plugins/` 目录下。

### API接口
提供REST API接口，可以通过HTTP调用运维功能。

### 集成监控
支持与Prometheus、Grafana、ELK等监控系统集成。

## 📋 最佳实践

### 1. 定期维护
- 每日执行健康检查
- 每周执行完整备份
- 每月清理过期文件

### 2. 监控策略
- 设置合理的告警阈值
- 配置多种通知方式
- 定期检查监控状态

### 3. 备份策略
- 数据库每日备份
- 配置文件每周备份
- 重要变更前手动备份

### 4. 安全考虑
- 限制脚本执行权限
- 定期更新系统
- 监控异常访问

## 🚨 故障排除

### 常见问题

1. **服务启动失败**
   ```bash
   # 检查端口占用
   ./main.sh status
   
   # 查看详细日志
   ./main.sh logs system
   
   # 执行健康检查
   ./main.sh health
   ```

2. **备份失败**
   ```bash
   # 检查磁盘空间
   df -h
   
   # 检查权限
   ls -la backups/
   
   # 查看备份日志
   ./main.sh logs system | grep backup
   ```

3. **监控异常**
   ```bash
   # 重启监控服务
   ./main.sh restart
   
   # 检查配置文件
   ./main.sh logs system | grep config
   ```

### 性能优化

1. **日志优化**
   - 调整日志级别
   - 启用日志压缩
   - 定期清理日志

2. **监控优化**
   - 调整检查间隔
   - 优化告警阈值
   - 使用异步处理

3. **备份优化**
   - 增量备份
   - 压缩备份
   - 异地备份

## 🎯 总结

运维管理系统提供了完整的运维解决方案：

- ✅ **智能化** - 自动检测、分析、修复
- ✅ **可视化** - 友好的界面和报告
- ✅ **自动化** - 减少人工干预
- ✅ **可靠性** - 多重保障机制
- ✅ **可扩展** - 模块化设计
- ✅ **易用性** - 简单的命令接口

通过这个系统，您可以轻松管理电话标记系统的运维工作，提高系统可靠性和运维效率！

---

**开始使用：`./main.sh panel` 🚀**
