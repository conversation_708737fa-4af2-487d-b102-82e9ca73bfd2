# 电话标记系统运维管理 - 快速开始指南

## 🚀 一键启动

### 方式一：自动安装并启动
```bash
# 进入脚本目录
cd phone-marking-system-v2/scripts/ops

# 运行一键安装脚本（首次使用）
./install.sh

# 启动所有服务
./main.sh start
```

### 方式二：直接启动（已安装环境）
```bash
# 进入脚本目录
cd phone-marking-system-v2/scripts/ops

# 一键启动所有服务
./main.sh start
```

## 📋 常用命令

### 服务管理
```bash
# 启动所有服务
./main.sh start

# 停止所有服务
./main.sh stop

# 重启所有服务
./main.sh restart

# 查看服务状态
./main.sh status
```

### 健康检查
```bash
# 执行全面健康检查
./main.sh health

# 自动修复问题
./main.sh repair
```

### 日志管理
```bash
# 查看所有日志
./main.sh logs

# 查看特定服务日志
./main.sh logs mysql 1h

# 查看今天的后端日志
./main.sh logs backend today
```

### 运维面板
```bash
# 启动交互式运维管理面板
./main.sh panel
```

## 🎛️ 运维管理面板功能

启动面板后，您可以使用以下功能：

1. **服务管理** - 启动/停止/重启服务
2. **系统监控** - 实时监控系统资源
3. **日志管理** - 查看和分析日志
4. **健康检查** - 检查系统健康状态
5. **自动修复** - 自动解决常见问题
6. **系统信息** - 查看系统详细信息

## 🌐 访问地址

启动成功后，可以通过以下地址访问：

- **前端界面**: http://127.0.0.1:8089
- **后端API**: http://127.0.0.1:9099
- **运维面板**: 在终端中运行 `./main.sh panel`

## 📊 服务状态说明

| 状态 | 说明 | 颜色 |
|------|------|------|
| 🟢 运行中 | 服务正常运行 | 绿色 |
| 🔴 已停止 | 服务未运行 | 红色 |
| 🟡 启动中 | 服务正在启动 | 黄色 |
| 🟠 警告 | 服务有问题但仍在运行 | 橙色 |

## 🔧 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 检查端口占用
   netstat -tlnp | grep :8089
   
   # 自动修复
   ./main.sh repair
   ```

2. **数据库连接失败**
   ```bash
   # 检查MySQL状态
   sudo systemctl status mysql
   
   # 启动MySQL
   sudo systemctl start mysql
   ```

3. **Redis连接失败**
   ```bash
   # 检查Redis状态
   sudo systemctl status redis
   
   # 启动Redis
   sudo systemctl start redis
   ```

4. **服务启动失败**
   ```bash
   # 查看详细日志
   ./main.sh logs system
   
   # 执行健康检查
   ./main.sh health
   
   # 自动修复
   ./main.sh repair
   ```

### 日志位置

- **系统日志**: `logs/system/system_YYYYMMDD.log`
- **服务日志**: `logs/services/[service_name]_YYYYMMDD.log`
- **操作日志**: `logs/operations/operations_YYYYMMDD.log`

## 📈 性能监控

### 系统资源监控
```bash
# 查看系统资源使用情况
./main.sh panel
# 选择 "2. 系统监控"
```

### 服务性能监控
```bash
# 查看服务状态和性能
./main.sh status
```

## 🔄 自动化功能

### 自动重启
系统会自动检测服务异常并尝试重启，最多重试3次。

### 自动清理
- 自动清理7天前的日志文件
- 自动清理临时文件
- 自动清理系统缓存

### 健康检查
- 每30秒自动检查服务健康状态
- 自动检测资源使用情况
- 自动生成健康报告

## 🛠️ 高级功能

### 备份和恢复
```bash
# 备份系统数据
./main.sh backup

# 恢复系统数据
./main.sh restore backup_file
```

### 系统更新
```bash
# 更新系统
./main.sh update
```

### 配置管理
```bash
# 编辑服务配置
vim config/services.conf
```

## 📞 技术支持

如果遇到问题，请：

1. 查看日志文件获取详细错误信息
2. 运行健康检查诊断问题
3. 尝试自动修复功能
4. 查看本文档的故障排除部分

## 🎯 最佳实践

1. **定期备份**: 建议每天备份重要数据
2. **监控日志**: 定期查看系统和服务日志
3. **健康检查**: 每天运行一次健康检查
4. **资源监控**: 关注CPU、内存、磁盘使用情况
5. **及时更新**: 定期更新系统和依赖包

---

**祝您使用愉快！** 🎉
