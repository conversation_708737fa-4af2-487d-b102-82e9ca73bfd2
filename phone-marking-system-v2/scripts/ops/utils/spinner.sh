#!/bin/bash

# =============================================================================
# 进度显示工具
# 提供加载动画、进度条等用户体验增强功能
# =============================================================================

# 加载颜色工具
if [[ -f "$(dirname "${BASH_SOURCE[0]}")/colors.sh" ]]; then
    source "$(dirname "${BASH_SOURCE[0]}")/colors.sh"
fi

# 旋转加载动画
spinner() {
    local pid=$1
    local message="${2:-加载中}"
    local delay=0.1
    local spinstr='|/-\'
    
    echo -n "$message "
    
    while kill -0 $pid 2>/dev/null; do
        local temp=${spinstr#?}
        printf "\r%s %c" "$message" "$spinstr"
        local spinstr=$temp${spinstr%"$temp"}
        sleep $delay
    done
    
    printf "\r%s ✓\n" "$message"
}

# 点状加载动画
dots_spinner() {
    local pid=$1
    local message="${2:-处理中}"
    local delay=0.5
    local dots=""
    
    while kill -0 $pid 2>/dev/null; do
        dots+="."
        if [[ ${#dots} -gt 3 ]]; then
            dots=""
        fi
        printf "\r%s%s   " "$message" "$dots"
        sleep $delay
    done
    
    printf "\r%s 完成!\n" "$message"
}

# 进度条动画
progress_bar() {
    local current=$1
    local total=$2
    local width=${3:-50}
    local message="${4:-进度}"
    
    local percentage=$((current * 100 / total))
    local filled=$((current * width / total))
    local empty=$((width - filled))
    
    # 构建进度条
    local bar=""
    for ((i=1; i<=filled; i++)); do
        bar+="█"
    done
    for ((i=1; i<=empty; i++)); do
        bar+="░"
    done
    
    # 选择颜色
    local color="$GREEN"
    if [[ $percentage -lt 30 ]]; then
        color="$RED"
    elif [[ $percentage -lt 70 ]]; then
        color="$YELLOW"
    fi
    
    # 输出进度条
    printf "\r${color}%s [%s] %3d%%${RESET}" "$message" "$bar" "$percentage"
    
    # 如果完成，换行
    if [[ $current -eq $total ]]; then
        echo ""
    fi
}

# 百分比进度条
percentage_bar() {
    local percentage=$1
    local width=${2:-30}
    local message="${3:-进度}"
    
    local filled=$((percentage * width / 100))
    local empty=$((width - filled))
    
    # 构建进度条
    local bar=""
    for ((i=1; i<=filled; i++)); do
        bar+="▓"
    done
    for ((i=1; i<=empty; i++)); do
        bar+="░"
    done
    
    # 选择颜色
    local color="$GREEN"
    if [[ $percentage -lt 30 ]]; then
        color="$RED"
    elif [[ $percentage -lt 70 ]]; then
        color="$YELLOW"
    fi
    
    printf "${color}%s [%s] %3d%%${RESET}\n" "$message" "$bar" "$percentage"
}

# 倒计时动画
countdown() {
    local seconds=$1
    local message="${2:-倒计时}"
    
    for ((i=seconds; i>0; i--)); do
        printf "\r%s: %d 秒" "$message" "$i"
        sleep 1
    done
    
    printf "\r%s: 完成!\n" "$message"
}

# 脉冲动画
pulse() {
    local pid=$1
    local message="${2:-运行中}"
    local delay=0.5
    
    while kill -0 $pid 2>/dev/null; do
        printf "\r%s ●" "$message"
        sleep $delay
        printf "\r%s ○" "$message"
        sleep $delay
    done
    
    printf "\r%s ✓\n" "$message"
}

# 波浪动画
wave() {
    local pid=$1
    local message="${2:-加载中}"
    local delay=0.2
    local wave_chars=("▁" "▂" "▃" "▄" "▅" "▆" "▇" "█" "▇" "▆" "▅" "▄" "▃" "▂")
    local index=0
    
    while kill -0 $pid 2>/dev/null; do
        printf "\r%s %s" "$message" "${wave_chars[$index]}"
        index=$(( (index + 1) % ${#wave_chars[@]} ))
        sleep $delay
    done
    
    printf "\r%s ✓\n" "$message"
}

# 箭头动画
arrow() {
    local pid=$1
    local message="${2:-处理中}"
    local delay=0.3
    local arrows=("→" "↘" "↓" "↙" "←" "↖" "↑" "↗")
    local index=0
    
    while kill -0 $pid 2>/dev/null; do
        printf "\r%s %s" "$message" "${arrows[$index]}"
        index=$(( (index + 1) % ${#arrows[@]} ))
        sleep $delay
    done
    
    printf "\r%s ✓\n" "$message"
}

# 带时间的进度显示
timed_progress() {
    local duration=$1
    local message="${2:-处理中}"
    local width=${3:-30}
    
    local start_time=$(date +%s)
    
    for ((i=0; i<=duration; i++)); do
        local percentage=$((i * 100 / duration))
        local elapsed=$(($(date +%s) - start_time))
        local eta=$((duration - elapsed))
        
        progress_bar $i $duration $width "$message (剩余: ${eta}s)"
        sleep 1
    done
}

# 多任务进度显示
multi_progress() {
    local -n tasks_ref=$1
    local width=${2:-30}
    
    while true; do
        clear
        echo_title "任务进度"
        
        local all_done=true
        for task_name in "${!tasks_ref[@]}"; do
            local progress=${tasks_ref[$task_name]}
            percentage_bar $progress $width "$task_name"
            
            if [[ $progress -lt 100 ]]; then
                all_done=false
            fi
        done
        
        if [[ "$all_done" == "true" ]]; then
            echo_success "所有任务完成!"
            break
        fi
        
        sleep 1
    done
}

# 实时状态显示
status_display() {
    local status_func=$1
    local message="${2:-状态监控}"
    local refresh_interval=${3:-2}
    
    echo_title "$message"
    echo_info "按 Ctrl+C 停止监控"
    echo ""
    
    while true; do
        # 保存光标位置
        tput sc
        
        # 执行状态函数
        eval "$status_func"
        
        # 恢复光标位置
        tput rc
        
        sleep $refresh_interval
    done
}

# 加载配置动画
loading_config() {
    local config_file=$1
    local message="加载配置文件: $(basename "$config_file")"
    
    # 模拟加载过程
    (
        sleep 0.5
        if [[ -f "$config_file" ]]; then
            sleep 1
        else
            sleep 2
            exit 1
        fi
    ) &
    
    local pid=$!
    spinner $pid "$message"
    
    wait $pid
    return $?
}

# 服务启动动画
service_start_animation() {
    local service_name=$1
    local timeout=${2:-30}
    
    echo_progress "启动服务: $service_name"
    
    for ((i=1; i<=timeout; i++)); do
        local dots=""
        for ((j=1; j<=((i%4)); j++)); do
            dots+="."
        done
        
        printf "\r启动服务: %s%s    " "$service_name" "$dots"
        
        # 这里可以添加实际的服务检查逻辑
        # if service_is_running "$service_name"; then
        #     echo_success "服务 $service_name 启动成功"
        #     return 0
        # fi
        
        sleep 1
    done
    
    echo_error "服务 $service_name 启动超时"
    return 1
}

# 文件传输进度
file_transfer_progress() {
    local source_file=$1
    local dest_file=$2
    local total_size=$(stat -c%s "$source_file" 2>/dev/null || echo "0")
    
    if [[ $total_size -eq 0 ]]; then
        echo_error "无法获取文件大小"
        return 1
    fi
    
    # 启动文件复制
    cp "$source_file" "$dest_file" &
    local cp_pid=$!
    
    # 监控进度
    while kill -0 $cp_pid 2>/dev/null; do
        if [[ -f "$dest_file" ]]; then
            local current_size=$(stat -c%s "$dest_file" 2>/dev/null || echo "0")
            local percentage=$((current_size * 100 / total_size))
            
            progress_bar $current_size $total_size 40 "复制文件"
        fi
        
        sleep 0.5
    done
    
    wait $cp_pid
    local result=$?
    
    if [[ $result -eq 0 ]]; then
        echo_success "文件复制完成"
    else
        echo_error "文件复制失败"
    fi
    
    return $result
}

# 示例用法函数
demo_spinners() {
    echo_title "进度显示演示"
    
    echo_subtitle "旋转动画"
    (sleep 3) &
    spinner $! "加载数据"
    
    echo_subtitle "点状动画"
    (sleep 3) &
    dots_spinner $! "连接服务器"
    
    echo_subtitle "进度条"
    for ((i=0; i<=100; i+=10)); do
        progress_bar $i 100 30 "下载文件"
        sleep 0.2
    done
    
    echo_subtitle "倒计时"
    countdown 5 "系统重启"
    
    echo_subtitle "脉冲动画"
    (sleep 3) &
    pulse $! "同步数据"
    
    echo_subtitle "波浪动画"
    (sleep 3) &
    wave $! "处理请求"
    
    echo_subtitle "箭头动画"
    (sleep 3) &
    arrow $! "传输数据"
    
    echo_done "演示完成"
}

# 如果直接执行此脚本，运行演示
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    demo_spinners
fi
