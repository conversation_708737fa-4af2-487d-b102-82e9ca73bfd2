#!/bin/bash

# =============================================================================
# 通用工具函数库
# 提供日志记录、时间处理、文件操作等通用功能
# =============================================================================

# 获取当前时间戳
get_timestamp() {
    date '+%Y-%m-%d %H:%M:%S'
}

# 字符串转大写 - 兼容不同bash版本和shell
to_upper() {
    local str="$1"
    # 优先使用bash 4.0+的内置功能
    if [[ "${BASH_VERSION%%.*}" -ge 4 ]] 2>/dev/null; then
        echo "${str^^}"
    else
        # 兼容旧版本bash和其他shell
        echo "$str" | tr '[:lower:]' '[:upper:]'
    fi
}

# 字符串转小写 - 兼容不同bash版本和shell
to_lower() {
    local str="$1"
    # 优先使用bash 4.0+的内置功能
    if [[ "${BASH_VERSION%%.*}" -ge 4 ]] 2>/dev/null; then
        echo "${str,,}"
    else
        # 兼容旧版本bash和其他shell
        echo "$str" | tr '[:upper:]' '[:lower:]'
    fi
}

# 获取CPU使用率 - 跨平台兼容
get_cpu_usage() {
    local cpu_usage=0

    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        cpu_usage=$(top -l 1 -s 0 | grep "CPU usage" | awk '{print $3}' | cut -d'%' -f1)
        # 如果获取失败，使用iostat
        if [[ -z "$cpu_usage" ]] || [[ "$cpu_usage" == "0" ]]; then
            cpu_usage=$(iostat -c 1 | tail -1 | awk '{print 100-$6}' | cut -d'.' -f1)
        fi
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux
        cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1 | cut -d',' -f1)
    else
        # 其他系统，尝试通用方法
        cpu_usage=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | cut -d',' -f1)
        cpu_usage=${cpu_usage%.*}
    fi

    # 确保返回数字
    cpu_usage=${cpu_usage%.*}
    echo "${cpu_usage:-0}"
}

# 获取内存使用率 - 跨平台兼容
get_memory_usage() {
    local memory_usage=0

    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        local total_mem=$(sysctl -n hw.memsize)
        local used_mem=$(vm_stat | grep "Pages active" | awk '{print $3}' | cut -d'.' -f1)
        used_mem=$((used_mem * 4096))  # 页面大小通常是4KB
        memory_usage=$((used_mem * 100 / total_mem))
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux
        if command -v free >/dev/null 2>&1; then
            memory_usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
        else
            # 备用方法
            memory_usage=$(cat /proc/meminfo | awk '/MemTotal/{total=$2} /MemAvailable/{avail=$2} END{printf "%.0f", (total-avail)*100/total}')
        fi
    else
        # 其他系统，返回默认值
        memory_usage=0
    fi

    echo "${memory_usage:-0}"
}

# 检测Python环境 - 智能选择python/python3
detect_python() {
    local python_cmd=""

    # 优先检查python3
    if command -v python3 >/dev/null 2>&1; then
        python_cmd="python3"
    elif command -v python >/dev/null 2>&1; then
        # 检查python版本是否为3.x
        local version=$(python --version 2>&1 | grep -o "Python [0-9]" | grep -o "[0-9]")
        if [[ "$version" == "3" ]]; then
            python_cmd="python"
        fi
    fi

    echo "$python_cmd"
}

# 检查Python包是否已安装
check_python_package() {
    local package="$1"
    local python_cmd="${2:-$(detect_python)}"

    if [[ -z "$python_cmd" ]]; then
        return 1
    fi

    $python_cmd -c "import $package" >/dev/null 2>&1
}

# 检查requirements.txt中的依赖是否已安装
check_requirements() {
    local requirements_file="$1"
    local python_cmd="${2:-$(detect_python)}"

    if [[ ! -f "$requirements_file" ]]; then
        echo "requirements文件不存在: $requirements_file"
        return 1
    fi

    if [[ -z "$python_cmd" ]]; then
        echo "未找到Python环境"
        return 1
    fi

    local missing_packages=()
    local total_packages=0
    local installed_packages=0

    # 读取requirements.txt并检查每个包
    while IFS= read -r line; do
        # 跳过空行和注释
        [[ -z "$line" || "$line" =~ ^[[:space:]]*# ]] && continue

        # 提取包名（去掉版本号）
        local package=$(echo "$line" | sed 's/[>=<].*//' | sed 's/\[.*\]//')
        package=$(echo "$package" | tr '[:upper:]' '[:lower:]' | sed 's/-/_/g')

        ((total_packages++))

        if check_python_package "$package" "$python_cmd"; then
            ((installed_packages++))
        else
            missing_packages+=("$line")
        fi
    done < "$requirements_file"

    echo "依赖检查结果: $installed_packages/$total_packages 已安装"

    if [[ ${#missing_packages[@]} -eq 0 ]]; then
        return 0
    else
        echo "缺失的依赖包:"
        printf '  %s\n' "${missing_packages[@]}"
        return 1
    fi
}

# 智能安装Python依赖
smart_install_requirements() {
    local requirements_file="$1"
    local python_cmd="${2:-$(detect_python)}"

    if [[ -z "$python_cmd" ]]; then
        echo_error "未找到Python环境，请先安装Python 3.x"
        return 1
    fi

    echo_info "使用Python命令: $python_cmd"

    # 检查pip
    local pip_cmd=""
    if command -v pip3 >/dev/null 2>&1; then
        pip_cmd="pip3"
    elif command -v pip >/dev/null 2>&1; then
        pip_cmd="pip"
    else
        echo_error "未找到pip，请先安装pip"
        return 1
    fi

    # 智能依赖检查（带缓存）
    local cache_file="/tmp/deps_$(basename "$requirements_file").cache"
    local hash_file="/tmp/deps_$(basename "$requirements_file").hash"

    # 计算requirements文件哈希
    local current_hash=""
    if command -v md5 >/dev/null 2>&1; then
        current_hash=$(md5 -q "$requirements_file" 2>/dev/null || echo "unknown")
    elif command -v md5sum >/dev/null 2>&1; then
        current_hash=$(md5sum "$requirements_file" 2>/dev/null | cut -d' ' -f1 || echo "unknown")
    else
        current_hash=$(stat -f "%m" "$requirements_file" 2>/dev/null || stat -c "%Y" "$requirements_file" 2>/dev/null || echo "unknown")
    fi

    # 检查缓存
    local use_cache=false
    if [[ -f "$hash_file" && -f "$cache_file" ]]; then
        local cached_hash=$(cat "$hash_file" 2>/dev/null || echo "")
        local cached_status=$(cat "$cache_file" 2>/dev/null || echo "")

        if [[ "$current_hash" == "$cached_hash" && "$cached_status" == "satisfied" ]]; then
            # 检查缓存时间（6小时内有效）
            local cache_time=$(stat -f "%m" "$cache_file" 2>/dev/null || stat -c "%Y" "$cache_file" 2>/dev/null || echo "0")
            local current_time=$(date +%s)
            local cache_age=$((current_time - cache_time))

            if [[ $cache_age -lt 21600 ]]; then  # 6小时 = 21600秒
                echo_success "所有依赖已满足（缓存验证，${cache_age}秒前检查）"
                return 0
            fi
        fi
    fi

    echo_info "检查现有依赖..."
    if check_requirements "$requirements_file" "$python_cmd"; then
        echo_success "所有依赖已安装，跳过安装步骤"
        # 更新缓存
        echo "$current_hash" > "$hash_file" 2>/dev/null || true
        echo "satisfied" > "$cache_file" 2>/dev/null || true
        return 0
    fi

    echo_warning "发现缺失依赖，开始安装..."

    # 使用国内镜像源加速安装
    local pip_index=""
    if [[ "$OSTYPE" == "darwin"* ]] || [[ -n "$CHINA_MIRROR" ]]; then
        pip_index="-i https://pypi.tuna.tsinghua.edu.cn/simple"
    fi

    if $pip_cmd install $pip_index -r "$requirements_file"; then
        echo_success "依赖安装完成"
        # 更新缓存
        echo "$current_hash" > "$hash_file" 2>/dev/null || true
        echo "satisfied" > "$cache_file" 2>/dev/null || true
        return 0
    else
        echo_error "依赖安装失败"
        # 记录失败状态
        echo "$current_hash" > "$hash_file" 2>/dev/null || true
        echo "failed" > "$cache_file" 2>/dev/null || true
        return 1
    fi
}

# 智能检测服务启动方式
detect_service_start_method() {
    local service_name="$1"
    local service_dir="$2"

    case "$service_name" in
        "backend"|"frontend")
            # 检查是否有app.py
            if [[ -f "$service_dir/app.py" ]]; then
                local python_cmd=$(detect_python)
                if [[ -n "$python_cmd" ]]; then
                    # 检查是否有uvicorn
                    if check_python_package "uvicorn" "$python_cmd"; then
                        echo "$python_cmd -m uvicorn app:app --host 0.0.0.0 --port"
                    else
                        echo "$python_cmd app.py"
                    fi
                else
                    echo ""
                fi
            else
                echo ""
            fi
            ;;
        *)
            echo ""
            ;;
    esac
}

# 获取当前日期
get_date() {
    date '+%Y-%m-%d'
}

# 记录系统日志
log_system() {
    local level="$1"
    local message="$2"
    local timestamp=$(get_timestamp)
    
    echo "[$timestamp] [$level] $message" >> "${SYSTEM_LOG:-/tmp/system.log}"
    
    # 根据级别输出到控制台
    case "$level" in
        "ERROR")
            echo_color "$RED" "❌ $message"
            ;;
        "WARN")
            echo_color "$YELLOW" "⚠️  $message"
            ;;
        "INFO")
            echo_color "$GREEN" "ℹ️  $message"
            ;;
        "DEBUG")
            echo_color "$GRAY" "🔍 $message"
            ;;
    esac
}

# 记录操作日志
log_operation() {
    local operation="$1"
    local details="$2"
    local timestamp=$(get_timestamp)
    
    echo "[$timestamp] 操作: $operation | 详情: $details" >> "${OPERATION_LOG:-/tmp/operations.log}"
}

# 检查命令是否存在
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# 检查端口是否被占用
port_in_use() {
    local port="$1"
    netstat -tlnp 2>/dev/null | grep -q ":$port "
}

# 检查进程是否运行
process_running() {
    local process_name="$1"
    pgrep -f "$process_name" >/dev/null 2>&1
}

# 等待端口可用
wait_for_port() {
    local host="$1"
    local port="$2"
    local timeout="${3:-30}"
    local count=0
    
    while ! nc -z "$host" "$port" 2>/dev/null; do
        if [[ $count -ge $timeout ]]; then
            return 1
        fi
        sleep 1
        ((count++))
    done
    return 0
}

# 检查服务健康状态
check_service_health() {
    local service_name="$1"
    local health_url="$2"
    local timeout="${3:-10}"
    
    if [[ -n "$health_url" ]]; then
        # HTTP健康检查
        if curl -s --max-time "$timeout" "$health_url" >/dev/null 2>&1; then
            return 0
        else
            return 1
        fi
    else
        # 进程检查
        if process_running "$service_name"; then
            return 0
        else
            return 1
        fi
    fi
}

# 获取系统信息
get_system_info() {
    echo "系统信息:"
    echo "  操作系统: $(uname -s)"
    echo "  内核版本: $(uname -r)"
    echo "  架构: $(uname -m)"
    echo "  主机名: $(hostname)"
    echo "  当前用户: $(whoami)"
    echo "  运行时间: $(uptime -p 2>/dev/null || uptime)"
    echo ""
    
    echo "资源使用:"
    echo "  CPU使用率: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)%"
    echo "  内存使用: $(free -h | awk 'NR==2{printf "%.1f%%", $3*100/$2}')"
    echo "  磁盘使用: $(df -h / | awk 'NR==2{print $5}')"
    echo ""
}

# 创建备份
create_backup() {
    local source_path="$1"
    local backup_name="$2"
    local backup_dir="${SCRIPT_DIR}/backups"
    
    mkdir -p "$backup_dir"
    
    local timestamp=$(date '+%Y%m%d_%H%M%S')
    local backup_file="$backup_dir/${backup_name}_${timestamp}.tar.gz"
    
    if tar -czf "$backup_file" -C "$(dirname "$source_path")" "$(basename "$source_path")" 2>/dev/null; then
        echo "$backup_file"
        return 0
    else
        return 1
    fi
}

# 验证配置文件
validate_config() {
    local config_file="$1"
    
    if [[ ! -f "$config_file" ]]; then
        log_system "ERROR" "配置文件不存在: $config_file"
        return 1
    fi
    
    # 检查配置文件语法
    if [[ "$config_file" == *.json ]]; then
        if command_exists jq; then
            if ! jq empty "$config_file" 2>/dev/null; then
                log_system "ERROR" "JSON配置文件语法错误: $config_file"
                return 1
            fi
        fi
    elif [[ "$config_file" == *.yml || "$config_file" == *.yaml ]]; then
        if command_exists python3; then
            if ! python3 -c "import yaml; yaml.safe_load(open('$config_file'))" 2>/dev/null; then
                log_system "ERROR" "YAML配置文件语法错误: $config_file"
                return 1
            fi
        fi
    fi
    
    return 0
}

# 安全地重启服务
safe_restart_service() {
    local service_name="$1"
    local stop_command="$2"
    local start_command="$3"
    local health_check="$4"
    
    log_system "INFO" "开始安全重启服务: $service_name"
    
    # 停止服务
    if eval "$stop_command"; then
        log_system "INFO" "服务停止成功: $service_name"
    else
        log_system "WARN" "服务停止失败: $service_name"
    fi
    
    # 等待服务完全停止
    sleep 3
    
    # 启动服务
    if eval "$start_command"; then
        log_system "INFO" "服务启动成功: $service_name"
        
        # 健康检查
        if [[ -n "$health_check" ]]; then
            sleep 5
            if eval "$health_check"; then
                log_system "INFO" "服务健康检查通过: $service_name"
                return 0
            else
                log_system "ERROR" "服务健康检查失败: $service_name"
                return 1
            fi
        fi
        return 0
    else
        log_system "ERROR" "服务启动失败: $service_name"
        return 1
    fi
}

# 清理旧日志
cleanup_old_logs() {
    local log_dir="$1"
    local days="${2:-7}"
    
    if [[ -d "$log_dir" ]]; then
        find "$log_dir" -name "*.log" -type f -mtime +$days -delete 2>/dev/null
        log_system "INFO" "清理了 $days 天前的日志文件"
    fi
}

# 格式化文件大小
format_size() {
    local size="$1"
    
    if [[ $size -gt ********** ]]; then
        echo "$(( size / ********** ))GB"
    elif [[ $size -gt 1048576 ]]; then
        echo "$(( size / 1048576 ))MB"
    elif [[ $size -gt 1024 ]]; then
        echo "$(( size / 1024 ))KB"
    else
        echo "${size}B"
    fi
}

# 检查磁盘空间
check_disk_space() {
    local path="${1:-.}"
    local threshold="${2:-90}"
    
    local usage=$(df "$path" | awk 'NR==2 {print $5}' | sed 's/%//')
    
    if [[ $usage -gt $threshold ]]; then
        log_system "WARN" "磁盘空间不足: $path 使用率 ${usage}%"
        return 1
    fi
    
    return 0
}
