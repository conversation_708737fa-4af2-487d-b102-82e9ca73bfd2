#!/bin/bash

# =============================================================================
# 通用工具函数库
# 提供日志记录、时间处理、文件操作等通用功能
# =============================================================================

# 获取当前时间戳
get_timestamp() {
    date '+%Y-%m-%d %H:%M:%S'
}

# 字符串转大写 - 兼容不同bash版本和shell
to_upper() {
    local str="$1"
    # 优先使用bash 4.0+的内置功能
    if [[ "${BASH_VERSION%%.*}" -ge 4 ]] 2>/dev/null; then
        echo "${str^^}"
    else
        # 兼容旧版本bash和其他shell
        echo "$str" | tr '[:lower:]' '[:upper:]'
    fi
}

# 字符串转小写 - 兼容不同bash版本和shell
to_lower() {
    local str="$1"
    # 优先使用bash 4.0+的内置功能
    if [[ "${BASH_VERSION%%.*}" -ge 4 ]] 2>/dev/null; then
        echo "${str,,}"
    else
        # 兼容旧版本bash和其他shell
        echo "$str" | tr '[:upper:]' '[:lower:]'
    fi
}

# 获取CPU使用率 - 跨平台兼容
get_cpu_usage() {
    local cpu_usage=0

    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        cpu_usage=$(top -l 1 -s 0 | grep "CPU usage" | awk '{print $3}' | cut -d'%' -f1)
        # 如果获取失败，使用iostat
        if [[ -z "$cpu_usage" ]] || [[ "$cpu_usage" == "0" ]]; then
            cpu_usage=$(iostat -c 1 | tail -1 | awk '{print 100-$6}' | cut -d'.' -f1)
        fi
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux
        cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1 | cut -d',' -f1)
    else
        # 其他系统，尝试通用方法
        cpu_usage=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | cut -d',' -f1)
        cpu_usage=${cpu_usage%.*}
    fi

    # 确保返回数字
    cpu_usage=${cpu_usage%.*}
    echo "${cpu_usage:-0}"
}

# 获取内存使用率 - 跨平台兼容
get_memory_usage() {
    local memory_usage=0

    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        local total_mem=$(sysctl -n hw.memsize)
        local used_mem=$(vm_stat | grep "Pages active" | awk '{print $3}' | cut -d'.' -f1)
        used_mem=$((used_mem * 4096))  # 页面大小通常是4KB
        memory_usage=$((used_mem * 100 / total_mem))
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux
        if command -v free >/dev/null 2>&1; then
            memory_usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
        else
            # 备用方法
            memory_usage=$(cat /proc/meminfo | awk '/MemTotal/{total=$2} /MemAvailable/{avail=$2} END{printf "%.0f", (total-avail)*100/total}')
        fi
    else
        # 其他系统，返回默认值
        memory_usage=0
    fi

    echo "${memory_usage:-0}"
}

# 获取当前日期
get_date() {
    date '+%Y-%m-%d'
}

# 记录系统日志
log_system() {
    local level="$1"
    local message="$2"
    local timestamp=$(get_timestamp)
    
    echo "[$timestamp] [$level] $message" >> "${SYSTEM_LOG:-/tmp/system.log}"
    
    # 根据级别输出到控制台
    case "$level" in
        "ERROR")
            echo_color "$RED" "❌ $message"
            ;;
        "WARN")
            echo_color "$YELLOW" "⚠️  $message"
            ;;
        "INFO")
            echo_color "$GREEN" "ℹ️  $message"
            ;;
        "DEBUG")
            echo_color "$GRAY" "🔍 $message"
            ;;
    esac
}

# 记录操作日志
log_operation() {
    local operation="$1"
    local details="$2"
    local timestamp=$(get_timestamp)
    
    echo "[$timestamp] 操作: $operation | 详情: $details" >> "${OPERATION_LOG:-/tmp/operations.log}"
}

# 检查命令是否存在
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# 检查端口是否被占用
port_in_use() {
    local port="$1"
    netstat -tlnp 2>/dev/null | grep -q ":$port "
}

# 检查进程是否运行
process_running() {
    local process_name="$1"
    pgrep -f "$process_name" >/dev/null 2>&1
}

# 等待端口可用
wait_for_port() {
    local host="$1"
    local port="$2"
    local timeout="${3:-30}"
    local count=0
    
    while ! nc -z "$host" "$port" 2>/dev/null; do
        if [[ $count -ge $timeout ]]; then
            return 1
        fi
        sleep 1
        ((count++))
    done
    return 0
}

# 检查服务健康状态
check_service_health() {
    local service_name="$1"
    local health_url="$2"
    local timeout="${3:-10}"
    
    if [[ -n "$health_url" ]]; then
        # HTTP健康检查
        if curl -s --max-time "$timeout" "$health_url" >/dev/null 2>&1; then
            return 0
        else
            return 1
        fi
    else
        # 进程检查
        if process_running "$service_name"; then
            return 0
        else
            return 1
        fi
    fi
}

# 获取系统信息
get_system_info() {
    echo "系统信息:"
    echo "  操作系统: $(uname -s)"
    echo "  内核版本: $(uname -r)"
    echo "  架构: $(uname -m)"
    echo "  主机名: $(hostname)"
    echo "  当前用户: $(whoami)"
    echo "  运行时间: $(uptime -p 2>/dev/null || uptime)"
    echo ""
    
    echo "资源使用:"
    echo "  CPU使用率: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)%"
    echo "  内存使用: $(free -h | awk 'NR==2{printf "%.1f%%", $3*100/$2}')"
    echo "  磁盘使用: $(df -h / | awk 'NR==2{print $5}')"
    echo ""
}

# 创建备份
create_backup() {
    local source_path="$1"
    local backup_name="$2"
    local backup_dir="${SCRIPT_DIR}/backups"
    
    mkdir -p "$backup_dir"
    
    local timestamp=$(date '+%Y%m%d_%H%M%S')
    local backup_file="$backup_dir/${backup_name}_${timestamp}.tar.gz"
    
    if tar -czf "$backup_file" -C "$(dirname "$source_path")" "$(basename "$source_path")" 2>/dev/null; then
        echo "$backup_file"
        return 0
    else
        return 1
    fi
}

# 验证配置文件
validate_config() {
    local config_file="$1"
    
    if [[ ! -f "$config_file" ]]; then
        log_system "ERROR" "配置文件不存在: $config_file"
        return 1
    fi
    
    # 检查配置文件语法
    if [[ "$config_file" == *.json ]]; then
        if command_exists jq; then
            if ! jq empty "$config_file" 2>/dev/null; then
                log_system "ERROR" "JSON配置文件语法错误: $config_file"
                return 1
            fi
        fi
    elif [[ "$config_file" == *.yml || "$config_file" == *.yaml ]]; then
        if command_exists python3; then
            if ! python3 -c "import yaml; yaml.safe_load(open('$config_file'))" 2>/dev/null; then
                log_system "ERROR" "YAML配置文件语法错误: $config_file"
                return 1
            fi
        fi
    fi
    
    return 0
}

# 安全地重启服务
safe_restart_service() {
    local service_name="$1"
    local stop_command="$2"
    local start_command="$3"
    local health_check="$4"
    
    log_system "INFO" "开始安全重启服务: $service_name"
    
    # 停止服务
    if eval "$stop_command"; then
        log_system "INFO" "服务停止成功: $service_name"
    else
        log_system "WARN" "服务停止失败: $service_name"
    fi
    
    # 等待服务完全停止
    sleep 3
    
    # 启动服务
    if eval "$start_command"; then
        log_system "INFO" "服务启动成功: $service_name"
        
        # 健康检查
        if [[ -n "$health_check" ]]; then
            sleep 5
            if eval "$health_check"; then
                log_system "INFO" "服务健康检查通过: $service_name"
                return 0
            else
                log_system "ERROR" "服务健康检查失败: $service_name"
                return 1
            fi
        fi
        return 0
    else
        log_system "ERROR" "服务启动失败: $service_name"
        return 1
    fi
}

# 清理旧日志
cleanup_old_logs() {
    local log_dir="$1"
    local days="${2:-7}"
    
    if [[ -d "$log_dir" ]]; then
        find "$log_dir" -name "*.log" -type f -mtime +$days -delete 2>/dev/null
        log_system "INFO" "清理了 $days 天前的日志文件"
    fi
}

# 格式化文件大小
format_size() {
    local size="$1"
    
    if [[ $size -gt ********** ]]; then
        echo "$(( size / ********** ))GB"
    elif [[ $size -gt 1048576 ]]; then
        echo "$(( size / 1048576 ))MB"
    elif [[ $size -gt 1024 ]]; then
        echo "$(( size / 1024 ))KB"
    else
        echo "${size}B"
    fi
}

# 检查磁盘空间
check_disk_space() {
    local path="${1:-.}"
    local threshold="${2:-90}"
    
    local usage=$(df "$path" | awk 'NR==2 {print $5}' | sed 's/%//')
    
    if [[ $usage -gt $threshold ]]; then
        log_system "WARN" "磁盘空间不足: $path 使用率 ${usage}%"
        return 1
    fi
    
    return 0
}
