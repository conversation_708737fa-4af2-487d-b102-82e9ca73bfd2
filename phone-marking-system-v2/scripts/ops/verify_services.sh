#!/bin/bash

# 服务验证脚本
# 用于验证所有服务是否能正常启动和运行

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
echo_success() { echo -e "${GREEN}✅ $1${NC}"; }
echo_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
echo_error() { echo -e "${RED}❌ $1${NC}"; }

# 检测Python命令
detect_python() {
    if command -v python3 >/dev/null 2>&1; then
        echo "python3"
    elif command -v python >/dev/null 2>&1; then
        local version=$(python --version 2>&1 | grep -o "Python [0-9]" | grep -o "[0-9]")
        if [[ "$version" == "3" ]]; then
            echo "python"
        fi
    fi
}

# 验证单个服务
verify_service() {
    local service_name="$1"
    local service_dir="$2"
    local service_file="$3"
    local expected_port="$4"
    
    echo_info "验证 $service_name 服务..."
    
    # 检查文件是否存在
    if [[ ! -f "$service_dir/$service_file" ]]; then
        echo_error "$service_name: 服务文件不存在 ($service_dir/$service_file)"
        return 1
    fi
    
    # 检查Python环境
    local python_cmd=$(detect_python)
    if [[ -z "$python_cmd" ]]; then
        echo_error "$service_name: Python环境不可用"
        return 1
    fi
    
    # 尝试启动服务（测试模式）
    echo_info "$service_name: 测试启动..."
    cd "$service_dir"

    # 启动服务并等待
    $python_cmd "$service_file" >/dev/null 2>&1 &
    local pid=$!

    # 等待服务启动
    sleep 5

    # 检查进程是否还在运行
    if kill -0 $pid 2>/dev/null; then
        echo_success "$service_name: 启动成功"

        # 如果有端口，测试端口连接
        if [[ -n "$expected_port" ]]; then
            sleep 2  # 给端口绑定更多时间
            if lsof -i:$expected_port >/dev/null 2>&1; then
                echo_success "$service_name: 端口 $expected_port 正常监听"
            else
                echo_warning "$service_name: 端口 $expected_port 未监听"
            fi
        fi

        # 终止测试进程
        kill $pid 2>/dev/null || true
        sleep 1
        kill -9 $pid 2>/dev/null || true
        wait $pid 2>/dev/null || true

        return 0
    else
        echo_error "$service_name: 启动失败"
        return 1
    fi
}

# 主验证流程
main() {
    echo_info "开始验证所有服务..."
    echo ""
    
    local failed_services=()
    
    # 验证Backend
    if ! verify_service "Backend" "$PROJECT_ROOT/dash-fastapi-admin/dash-fastapi-backend" "app.py" "9099"; then
        failed_services+=("Backend")
    fi
    echo ""
    
    # 验证Frontend
    if ! verify_service "Frontend" "$PROJECT_ROOT/dash-fastapi-admin/dash-fastapi-frontend" "app.py" "8088"; then
        failed_services+=("Frontend")
    fi
    echo ""
    
    # 验证NLP服务
    if ! verify_service "NLP" "$PROJECT_ROOT/src/services/nlp" "nlp_microservice.py" "8002"; then
        failed_services+=("NLP")
    fi
    echo ""
    
    # 验证Batch服务
    if ! verify_service "Batch" "$PROJECT_ROOT/src/services/batch" "batch_processing_service.py" "8003"; then
        failed_services+=("Batch")
    fi
    echo ""
    
    # 显示结果
    if [[ ${#failed_services[@]} -eq 0 ]]; then
        echo_success "🎉 所有服务验证通过！"
        return 0
    else
        echo_error "❌ 以下服务验证失败："
        for service in "${failed_services[@]}"; do
            echo_error "  - $service"
        done
        return 1
    fi
}

# 运行主函数
main "$@"
