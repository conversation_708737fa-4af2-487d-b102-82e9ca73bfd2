# 日志管理系统完整指南

## 🎯 系统概述

电话标记系统的日志管理系统提供了完整的日志处理解决方案，包括：
- **智能日志收集** - 自动收集各服务日志
- **实时监控** - 实时监控日志变化和异常
- **高级分析** - 错误分析、趋势分析、性能分析
- **自动维护** - 日志轮转、压缩、清理
- **友好展示** - 彩色输出、时间过滤、分类显示

## 📁 系统架构

```
logs/
├── system/           # 系统日志
├── services/         # 服务日志
├── operations/       # 操作日志
├── errors/           # 错误日志
├── access/           # 访问日志
├── audit/            # 审计日志
├── performance/      # 性能日志
├── security/         # 安全日志
└── reports/          # 分析报告
```

## 🚀 快速开始

### 基础使用
```bash
# 查看所有日志
./main.sh logs

# 查看特定服务日志
./main.sh logs mysql

# 查看最近1小时的日志
./main.sh logs backend 1h

# 进入高级日志管理
./main.sh logs advanced
```

### 高级功能
```bash
# 搜索日志内容
./main.sh logs search "ERROR"

# 分析错误日志
./main.sh logs analyze

# 实时监控日志
./main.sh logs monitor mysql

# 执行日志维护
./main.sh logs rotate
```

## 📊 功能详解

### 1. 基础日志查看

#### 查看系统日志
```bash
# 查看今天的系统日志
./core/log_manager.sh system today

# 查看昨天的系统日志
./core/log_manager.sh system yesterday

# 查看最近一周的系统日志
./core/log_manager.sh system week
```

#### 查看服务日志
```bash
# 查看MySQL日志
./core/log_manager.sh view mysql

# 查看后端服务日志（最近6小时）
./core/log_manager.sh view backend 6h

# 查看前端服务日志（最近100行）
./core/log_manager.sh view frontend today 100
```

#### 查看操作日志
```bash
# 查看今天的操作日志
./core/log_manager.sh operations today

# 查看最近3天的操作日志
./core/log_manager.sh operations 3d
```

### 2. 实时日志监控

#### 启动实时监控
```bash
# 监控所有服务日志
./core/log_monitor.sh start

# 监控特定服务
./core/log_monitor.sh start mysql redis

# 显示监控仪表板
./core/log_monitor.sh dashboard

# 启动统计监控
./core/log_monitor.sh stats 60
```

#### 监控功能特性
- 🔴 **错误检测** - 自动检测ERROR、CRITICAL、FATAL级别日志
- 🟡 **警告监控** - 监控WARN、WARNING级别日志
- 🐌 **性能监控** - 检测响应时间过长的请求
- 🚨 **安全监控** - 检测可疑活动和安全威胁
- 📧 **告警通知** - 支持邮件、Webhook、短信告警

### 3. 高级日志分析

#### 日志搜索
```bash
# 搜索包含"ERROR"的日志
./core/log_analyzer.sh search "ERROR"

# 在MySQL日志中搜索连接问题
./core/log_analyzer.sh search "connection" mysql

# 大小写敏感搜索
./core/log_analyzer.sh search "Failed" "" "" true
```

#### 错误分析
```bash
# 分析所有服务的错误
./core/log_analyzer.sh errors

# 分析特定服务的错误
./core/log_analyzer.sh errors mysql

# 分析最近3天的错误
./core/log_analyzer.sh errors "" 3d
```

#### 趋势分析
```bash
# 分析最近7天的日志趋势
./core/log_analyzer.sh trends

# 分析MySQL服务的趋势
./core/log_analyzer.sh trends mysql 7

# 分析最近30天的趋势
./core/log_analyzer.sh trends "" 30
```

#### 性能分析
```bash
# 分析后端服务性能
./core/log_analyzer.sh performance backend

# 分析响应时间指标
./core/log_analyzer.sh performance "" response_time
```

### 4. 日志维护管理

#### 日志轮转
```bash
# 轮转特定日志文件
./core/log_rotator.sh rotate /path/to/logfile.log

# 按时间轮转日志
./core/log_rotator.sh rotate-time /path/to/logfile.log daily

# 自动维护所有日志
./core/log_rotator.sh auto
```

#### 日志压缩
```bash
# 压缩1天前的日志
./core/log_rotator.sh compress logs/ 1

# 压缩特定目录的日志
./core/log_rotator.sh compress logs/services/ 2
```

#### 清理过期日志
```bash
# 清理7天前的日志
./core/log_rotator.sh cleanup logs/ 7

# 清理30天前的日志
./core/log_rotator.sh cleanup logs/ 30
```

#### 查看统计信息
```bash
# 查看日志文件统计
./core/log_rotator.sh stats
```

### 5. 报告生成

#### 生成分析报告
```bash
# 生成综合分析报告
./core/log_analyzer.sh report

# 生成特定服务报告
./core/log_analyzer.sh report mysql
```

报告包含：
- 📊 错误统计和分类
- 📈 日志趋势图表
- ⚡ 性能指标分析
- 🔍 异常模式识别
- 💡 优化建议

## ⚙️ 配置管理

### 日志配置文件
配置文件位置：`config/logging.conf`

#### 主要配置项
```bash
# 日志级别
GLOBAL_LOG_LEVEL="INFO"

# 文件大小限制
LOG_MAX_SIZE_MB=100

# 保留天数
LOG_RETENTION_DAYS=30

# 压缩设置
LOG_COMPRESS_ENABLED=true

# 监控设置
ERROR_MONITORING_ENABLED=true
ERROR_THRESHOLD_PER_MINUTE=10
```

#### 告警配置
```bash
# 邮件告警
EMAIL_ALERT_ENABLED=false
EMAIL_TO="<EMAIL>"

# Webhook告警
WEBHOOK_ALERT_ENABLED=false
WEBHOOK_URL="https://hooks.example.com/alert"

# 短信告警
SMS_ALERT_ENABLED=false
SMS_PHONE_NUMBERS="13800138000"
```

## 🎮 演示系统

### 运行完整演示
```bash
# 启动完整功能演示
./demo_logs.sh demo

# 生成示例数据
./demo_logs.sh generate

# 演示特定功能
./demo_logs.sh search
./demo_logs.sh errors
./demo_logs.sh trends
```

### 清理演示数据
```bash
# 清理演示生成的数据
./demo_logs.sh cleanup
```

## 📋 时间格式支持

系统支持多种时间格式：

| 格式 | 说明 | 示例 |
|------|------|------|
| `1h` | 最近1小时 | `./main.sh logs mysql 1h` |
| `6h` | 最近6小时 | `./main.sh logs backend 6h` |
| `12h` | 最近12小时 | `./main.sh logs frontend 12h` |
| `today` | 今天 | `./main.sh logs system today` |
| `yesterday` | 昨天 | `./main.sh logs operations yesterday` |
| `3d` | 最近3天 | `./main.sh logs mysql 3d` |
| `week` | 最近一周 | `./main.sh logs backend week` |
| `month` | 最近一月 | `./main.sh logs system month` |
| `YYYY-MM-DD` | 指定日期 | `./main.sh logs mysql 2024-01-15` |

## 🔧 故障排除

### 常见问题

1. **日志文件不存在**
   ```bash
   # 检查服务是否运行
   ./main.sh status
   
   # 检查日志目录权限
   ls -la logs/
   ```

2. **监控无法启动**
   ```bash
   # 检查端口占用
   netstat -tlnp | grep :8089
   
   # 检查依赖工具
   which multitail
   ```

3. **搜索结果为空**
   ```bash
   # 检查搜索关键词
   # 检查时间范围
   # 检查服务名称
   ```

4. **压缩失败**
   ```bash
   # 检查gzip是否安装
   which gzip
   
   # 检查磁盘空间
   df -h
   ```

### 性能优化

1. **大日志文件处理**
   - 启用日志轮转
   - 定期压缩旧日志
   - 调整缓冲区大小

2. **监控性能优化**
   - 调整监控间隔
   - 过滤不重要的日志
   - 使用异步处理

3. **存储优化**
   - 启用日志压缩
   - 合理设置保留期
   - 使用SSD存储

## 🎯 最佳实践

1. **日志级别管理**
   - 生产环境使用WARN级别
   - 开发环境使用DEBUG级别
   - 关键服务使用INFO级别

2. **监控策略**
   - 设置合理的告警阈值
   - 配置多种告警方式
   - 定期检查监控状态

3. **维护计划**
   - 每日自动轮转
   - 每周压缩旧日志
   - 每月清理过期文件

4. **安全考虑**
   - 过滤敏感信息
   - 限制日志访问权限
   - 定期备份重要日志

---

**🎉 现在您已经掌握了完整的日志管理系统！**

通过这个系统，您可以：
- ✅ 轻松查看和分析日志
- ✅ 实时监控系统状态
- ✅ 快速定位问题根因
- ✅ 自动维护日志文件
- ✅ 生成专业分析报告

开始使用：`./main.sh logs advanced` 🚀
