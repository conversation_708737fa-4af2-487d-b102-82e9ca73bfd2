#!/bin/bash

# =============================================================================
# 一键安装脚本
# 自动安装和配置电话标记系统运维管理环境
# =============================================================================

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# 加载工具函数
source "$SCRIPT_DIR/utils/common.sh"
source "$SCRIPT_DIR/utils/colors.sh"

# 安装配置
INSTALL_LOG="$SCRIPT_DIR/logs/install_$(date +%Y%m%d_%H%M%S).log"

# 创建日志目录
mkdir -p "$(dirname "$INSTALL_LOG")"

# 记录安装日志
log_install() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] $message" | tee -a "$INSTALL_LOG"
}

# 检查系统环境
check_system() {
    echo_title "检查系统环境"
    
    # 检查操作系统
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        echo_success "✅ 操作系统: Linux"
        log_install "系统检查: Linux 系统"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        echo_success "✅ 操作系统: macOS"
        log_install "系统检查: macOS 系统"
    else
        echo_error "❌ 不支持的操作系统: $OSTYPE"
        exit 1
    fi
    
    # 检查权限
    if [[ $EUID -eq 0 ]]; then
        echo_warning "⚠️  检测到root用户，建议使用普通用户安装"
    else
        echo_success "✅ 用户权限: 普通用户"
    fi
    
    # 检查磁盘空间
    local available_space=$(df "$PROJECT_ROOT" | awk 'NR==2 {print $4}')
    if [[ $available_space -lt 1048576 ]]; then  # 1GB
        echo_error "❌ 磁盘空间不足，至少需要1GB可用空间"
        exit 1
    else
        echo_success "✅ 磁盘空间: 充足"
    fi
    
    echo ""
}

# 安装系统依赖
install_system_dependencies() {
    echo_title "安装系统依赖"
    
    # 检测包管理器
    if command_exists apt-get; then
        PKG_MANAGER="apt-get"
        PKG_UPDATE="sudo apt-get update"
        PKG_INSTALL="sudo apt-get install -y"
    elif command_exists yum; then
        PKG_MANAGER="yum"
        PKG_UPDATE="sudo yum update -y"
        PKG_INSTALL="sudo yum install -y"
    elif command_exists brew; then
        PKG_MANAGER="brew"
        PKG_UPDATE="brew update"
        PKG_INSTALL="brew install"
    else
        echo_error "❌ 未找到支持的包管理器"
        exit 1
    fi
    
    echo_info "使用包管理器: $PKG_MANAGER"
    log_install "包管理器: $PKG_MANAGER"
    
    # 更新包列表
    echo_progress "更新包列表..."
    if eval "$PKG_UPDATE" >> "$INSTALL_LOG" 2>&1; then
        echo_success "✅ 包列表更新完成"
    else
        echo_warning "⚠️  包列表更新失败，继续安装..."
    fi
    
    # 安装基础工具
    local basic_tools=("curl" "wget" "git" "vim" "htop" "netstat" "lsof")
    
    for tool in "${basic_tools[@]}"; do
        if ! command_exists "$tool"; then
            echo_progress "安装 $tool..."
            if eval "$PKG_INSTALL $tool" >> "$INSTALL_LOG" 2>&1; then
                echo_success "✅ $tool 安装完成"
                log_install "工具安装: $tool 成功"
            else
                echo_warning "⚠️  $tool 安装失败"
                log_install "工具安装: $tool 失败"
            fi
        else
            echo_info "✓ $tool 已安装"
        fi
    done
    
    echo ""
}

# 安装Python环境
install_python() {
    echo_title "安装Python环境"
    
    # 检查Python版本
    if command_exists python3; then
        local python_version=$(python3 --version 2>&1 | awk '{print $2}')
        echo_success "✅ Python3 已安装: $python_version"
        log_install "Python检查: $python_version"
    else
        echo_progress "安装Python3..."
        if eval "$PKG_INSTALL python3 python3-pip" >> "$INSTALL_LOG" 2>&1; then
            echo_success "✅ Python3 安装完成"
            log_install "Python安装: 成功"
        else
            echo_error "❌ Python3 安装失败"
            exit 1
        fi
    fi
    
    # 检查pip
    if command_exists pip3; then
        echo_success "✅ pip3 已安装"
    else
        echo_progress "安装pip3..."
        if eval "$PKG_INSTALL python3-pip" >> "$INSTALL_LOG" 2>&1; then
            echo_success "✅ pip3 安装完成"
        else
            echo_error "❌ pip3 安装失败"
            exit 1
        fi
    fi
    
    # 升级pip
    echo_progress "升级pip..."
    if python3 -m pip install --upgrade pip >> "$INSTALL_LOG" 2>&1; then
        echo_success "✅ pip 升级完成"
    else
        echo_warning "⚠️  pip 升级失败"
    fi
    
    echo ""
}

# 安装MySQL
install_mysql() {
    echo_title "安装MySQL数据库"
    
    if command_exists mysql; then
        echo_success "✅ MySQL 已安装"
        log_install "MySQL检查: 已存在"
    else
        echo_progress "安装MySQL..."
        
        if [[ "$PKG_MANAGER" == "apt-get" ]]; then
            if eval "$PKG_INSTALL mysql-server mysql-client" >> "$INSTALL_LOG" 2>&1; then
                echo_success "✅ MySQL 安装完成"
                log_install "MySQL安装: 成功"
            else
                echo_error "❌ MySQL 安装失败"
                exit 1
            fi
        elif [[ "$PKG_MANAGER" == "yum" ]]; then
            if eval "$PKG_INSTALL mysql-server mysql" >> "$INSTALL_LOG" 2>&1; then
                echo_success "✅ MySQL 安装完成"
                log_install "MySQL安装: 成功"
            else
                echo_error "❌ MySQL 安装失败"
                exit 1
            fi
        elif [[ "$PKG_MANAGER" == "brew" ]]; then
            if eval "$PKG_INSTALL mysql" >> "$INSTALL_LOG" 2>&1; then
                echo_success "✅ MySQL 安装完成"
                log_install "MySQL安装: 成功"
            else
                echo_error "❌ MySQL 安装失败"
                exit 1
            fi
        fi
    fi
    
    # 启动MySQL服务
    echo_progress "启动MySQL服务..."
    if sudo systemctl start mysql >> "$INSTALL_LOG" 2>&1; then
        echo_success "✅ MySQL 服务启动成功"
        sudo systemctl enable mysql >> "$INSTALL_LOG" 2>&1
    elif brew services start mysql >> "$INSTALL_LOG" 2>&1; then
        echo_success "✅ MySQL 服务启动成功"
    else
        echo_warning "⚠️  MySQL 服务启动失败，请手动启动"
    fi
    
    echo ""
}

# 安装Redis
install_redis() {
    echo_title "安装Redis缓存"
    
    if command_exists redis-server || command_exists redis-cli; then
        echo_success "✅ Redis 已安装"
        log_install "Redis检查: 已存在"
    else
        echo_progress "安装Redis..."
        
        if eval "$PKG_INSTALL redis-server" >> "$INSTALL_LOG" 2>&1; then
            echo_success "✅ Redis 安装完成"
            log_install "Redis安装: 成功"
        else
            echo_error "❌ Redis 安装失败"
            exit 1
        fi
    fi
    
    # 启动Redis服务
    echo_progress "启动Redis服务..."
    if sudo systemctl start redis >> "$INSTALL_LOG" 2>&1; then
        echo_success "✅ Redis 服务启动成功"
        sudo systemctl enable redis >> "$INSTALL_LOG" 2>&1
    elif brew services start redis >> "$INSTALL_LOG" 2>&1; then
        echo_success "✅ Redis 服务启动成功"
    else
        echo_warning "⚠️  Redis 服务启动失败，请手动启动"
    fi
    
    echo ""
}

# 安装Python依赖
install_python_dependencies() {
    echo_title "安装Python依赖包"
    
    local requirements_file="$PROJECT_ROOT/dash-fastapi-admin/requirements.txt"
    
    if [[ -f "$requirements_file" ]]; then
        echo_progress "安装项目依赖..."
        if python3 -m pip install -r "$requirements_file" >> "$INSTALL_LOG" 2>&1; then
            echo_success "✅ Python依赖安装完成"
            log_install "Python依赖: 安装成功"
        else
            echo_error "❌ Python依赖安装失败"
            echo_info "请检查日志: $INSTALL_LOG"
            exit 1
        fi
    else
        echo_warning "⚠️  未找到requirements.txt文件"
        
        # 安装基础依赖
        local basic_packages=("fastapi" "uvicorn" "dash" "mysql-connector-python" "redis" "requests")
        
        for package in "${basic_packages[@]}"; do
            echo_progress "安装 $package..."
            if python3 -m pip install "$package" >> "$INSTALL_LOG" 2>&1; then
                echo_success "✅ $package 安装完成"
            else
                echo_warning "⚠️  $package 安装失败"
            fi
        done
    fi
    
    echo ""
}

# 配置数据库
configure_database() {
    echo_title "配置数据库"
    
    # 创建数据库
    echo_progress "创建数据库..."
    if mysql -u root -e "CREATE DATABASE IF NOT EXISTS phone_marking_system;" >> "$INSTALL_LOG" 2>&1; then
        echo_success "✅ 数据库创建成功"
        log_install "数据库配置: 创建成功"
    else
        echo_warning "⚠️  数据库创建失败，可能已存在"
    fi
    
    # 运行数据库迁移（如果存在）
    local migration_script="$PROJECT_ROOT/dash-fastapi-admin/dash-fastapi-backend/migrations/init.sql"
    if [[ -f "$migration_script" ]]; then
        echo_progress "执行数据库迁移..."
        if mysql -u root phone_marking_system < "$migration_script" >> "$INSTALL_LOG" 2>&1; then
            echo_success "✅ 数据库迁移完成"
            log_install "数据库迁移: 成功"
        else
            echo_warning "⚠️  数据库迁移失败"
        fi
    fi
    
    echo ""
}

# 设置脚本权限
setup_permissions() {
    echo_title "设置脚本权限"
    
    # 设置执行权限
    local script_files=(
        "$SCRIPT_DIR/main.sh"
        "$SCRIPT_DIR/core/service_manager.sh"
        "$SCRIPT_DIR/core/health_checker.sh"
        "$SCRIPT_DIR/core/auto_repair.sh"
        "$SCRIPT_DIR/core/log_manager.sh"
        "$SCRIPT_DIR/core/ops_panel.sh"
    )
    
    for script in "${script_files[@]}"; do
        if [[ -f "$script" ]]; then
            chmod +x "$script"
            echo_success "✅ 设置权限: $(basename "$script")"
        fi
    done
    
    log_install "权限设置: 完成"
    echo ""
}

# 创建系统服务（可选）
create_systemd_service() {
    echo_title "创建系统服务"
    
    if ask_confirmation "是否创建systemd服务以便开机自启？"; then
        local service_file="/etc/systemd/system/phone-marking-system.service"
        
        cat > "/tmp/phone-marking-system.service" << EOF
[Unit]
Description=Phone Marking System
After=network.target mysql.service redis.service

[Service]
Type=forking
User=$(whoami)
WorkingDirectory=$PROJECT_ROOT
ExecStart=$SCRIPT_DIR/main.sh start
ExecStop=$SCRIPT_DIR/main.sh stop
ExecReload=$SCRIPT_DIR/main.sh restart
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF
        
        if sudo mv "/tmp/phone-marking-system.service" "$service_file"; then
            sudo systemctl daemon-reload
            sudo systemctl enable phone-marking-system
            echo_success "✅ 系统服务创建成功"
            log_install "系统服务: 创建成功"
        else
            echo_warning "⚠️  系统服务创建失败"
        fi
    fi
    
    echo ""
}

# 安装完成
installation_complete() {
    echo_title "安装完成"
    
    echo_success "🎉 电话标记系统运维管理环境安装完成！"
    echo ""
    
    echo_subtitle "使用方法："
    echo_info "启动所有服务:    $SCRIPT_DIR/main.sh start"
    echo_info "查看服务状态:    $SCRIPT_DIR/main.sh status"
    echo_info "运维管理面板:    $SCRIPT_DIR/main.sh panel"
    echo_info "查看帮助:        $SCRIPT_DIR/main.sh help"
    echo ""
    
    echo_subtitle "访问地址："
    echo_info "前端界面:        http://127.0.0.1:8089"
    echo_info "后端API:         http://127.0.0.1:9099"
    echo ""
    
    echo_subtitle "日志文件："
    echo_info "安装日志:        $INSTALL_LOG"
    echo_info "系统日志:        $SCRIPT_DIR/logs/system/"
    echo_info "服务日志:        $SCRIPT_DIR/logs/services/"
    echo ""
    
    if ask_confirmation "是否现在启动系统？"; then
        echo_progress "启动系统..."
        "$SCRIPT_DIR/main.sh" start
    fi
    
    log_install "安装完成: 成功"
}

# 主安装流程
main() {
    echo_color "$BLUE" "=================================================================="
    echo_color "$CYAN" "    电话标记系统运维管理环境安装程序"
    echo_color "$GREEN" "    一键安装 | 自动配置 | 智能检测"
    echo_color "$BLUE" "=================================================================="
    echo ""
    
    log_install "开始安装: 用户 $(whoami), 系统 $OSTYPE"
    
    # 执行安装步骤
    check_system
    install_system_dependencies
    install_python
    install_mysql
    install_redis
    install_python_dependencies
    configure_database
    setup_permissions
    create_systemd_service
    installation_complete
    
    echo_done "安装程序执行完毕！"
}

# 错误处理
trap 'echo_error "安装过程中发生错误，请检查日志: $INSTALL_LOG"; exit 1' ERR

# 执行主函数
main "$@"
