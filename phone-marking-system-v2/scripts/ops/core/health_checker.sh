#!/bin/bash

# =============================================================================
# 健康检查模块
# 提供服务健康状态检查、性能监控、异常检测等功能
# =============================================================================

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

# 加载依赖
source "$SCRIPT_DIR/utils/common.sh"
source "$SCRIPT_DIR/utils/colors.sh"
source "$SCRIPT_DIR/config/services.conf"
source "$SCRIPT_DIR/core/service_manager.sh"

# 检查单个服务健康状态
check_service_health() {
    local service_name="$1"
    local verbose="${2:-false}"
    
    if [[ "$verbose" == "true" ]]; then
        echo_progress "检查服务健康状态: $service_name"
    fi
    
    # 获取服务配置
    local service_upper=$(to_upper "$service_name")
    local health_check_var="${service_upper}_HEALTH_CHECK"
    local health_check="${!health_check_var}"
    local host_var="${service_upper}_HOST"
    local host="${!host_var:-127.0.0.1}"
    local port_var="${service_upper}_PORT"
    local port="${!port_var}"
    
    local status="unknown"
    local response_time="N/A"
    local error_msg=""
    
    # 基础进程检查
    if ! is_service_running "$service_name"; then
        status="stopped"
        error_msg="服务进程未运行"
    elif [[ -n "$health_check" ]]; then
        # 执行健康检查命令
        local start_time=$(date +%s)

        if eval "$health_check" >/dev/null 2>&1; then
            status="healthy"
            local end_time=$(date +%s)
            response_time=$(( (end_time - start_time) * 1000 ))ms
        else
            status="unhealthy"
            error_msg="健康检查失败"
        fi
    elif [[ -n "$port" ]]; then
        # 端口连通性检查
        local start_time=$(date +%s%N)
        
        if nc -z "$host" "$port" 2>/dev/null; then
            status="healthy"
            local end_time=$(date +%s%N)
            response_time=$(( (end_time - start_time) / 1000000 ))ms
        else
            status="unhealthy"
            error_msg="端口 $port 不可达"
        fi
    else
        # 仅进程检查
        status="running"
    fi
    
    # 输出结果
    if [[ "$verbose" == "true" ]]; then
        case "$status" in
            "healthy")
                echo_success "✅ $service_name: 健康 (响应时间: $response_time)"
                ;;
            "running")
                echo_info "🟡 $service_name: 运行中 (无健康检查)"
                ;;
            "unhealthy")
                echo_error "❌ $service_name: 不健康 ($error_msg)"
                ;;
            "stopped")
                echo_error "🔴 $service_name: 已停止"
                ;;
            *)
                echo_warning "⚪ $service_name: 状态未知"
                ;;
        esac
    fi
    
    # 记录日志
    log_system "INFO" "健康检查 $service_name: $status ($error_msg)"
    
    # 返回状态码
    case "$status" in
        "healthy"|"running") return 0 ;;
        *) return 1 ;;
    esac
}

# 检查系统资源
check_system_resources() {
    local verbose="${1:-false}"
    
    if [[ "$verbose" == "true" ]]; then
        echo_subtitle "系统资源检查"
    fi
    
    local issues=()
    
    # CPU使用率检查
    local cpu_usage=$(get_cpu_usage)
    
    if [[ $cpu_usage -gt ${CPU_ALERT_THRESHOLD:-80} ]]; then
        issues+=("CPU使用率过高: ${cpu_usage}%")
        if [[ "$verbose" == "true" ]]; then
            echo_warning "⚠️  CPU使用率: ${cpu_usage}% (阈值: ${CPU_ALERT_THRESHOLD}%)"
        fi
    elif [[ "$verbose" == "true" ]]; then
        echo_success "✅ CPU使用率: ${cpu_usage}%"
    fi
    
    # 内存使用率检查
    local memory_usage=$(get_memory_usage)
    
    if [[ $memory_usage -gt ${MEMORY_ALERT_THRESHOLD:-85} ]]; then
        issues+=("内存使用率过高: ${memory_usage}%")
        if [[ "$verbose" == "true" ]]; then
            echo_warning "⚠️  内存使用率: ${memory_usage}% (阈值: ${MEMORY_ALERT_THRESHOLD}%)"
        fi
    elif [[ "$verbose" == "true" ]]; then
        echo_success "✅ 内存使用率: ${memory_usage}%"
    fi
    
    # 磁盘使用率检查
    local disk_usage=$(df / | awk 'NR==2{print $5}' | sed 's/%//')
    
    if [[ $disk_usage -gt ${DISK_ALERT_THRESHOLD:-90} ]]; then
        issues+=("磁盘使用率过高: ${disk_usage}%")
        if [[ "$verbose" == "true" ]]; then
            echo_warning "⚠️  磁盘使用率: ${disk_usage}% (阈值: ${DISK_ALERT_THRESHOLD}%)"
        fi
    elif [[ "$verbose" == "true" ]]; then
        echo_success "✅ 磁盘使用率: ${disk_usage}%"
    fi
    
    # 网络连接数检查
    local connection_count=$(netstat -an | grep ESTABLISHED | wc -l)
    
    if [[ $connection_count -gt ${CONNECTION_ALERT_THRESHOLD:-1000} ]]; then
        issues+=("网络连接数过多: $connection_count")
        if [[ "$verbose" == "true" ]]; then
            echo_warning "⚠️  网络连接数: $connection_count (阈值: ${CONNECTION_ALERT_THRESHOLD})"
        fi
    elif [[ "$verbose" == "true" ]]; then
        echo_success "✅ 网络连接数: $connection_count"
    fi
    
    # 返回问题列表
    if [[ ${#issues[@]} -gt 0 ]]; then
        if [[ "$verbose" == "true" ]]; then
            echo_error "发现 ${#issues[@]} 个系统资源问题"
        fi
        printf '%s\n' "${issues[@]}"
        return 1
    else
        if [[ "$verbose" == "true" ]]; then
            echo_success "系统资源状态正常"
        fi
        return 0
    fi
}

# 检查数据库连接
check_database_connection() {
    local verbose="${1:-false}"
    
    if [[ "$verbose" == "true" ]]; then
        echo_progress "检查数据库连接..."
    fi
    
    # MySQL连接检查
    if command_exists mysql; then
        if mysql -h"$MYSQL_HOST" -P"$MYSQL_PORT" -u"$MYSQL_USER" -e "SELECT 1" >/dev/null 2>&1; then
            if [[ "$verbose" == "true" ]]; then
                echo_success "✅ MySQL连接正常"
            fi
        else
            if [[ "$verbose" == "true" ]]; then
                echo_error "❌ MySQL连接失败"
            fi
            return 1
        fi
    fi
    
    # Redis连接检查
    if command_exists redis-cli; then
        if redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" ping >/dev/null 2>&1; then
            if [[ "$verbose" == "true" ]]; then
                echo_success "✅ Redis连接正常"
            fi
        else
            if [[ "$verbose" == "true" ]]; then
                echo_error "❌ Redis连接失败"
            fi
            return 1
        fi
    fi
    
    return 0
}

# 检查端口占用
check_port_conflicts() {
    local verbose="${1:-false}"
    
    if [[ "$verbose" == "true" ]]; then
        echo_subtitle "端口冲突检查"
    fi
    
    local conflicts=()
    
    for service in "${ALL_SERVICES[@]}"; do
        local service_upper=$(to_upper "$service")
        local port_var="${service_upper}_PORT"
        local port="${!port_var}"
        
        if [[ -n "$port" ]]; then
            # 检查端口是否被其他进程占用
            local pid=$(lsof -ti:$port 2>/dev/null)
            if [[ -n "$pid" ]]; then
                local process_name=$(ps -p $pid -o comm= 2>/dev/null)
                if [[ -n "$process_name" ]] && [[ "$process_name" != *"$service"* ]]; then
                    conflicts+=("端口 $port 被进程 $process_name (PID: $pid) 占用")
                    if [[ "$verbose" == "true" ]]; then
                        echo_warning "⚠️  端口 $port ($service) 被 $process_name 占用"
                    fi
                fi
            fi
        fi
    done
    
    if [[ ${#conflicts[@]} -gt 0 ]]; then
        if [[ "$verbose" == "true" ]]; then
            echo_error "发现 ${#conflicts[@]} 个端口冲突"
        fi
        printf '%s\n' "${conflicts[@]}"
        return 1
    else
        if [[ "$verbose" == "true" ]]; then
            echo_success "无端口冲突"
        fi
        return 0
    fi
}

# 生成健康报告
generate_health_report() {
    local report_file="$SCRIPT_DIR/logs/system/health_report_$(date +%Y%m%d_%H%M%S).log"
    
    echo_title "生成健康检查报告"
    
    {
        echo "========================================"
        echo "健康检查报告"
        echo "生成时间: $(get_timestamp)"
        echo "========================================"
        echo ""
        
        echo "=== 服务状态 ==="
        for service in "${ALL_SERVICES[@]}"; do
            if check_service_health "$service" false; then
                echo "✅ $service: 健康"
            else
                echo "❌ $service: 异常"
            fi
        done
        echo ""
        
        echo "=== 系统资源 ==="
        check_system_resources false
        echo ""
        
        echo "=== 数据库连接 ==="
        if check_database_connection false; then
            echo "✅ 数据库连接正常"
        else
            echo "❌ 数据库连接异常"
        fi
        echo ""
        
        echo "=== 端口状态 ==="
        check_port_conflicts false
        echo ""
        
        echo "=== 系统信息 ==="
        get_system_info
        
    } > "$report_file"
    
    echo_success "健康报告已生成: $report_file"
    return 0
}

# 检查所有服务
check_all() {
    echo_title "系统健康检查"
    
    local failed_checks=0
    
    # 服务健康检查
    echo_subtitle "服务健康状态"
    for service in "${ALL_SERVICES[@]}"; do
        if ! check_service_health "$service" true; then
            ((failed_checks++))
        fi
    done
    
    echo ""
    
    # 系统资源检查
    if ! check_system_resources true; then
        ((failed_checks++))
    fi
    
    echo ""
    
    # 数据库连接检查
    if ! check_database_connection true; then
        ((failed_checks++))
    fi
    
    echo ""
    
    # 端口冲突检查
    if ! check_port_conflicts true; then
        ((failed_checks++))
    fi
    
    echo ""
    
    # 总结
    if [[ $failed_checks -eq 0 ]]; then
        echo_done "🎉 所有健康检查通过！"
    else
        echo_error "❌ 发现 $failed_checks 个问题，建议执行自动修复"
    fi
    
    # 生成报告
    generate_health_report
    
    return $failed_checks
}

# 主函数
main() {
    case "${1:-check_all}" in
        "check_all")
            check_all
            ;;
        "check_service")
            check_service_health "$2" true
            ;;
        "check_resources")
            check_system_resources true
            ;;
        "check_database")
            check_database_connection true
            ;;
        "check_ports")
            check_port_conflicts true
            ;;
        "report")
            generate_health_report
            ;;
        *)
            echo_error "未知命令: $1"
            echo "用法: $0 {check_all|check_service|check_resources|check_database|check_ports|report} [service_name]"
            exit 1
            ;;
    esac
}

# 如果直接执行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
