#!/bin/bash

# =============================================================================
# 数据备份管理模块
# 提供数据库备份、配置文件备份、日志备份、系统备份等功能
# =============================================================================

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# 加载依赖
source "$SCRIPT_DIR/utils/common.sh"
source "$SCRIPT_DIR/utils/colors.sh"
source "$SCRIPT_DIR/config/services.conf"

# 备份配置
BACKUP_ROOT_DIR="$SCRIPT_DIR/backups"
BACKUP_RETENTION_DAYS=${BACKUP_RETENTION_DAYS:-30}
BACKUP_COMPRESS_ENABLED=true

# 创建备份目录
init_backup_dirs() {
    local dirs=(
        "$BACKUP_ROOT_DIR/database"
        "$BACKUP_ROOT_DIR/config"
        "$BACKUP_ROOT_DIR/logs"
        "$BACKUP_ROOT_DIR/system"
        "$BACKUP_ROOT_DIR/full"
    )
    
    for dir in "${dirs[@]}"; do
        mkdir -p "$dir"
    done
}

# 数据库备份
backup_database() {
    local backup_name="${1:-database_$(date +%Y%m%d_%H%M%S)}"
    local backup_file="$BACKUP_ROOT_DIR/database/${backup_name}.sql"
    
    echo_title "数据库备份"
    echo_info "备份文件: $backup_file"
    
    # 检查MySQL连接
    if ! mysql -h"$MYSQL_HOST" -P"$MYSQL_PORT" -u"$MYSQL_USER" -e "SELECT 1" >/dev/null 2>&1; then
        echo_error "❌ 无法连接到MySQL数据库"
        return 1
    fi
    
    echo_progress "正在备份数据库..."
    
    # 执行数据库备份
    if mysqldump -h"$MYSQL_HOST" -P"$MYSQL_PORT" -u"$MYSQL_USER" \
                 --single-transaction \
                 --routines \
                 --triggers \
                 --events \
                 --all-databases > "$backup_file" 2>/dev/null; then
        
        local file_size=$(stat -c%s "$backup_file" 2>/dev/null || stat -f%z "$backup_file" 2>/dev/null || echo "0")
        echo_success "✅ 数据库备份完成"
        show_key_value "备份文件" "$backup_file"
        show_key_value "文件大小" "$(format_size $file_size)"
        
        # 压缩备份文件
        if [[ "$BACKUP_COMPRESS_ENABLED" == "true" ]] && command_exists gzip; then
            echo_progress "压缩备份文件..."
            if gzip "$backup_file"; then
                local compressed_size=$(stat -c%s "${backup_file}.gz" 2>/dev/null || stat -f%z "${backup_file}.gz" 2>/dev/null || echo "0")
                echo_success "✅ 备份文件已压缩"
                show_key_value "压缩后大小" "$(format_size $compressed_size)"
                show_key_value "压缩率" "$(( (file_size - compressed_size) * 100 / file_size ))%"
                backup_file="${backup_file}.gz"
            fi
        fi
        
        log_system "INFO" "数据库备份完成: $backup_file"
        echo "$backup_file"
        return 0
    else
        echo_error "❌ 数据库备份失败"
        rm -f "$backup_file"
        log_system "ERROR" "数据库备份失败"
        return 1
    fi
}

# 配置文件备份
backup_config() {
    local backup_name="${1:-config_$(date +%Y%m%d_%H%M%S)}"
    local backup_file="$BACKUP_ROOT_DIR/config/${backup_name}.tar.gz"
    
    echo_title "配置文件备份"
    echo_info "备份文件: $backup_file"
    
    # 配置文件列表
    local config_files=(
        "$PROJECT_ROOT/dash-fastapi-admin/dash-fastapi-backend/config"
        "$PROJECT_ROOT/dash-fastapi-admin/dash-fastapi-frontend/config"
        "$SCRIPT_DIR/config"
    )
    
    # 检查配置文件是否存在
    local existing_files=()
    for config_path in "${config_files[@]}"; do
        if [[ -e "$config_path" ]]; then
            existing_files+=("$config_path")
        fi
    done
    
    if [[ ${#existing_files[@]} -eq 0 ]]; then
        echo_warning "⚠️  未找到配置文件"
        return 1
    fi
    
    echo_progress "正在备份配置文件..."
    
    # 创建临时目录
    local temp_dir=$(mktemp -d)
    local backup_root="$temp_dir/config_backup"
    mkdir -p "$backup_root"
    
    # 复制配置文件
    for config_path in "${existing_files[@]}"; do
        local relative_path=$(realpath --relative-to="$PROJECT_ROOT" "$config_path" 2>/dev/null || echo "$(basename "$config_path")")
        local dest_dir="$backup_root/$(dirname "$relative_path")"
        
        mkdir -p "$dest_dir"
        cp -r "$config_path" "$dest_dir/" 2>/dev/null
        echo_info "✓ 已备份: $relative_path"
    done
    
    # 创建备份信息文件
    cat > "$backup_root/backup_info.txt" << EOF
备份时间: $(get_timestamp)
备份类型: 配置文件备份
系统版本: $(uname -a)
用户: $(whoami)
项目路径: $PROJECT_ROOT
备份文件:
EOF
    
    for config_path in "${existing_files[@]}"; do
        echo "  - $config_path" >> "$backup_root/backup_info.txt"
    done
    
    # 创建压缩包
    if tar -czf "$backup_file" -C "$temp_dir" config_backup 2>/dev/null; then
        local file_size=$(stat -c%s "$backup_file" 2>/dev/null || stat -f%z "$backup_file" 2>/dev/null || echo "0")
        echo_success "✅ 配置文件备份完成"
        show_key_value "备份文件" "$backup_file"
        show_key_value "文件大小" "$(format_size $file_size)"
        show_key_value "包含文件" "${#existing_files[@]} 个配置目录"
        
        log_system "INFO" "配置文件备份完成: $backup_file"
        
        # 清理临时目录
        rm -rf "$temp_dir"
        
        echo "$backup_file"
        return 0
    else
        echo_error "❌ 配置文件备份失败"
        rm -rf "$temp_dir"
        rm -f "$backup_file"
        log_system "ERROR" "配置文件备份失败"
        return 1
    fi
}

# 日志备份
backup_logs() {
    local backup_name="${1:-logs_$(date +%Y%m%d_%H%M%S)}"
    local backup_file="$BACKUP_ROOT_DIR/logs/${backup_name}.tar.gz"
    local days="${2:-7}"
    
    echo_title "日志文件备份"
    echo_info "备份文件: $backup_file"
    echo_info "备份最近 $days 天的日志"
    
    if [[ ! -d "$SCRIPT_DIR/logs" ]]; then
        echo_warning "⚠️  日志目录不存在"
        return 1
    fi
    
    echo_progress "正在备份日志文件..."
    
    # 查找最近的日志文件
    local log_files=()
    while IFS= read -r -d '' file; do
        log_files+=("$file")
    done < <(find "$SCRIPT_DIR/logs" -name "*.log" -o -name "*.log.gz" -mtime -$days -print0 2>/dev/null)
    
    if [[ ${#log_files[@]} -eq 0 ]]; then
        echo_warning "⚠️  未找到最近 $days 天的日志文件"
        return 1
    fi
    
    # 创建临时目录
    local temp_dir=$(mktemp -d)
    local backup_root="$temp_dir/logs_backup"
    mkdir -p "$backup_root"
    
    # 复制日志文件，保持目录结构
    for log_file in "${log_files[@]}"; do
        local relative_path=$(realpath --relative-to="$SCRIPT_DIR/logs" "$log_file" 2>/dev/null || echo "$(basename "$log_file")")
        local dest_dir="$backup_root/$(dirname "$relative_path")"
        
        mkdir -p "$dest_dir"
        cp "$log_file" "$dest_dir/" 2>/dev/null
    done
    
    # 创建备份信息文件
    cat > "$backup_root/backup_info.txt" << EOF
备份时间: $(get_timestamp)
备份类型: 日志文件备份
备份天数: $days 天
文件数量: ${#log_files[@]} 个
总大小: $(du -sh "$backup_root" | cut -f1)
EOF
    
    # 创建压缩包
    if tar -czf "$backup_file" -C "$temp_dir" logs_backup 2>/dev/null; then
        local file_size=$(stat -c%s "$backup_file" 2>/dev/null || stat -f%z "$backup_file" 2>/dev/null || echo "0")
        echo_success "✅ 日志文件备份完成"
        show_key_value "备份文件" "$backup_file"
        show_key_value "文件大小" "$(format_size $file_size)"
        show_key_value "包含文件" "${#log_files[@]} 个日志文件"
        
        log_system "INFO" "日志文件备份完成: $backup_file"
        
        # 清理临时目录
        rm -rf "$temp_dir"
        
        echo "$backup_file"
        return 0
    else
        echo_error "❌ 日志文件备份失败"
        rm -rf "$temp_dir"
        rm -f "$backup_file"
        log_system "ERROR" "日志文件备份失败"
        return 1
    fi
}

# 系统备份
backup_system() {
    local backup_name="${1:-system_$(date +%Y%m%d_%H%M%S)}"
    local backup_file="$BACKUP_ROOT_DIR/system/${backup_name}.tar.gz"
    
    echo_title "系统文件备份"
    echo_info "备份文件: $backup_file"
    
    echo_progress "正在备份系统文件..."
    
    # 系统文件列表
    local system_files=(
        "$SCRIPT_DIR"
        "$PROJECT_ROOT/dash-fastapi-admin/requirements.txt"
        "$PROJECT_ROOT/dash-fastapi-admin/phone_marking_app.py"
    )
    
    # 排除的文件和目录
    local exclude_patterns=(
        "*.log"
        "*.log.gz"
        "backups/*"
        "__pycache__/*"
        "*.pyc"
        ".git/*"
        "node_modules/*"
    )
    
    # 构建tar排除参数
    local exclude_args=()
    for pattern in "${exclude_patterns[@]}"; do
        exclude_args+=("--exclude=$pattern")
    done
    
    # 创建临时目录
    local temp_dir=$(mktemp -d)
    local backup_root="$temp_dir/system_backup"
    mkdir -p "$backup_root"
    
    # 复制系统文件
    for system_path in "${system_files[@]}"; do
        if [[ -e "$system_path" ]]; then
            local relative_path=$(realpath --relative-to="$PROJECT_ROOT" "$system_path" 2>/dev/null || echo "$(basename "$system_path")")
            local dest_dir="$backup_root/$(dirname "$relative_path")"
            
            mkdir -p "$dest_dir"
            cp -r "$system_path" "$dest_dir/" 2>/dev/null
            echo_info "✓ 已备份: $relative_path"
        fi
    done
    
    # 创建系统信息文件
    cat > "$backup_root/system_info.txt" << EOF
备份时间: $(get_timestamp)
备份类型: 系统文件备份
系统信息: $(uname -a)
用户: $(whoami)
项目路径: $PROJECT_ROOT
Python版本: $(python3 --version 2>/dev/null || echo "未安装")
MySQL状态: $(systemctl is-active mysql 2>/dev/null || echo "未知")
Redis状态: $(systemctl is-active redis 2>/dev/null || echo "未知")
磁盘使用: $(df -h / | awk 'NR==2{print $5}')
内存使用: $(free -h | awk 'NR==2{printf "%.1f%%", $3*100/$2}')
EOF
    
    # 创建压缩包
    if tar -czf "$backup_file" -C "$temp_dir" system_backup 2>/dev/null; then
        local file_size=$(stat -c%s "$backup_file" 2>/dev/null || stat -f%z "$backup_file" 2>/dev/null || echo "0")
        echo_success "✅ 系统文件备份完成"
        show_key_value "备份文件" "$backup_file"
        show_key_value "文件大小" "$(format_size $file_size)"
        
        log_system "INFO" "系统文件备份完成: $backup_file"
        
        # 清理临时目录
        rm -rf "$temp_dir"
        
        echo "$backup_file"
        return 0
    else
        echo_error "❌ 系统文件备份失败"
        rm -rf "$temp_dir"
        rm -f "$backup_file"
        log_system "ERROR" "系统文件备份失败"
        return 1
    fi
}

# 完整备份
backup_full() {
    local backup_name="${1:-full_$(date +%Y%m%d_%H%M%S)}"
    local backup_dir="$BACKUP_ROOT_DIR/full/$backup_name"
    
    echo_title "完整系统备份"
    echo_info "备份目录: $backup_dir"
    
    mkdir -p "$backup_dir"
    
    local backup_files=()
    local failed_backups=()
    
    # 1. 数据库备份
    echo_subtitle "1. 备份数据库"
    if db_backup=$(backup_database "${backup_name}_database"); then
        mv "$db_backup" "$backup_dir/"
        backup_files+=("数据库: $(basename "$db_backup")")
    else
        failed_backups+=("数据库备份")
    fi
    
    echo ""
    
    # 2. 配置文件备份
    echo_subtitle "2. 备份配置文件"
    if config_backup=$(backup_config "${backup_name}_config"); then
        mv "$config_backup" "$backup_dir/"
        backup_files+=("配置文件: $(basename "$config_backup")")
    else
        failed_backups+=("配置文件备份")
    fi
    
    echo ""
    
    # 3. 日志备份
    echo_subtitle "3. 备份日志文件"
    if log_backup=$(backup_logs "${backup_name}_logs" 7); then
        mv "$log_backup" "$backup_dir/"
        backup_files+=("日志文件: $(basename "$log_backup")")
    else
        failed_backups+=("日志文件备份")
    fi
    
    echo ""
    
    # 4. 系统文件备份
    echo_subtitle "4. 备份系统文件"
    if system_backup=$(backup_system "${backup_name}_system"); then
        mv "$system_backup" "$backup_dir/"
        backup_files+=("系统文件: $(basename "$system_backup")")
    else
        failed_backups+=("系统文件备份")
    fi
    
    # 创建备份清单
    cat > "$backup_dir/backup_manifest.txt" << EOF
完整备份清单
============

备份时间: $(get_timestamp)
备份名称: $backup_name
备份目录: $backup_dir

成功备份的组件:
EOF
    
    for item in "${backup_files[@]}"; do
        echo "  ✅ $item" >> "$backup_dir/backup_manifest.txt"
    done
    
    if [[ ${#failed_backups[@]} -gt 0 ]]; then
        echo "" >> "$backup_dir/backup_manifest.txt"
        echo "失败的备份组件:" >> "$backup_dir/backup_manifest.txt"
        for item in "${failed_backups[@]}"; do
            echo "  ❌ $item" >> "$backup_dir/backup_manifest.txt"
        done
    fi
    
    # 计算总大小
    local total_size=$(du -sb "$backup_dir" 2>/dev/null | cut -f1)
    echo "" >> "$backup_dir/backup_manifest.txt"
    echo "备份统计:" >> "$backup_dir/backup_manifest.txt"
    echo "  总大小: $(format_size ${total_size:-0})" >> "$backup_dir/backup_manifest.txt"
    echo "  文件数: $(find "$backup_dir" -type f | wc -l)" >> "$backup_dir/backup_manifest.txt"
    
    echo ""
    echo_title "完整备份结果"
    
    if [[ ${#backup_files[@]} -gt 0 ]]; then
        echo_success "✅ 成功备份的组件:"
        for item in "${backup_files[@]}"; do
            echo_success "  ✓ $item"
        done
    fi
    
    if [[ ${#failed_backups[@]} -gt 0 ]]; then
        echo_error "❌ 失败的备份组件:"
        for item in "${failed_backups[@]}"; do
            echo_error "  ✗ $item"
        done
    fi
    
    echo ""
    show_key_value "备份目录" "$backup_dir"
    show_key_value "总大小" "$(format_size ${total_size:-0})"
    show_key_value "成功组件" "${#backup_files[@]} / $((${#backup_files[@]} + ${#failed_backups[@]}))"
    
    log_system "INFO" "完整备份完成: $backup_dir (成功: ${#backup_files[@]}, 失败: ${#failed_backups[@]})"
    
    echo "$backup_dir"
    return $([[ ${#failed_backups[@]} -eq 0 ]] && echo 0 || echo 1)
}

# 列出备份文件
list_backups() {
    local backup_type="${1:-all}"

    echo_title "备份文件列表"

    case "$backup_type" in
        "database"|"db")
            echo_subtitle "数据库备份"
            if [[ -d "$BACKUP_ROOT_DIR/database" ]]; then
                ls -la "$BACKUP_ROOT_DIR/database/" | grep -E '\.(sql|sql\.gz)$' || echo_info "暂无数据库备份"
            fi
            ;;
        "config")
            echo_subtitle "配置文件备份"
            if [[ -d "$BACKUP_ROOT_DIR/config" ]]; then
                ls -la "$BACKUP_ROOT_DIR/config/" | grep '\.tar\.gz$' || echo_info "暂无配置文件备份"
            fi
            ;;
        "logs")
            echo_subtitle "日志文件备份"
            if [[ -d "$BACKUP_ROOT_DIR/logs" ]]; then
                ls -la "$BACKUP_ROOT_DIR/logs/" | grep '\.tar\.gz$' || echo_info "暂无日志文件备份"
            fi
            ;;
        "system")
            echo_subtitle "系统文件备份"
            if [[ -d "$BACKUP_ROOT_DIR/system" ]]; then
                ls -la "$BACKUP_ROOT_DIR/system/" | grep '\.tar\.gz$' || echo_info "暂无系统文件备份"
            fi
            ;;
        "full")
            echo_subtitle "完整备份"
            if [[ -d "$BACKUP_ROOT_DIR/full" ]]; then
                ls -la "$BACKUP_ROOT_DIR/full/" || echo_info "暂无完整备份"
            fi
            ;;
        "all")
            list_backups "database"
            echo ""
            list_backups "config"
            echo ""
            list_backups "logs"
            echo ""
            list_backups "system"
            echo ""
            list_backups "full"
            ;;
    esac
}

# 清理过期备份
cleanup_old_backups() {
    local days="${1:-$BACKUP_RETENTION_DAYS}"

    echo_title "清理过期备份"
    echo_info "清理 $days 天前的备份文件"

    local deleted_count=0
    local freed_space=0

    # 清理各类备份
    local backup_dirs=("database" "config" "logs" "system" "full")

    for backup_dir in "${backup_dirs[@]}"; do
        local full_path="$BACKUP_ROOT_DIR/$backup_dir"

        if [[ -d "$full_path" ]]; then
            echo_subtitle "清理 $backup_dir 备份"

            while IFS= read -r -d '' file; do
                local file_size=$(stat -c%s "$file" 2>/dev/null || stat -f%z "$file" 2>/dev/null || echo "0")
                local file_name=$(basename "$file")

                echo_info "🗑️  删除过期备份: $file_name ($(format_size $file_size))"

                if rm -rf "$file"; then
                    ((deleted_count++))
                    freed_space=$((freed_space + file_size))
                else
                    echo_warning "⚠️  删除失败: $file_name"
                fi
            done < <(find "$full_path" -type f -mtime +$days -print0 2>/dev/null)

            # 清理空目录
            find "$full_path" -type d -empty -delete 2>/dev/null
        fi
    done

    echo ""
    echo_success "✅ 清理完成"
    show_key_value "删除文件数" "$deleted_count"
    show_key_value "释放空间" "$(format_size $freed_space)"

    log_system "INFO" "备份清理完成: 删除 $deleted_count 个文件，释放 $(format_size $freed_space) 空间"
}

# 备份统计信息
backup_statistics() {
    echo_title "备份统计信息"

    local backup_dirs=("database" "config" "logs" "system" "full")
    local total_files=0
    local total_size=0

    for backup_dir in "${backup_dirs[@]}"; do
        local full_path="$BACKUP_ROOT_DIR/$backup_dir"

        if [[ -d "$full_path" ]]; then
            echo_subtitle "$backup_dir 备份"

            local file_count=$(find "$full_path" -type f 2>/dev/null | wc -l)
            local dir_size=$(du -sb "$full_path" 2>/dev/null | cut -f1)

            echo_table_header "最新备份" "文件数" "总大小" "最后备份时间"

            if [[ $file_count -gt 0 ]]; then
                local latest_file=$(find "$full_path" -type f -printf '%T@ %p\n' 2>/dev/null | sort -n | tail -1 | cut -d' ' -f2-)
                local latest_time=""
                if [[ -n "$latest_file" ]]; then
                    latest_time=$(stat -c%y "$latest_file" 2>/dev/null | cut -d' ' -f1 || echo "未知")
                fi

                echo_table_row "$(basename "$latest_file")" "$file_count" "$(format_size ${dir_size:-0})" "$latest_time"
            else
                echo_table_row "无" "0" "0B" "从未备份"
            fi

            total_files=$((total_files + file_count))
            total_size=$((total_size + ${dir_size:-0}))
            echo ""
        fi
    done

    echo_subtitle "总计统计"
    show_key_value "总文件数" "$total_files 个"
    show_key_value "总大小" "$(format_size $total_size)"
    show_key_value "备份目录" "$BACKUP_ROOT_DIR"
    show_key_value "保留天数" "$BACKUP_RETENTION_DAYS 天"
}

# 主函数
main() {
    # 初始化备份目录
    init_backup_dirs

    case "${1:-help}" in
        "database"|"db")
            backup_database "$2"
            ;;
        "config")
            backup_config "$2"
            ;;
        "logs")
            backup_logs "$2" "$3"
            ;;
        "system")
            backup_system "$2"
            ;;
        "full")
            backup_full "$2"
            ;;
        "list")
            list_backups "$2"
            ;;
        "cleanup")
            cleanup_old_backups "$2"
            ;;
        "stats")
            backup_statistics
            ;;
        *)
            echo_error "未知命令: $1"
            echo "用法: $0 {database|config|logs|system|full|list|cleanup|stats} [参数...]"
            echo ""
            echo "备份命令:"
            echo "  database [名称]           # 备份数据库"
            echo "  config [名称]             # 备份配置文件"
            echo "  logs [名称] [天数]        # 备份日志文件"
            echo "  system [名称]             # 备份系统文件"
            echo "  full [名称]               # 完整备份"
            echo ""
            echo "管理命令:"
            echo "  list [类型]               # 列出备份文件"
            echo "  cleanup [天数]            # 清理过期备份"
            echo "  stats                     # 显示统计信息"
            echo ""
            echo "示例:"
            echo "  $0 database               # 备份数据库"
            echo "  $0 full weekly_backup     # 完整备份"
            echo "  $0 list all               # 列出所有备份"
            echo "  $0 cleanup 30             # 清理30天前的备份"
            exit 1
            ;;
    esac
}

# 如果直接执行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
