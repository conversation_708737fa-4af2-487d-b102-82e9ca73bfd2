#!/bin/bash

# =============================================================================
# 日志管理模块
# 提供分类日志查看、时间过滤、友好格式显示、日志轮转等功能
# =============================================================================

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

# 加载依赖
source "$SCRIPT_DIR/utils/common.sh"
source "$SCRIPT_DIR/utils/colors.sh"
source "$SCRIPT_DIR/config/services.conf"

# 解析时间范围
parse_time_range() {
    local time_input="$1"
    local start_time=""
    local end_time=""
    
    case "$time_input" in
        "1h"|"hour")
            start_time=$(date -d "1 hour ago" '+%Y-%m-%d %H:%M:%S')
            end_time=$(date '+%Y-%m-%d %H:%M:%S')
            ;;
        "6h")
            start_time=$(date -d "6 hours ago" '+%Y-%m-%d %H:%M:%S')
            end_time=$(date '+%Y-%m-%d %H:%M:%S')
            ;;
        "12h")
            start_time=$(date -d "12 hours ago" '+%Y-%m-%d %H:%M:%S')
            end_time=$(date '+%Y-%m-%d %H:%M:%S')
            ;;
        "today"|"1d"|"day")
            start_time=$(date '+%Y-%m-%d 00:00:00')
            end_time=$(date '+%Y-%m-%d %H:%M:%S')
            ;;
        "yesterday")
            start_time=$(date -d "yesterday" '+%Y-%m-%d 00:00:00')
            end_time=$(date -d "yesterday" '+%Y-%m-%d 23:59:59')
            ;;
        "3d")
            start_time=$(date -d "3 days ago" '+%Y-%m-%d 00:00:00')
            end_time=$(date '+%Y-%m-%d %H:%M:%S')
            ;;
        "week"|"7d")
            start_time=$(date -d "7 days ago" '+%Y-%m-%d 00:00:00')
            end_time=$(date '+%Y-%m-%d %H:%M:%S')
            ;;
        "month"|"30d")
            start_time=$(date -d "30 days ago" '+%Y-%m-%d 00:00:00')
            end_time=$(date '+%Y-%m-%d %H:%M:%S')
            ;;
        "")
            # 默认显示最近1小时
            start_time=$(date -d "1 hour ago" '+%Y-%m-%d %H:%M:%S')
            end_time=$(date '+%Y-%m-%d %H:%M:%S')
            ;;
        *)
            # 尝试解析自定义时间格式
            if [[ "$time_input" =~ ^[0-9]{4}-[0-9]{2}-[0-9]{2}$ ]]; then
                start_time="$time_input 00:00:00"
                end_time="$time_input 23:59:59"
            else
                echo_error "不支持的时间格式: $time_input"
                echo_info "支持的格式: 1h, 6h, 12h, today, yesterday, 3d, week, month, YYYY-MM-DD"
                return 1
            fi
            ;;
    esac
    
    echo "$start_time|$end_time"
}

# 格式化日志输出
format_log_line() {
    local line="$1"
    local service="$2"
    
    # 提取时间戳
    if [[ "$line" =~ ^\[([0-9]{4}-[0-9]{2}-[0-9]{2}\ [0-9]{2}:[0-9]{2}:[0-9]{2})\] ]]; then
        local timestamp="${BASH_REMATCH[1]}"
        local content="${line#*] }"
        
        # 根据日志级别着色
        if [[ "$content" =~ ERROR|CRITICAL|FATAL ]]; then
            echo_color "$RED" "[$timestamp] [$service] $content"
        elif [[ "$content" =~ WARN|WARNING ]]; then
            echo_color "$YELLOW" "[$timestamp] [$service] $content"
        elif [[ "$content" =~ INFO ]]; then
            echo_color "$GREEN" "[$timestamp] [$service] $content"
        elif [[ "$content" =~ DEBUG ]]; then
            echo_color "$GRAY" "[$timestamp] [$service] $content"
        else
            echo_color "$WHITE" "[$timestamp] [$service] $content"
        fi
    else
        # 没有时间戳的行
        echo_color "$GRAY" "[$(date '+%Y-%m-%d %H:%M:%S')] [$service] $line"
    fi
}

# 查看服务日志
view_service_logs() {
    local service_name="$1"
    local time_range="$2"
    local lines="${3:-100}"
    
    # 获取日志文件路径
    local service_upper=$(to_upper "$service_name")
    local log_file_var="${service_upper}_LOG_FILE"
    local log_file="${!log_file_var}"
    
    if [[ -z "$log_file" ]]; then
        # 尝试默认路径
        log_file="$SCRIPT_DIR/logs/services/${service_name}_$(date +%Y%m%d).log"
    fi
    
    if [[ ! -f "$log_file" ]]; then
        echo_warning "日志文件不存在: $log_file"
        
        # 尝试查找其他日期的日志文件
        local log_dir="$(dirname "$log_file")"
        local log_pattern="${service_name}_*.log"
        
        if [[ -d "$log_dir" ]]; then
            local available_logs=($(find "$log_dir" -name "$log_pattern" -type f | sort -r))
            if [[ ${#available_logs[@]} -gt 0 ]]; then
                echo_info "找到以下日志文件："
                for i in "${!available_logs[@]}"; do
                    echo "  $((i+1)). $(basename "${available_logs[i]}")"
                done
                echo ""
                read -p "请选择要查看的日志文件 (1-${#available_logs[@]}): " choice
                if [[ "$choice" =~ ^[0-9]+$ ]] && [[ $choice -ge 1 ]] && [[ $choice -le ${#available_logs[@]} ]]; then
                    log_file="${available_logs[$((choice-1))]}"
                else
                    echo_error "无效选择"
                    return 1
                fi
            else
                echo_error "未找到 $service_name 的日志文件"
                return 1
            fi
        else
            echo_error "日志目录不存在: $log_dir"
            return 1
        fi
    fi
    
    echo_title "查看 $service_name 服务日志"
    echo_info "日志文件: $log_file"
    
    # 解析时间范围
    if [[ -n "$time_range" ]]; then
        local time_filter=$(parse_time_range "$time_range")
        if [[ $? -ne 0 ]]; then
            return 1
        fi
        
        local start_time="${time_filter%|*}"
        local end_time="${time_filter#*|}"
        
        echo_info "时间范围: $start_time 到 $end_time"
        echo_separator
        
        # 使用awk过滤时间范围
        awk -v start="$start_time" -v end="$end_time" '
        {
            if (match($0, /^\[([0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2})\]/, arr)) {
                timestamp = arr[1]
                if (timestamp >= start && timestamp <= end) {
                    print $0
                }
            }
        }' "$log_file" | tail -n "$lines" | while IFS= read -r line; do
            format_log_line "$line" "$service_name"
        done
    else
        echo_info "显示最近 $lines 行日志"
        echo_separator
        
        tail -n "$lines" "$log_file" | while IFS= read -r line; do
            format_log_line "$line" "$service_name"
        done
    fi
}

# 查看系统日志
view_system_logs() {
    local time_range="$1"
    local lines="${2:-100}"
    
    local system_log="$SCRIPT_DIR/logs/system/system_$(date +%Y%m%d).log"
    
    echo_title "查看系统日志"
    
    if [[ ! -f "$system_log" ]]; then
        echo_warning "系统日志文件不存在: $system_log"
        return 1
    fi
    
    echo_info "日志文件: $system_log"
    
    if [[ -n "$time_range" ]]; then
        local time_filter=$(parse_time_range "$time_range")
        if [[ $? -ne 0 ]]; then
            return 1
        fi
        
        local start_time="${time_filter%|*}"
        local end_time="${time_filter#*|}"
        
        echo_info "时间范围: $start_time 到 $end_time"
        echo_separator
        
        awk -v start="$start_time" -v end="$end_time" '
        {
            if (match($0, /^\[([0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2})\]/, arr)) {
                timestamp = arr[1]
                if (timestamp >= start && timestamp <= end) {
                    print $0
                }
            }
        }' "$system_log" | tail -n "$lines" | while IFS= read -r line; do
            format_log_line "$line" "SYSTEM"
        done
    else
        echo_info "显示最近 $lines 行日志"
        echo_separator
        
        tail -n "$lines" "$system_log" | while IFS= read -r line; do
            format_log_line "$line" "SYSTEM"
        done
    fi
}

# 查看操作日志
view_operation_logs() {
    local time_range="$1"
    local lines="${2:-50}"
    
    local operation_log="$SCRIPT_DIR/logs/operations/operations_$(date +%Y%m%d).log"
    
    echo_title "查看操作日志"
    
    if [[ ! -f "$operation_log" ]]; then
        echo_warning "操作日志文件不存在: $operation_log"
        return 1
    fi
    
    echo_info "日志文件: $operation_log"
    
    if [[ -n "$time_range" ]]; then
        local time_filter=$(parse_time_range "$time_range")
        if [[ $? -ne 0 ]]; then
            return 1
        fi
        
        local start_time="${time_filter%|*}"
        local end_time="${time_filter#*|}"
        
        echo_info "时间范围: $start_time 到 $end_time"
        echo_separator
        
        awk -v start="$start_time" -v end="$end_time" '
        {
            if (match($0, /^\[([0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2})\]/, arr)) {
                timestamp = arr[1]
                if (timestamp >= start && timestamp <= end) {
                    print $0
                }
            }
        }' "$operation_log" | tail -n "$lines" | while IFS= read -r line; do
            echo_color "$CYAN" "$line"
        done
    else
        echo_info "显示最近 $lines 行日志"
        echo_separator
        
        tail -n "$lines" "$operation_log" | while IFS= read -r line; do
            echo_color "$CYAN" "$line"
        done
    fi
}

# 查看所有日志
view_all_logs() {
    local time_range="$1"
    local lines="${2:-50}"
    
    echo_title "查看所有服务日志"
    
    # 显示系统日志
    echo_subtitle "系统日志"
    view_system_logs "$time_range" "$lines"
    echo ""
    
    # 显示各服务日志
    for service in "${ALL_SERVICES[@]}"; do
        echo_subtitle "$service 服务日志"
        view_service_logs "$service" "$time_range" "$lines"
        echo ""
    done
    
    # 显示操作日志
    echo_subtitle "操作日志"
    view_operation_logs "$time_range" "$lines"
}

# 实时监控日志
monitor_logs() {
    local service_name="$1"
    
    if [[ -n "$service_name" ]]; then
        # 监控特定服务
        local service_upper=$(to_upper "$service_name")
        local log_file_var="${service_upper}_LOG_FILE"
        local log_file="${!log_file_var}"
        
        if [[ -z "$log_file" ]]; then
            log_file="$SCRIPT_DIR/logs/services/${service_name}_$(date +%Y%m%d).log"
        fi
        
        if [[ ! -f "$log_file" ]]; then
            echo_error "日志文件不存在: $log_file"
            return 1
        fi
        
        echo_title "实时监控 $service_name 服务日志"
        echo_info "按 Ctrl+C 停止监控"
        echo_separator
        
        tail -f "$log_file" | while IFS= read -r line; do
            format_log_line "$line" "$service_name"
        done
    else
        # 监控所有日志
        echo_title "实时监控所有服务日志"
        echo_info "按 Ctrl+C 停止监控"
        echo_separator
        
        # 使用multitail或者简单的tail -f
        if command_exists multitail; then
            local tail_files=()
            for service in "${ALL_SERVICES[@]}"; do
                local log_file_var="${service^^}_LOG_FILE"
                local log_file="${!log_file_var}"
                if [[ -f "$log_file" ]]; then
                    tail_files+=("$log_file")
                fi
            done
            
            if [[ ${#tail_files[@]} -gt 0 ]]; then
                multitail "${tail_files[@]}"
            else
                echo_warning "未找到可监控的日志文件"
            fi
        else
            echo_warning "建议安装 multitail 以获得更好的多文件监控体验"
            echo_info "使用简单模式监控系统日志..."
            
            local system_log="$SCRIPT_DIR/logs/system/system_$(date +%Y%m%d).log"
            if [[ -f "$system_log" ]]; then
                tail -f "$system_log" | while IFS= read -r line; do
                    format_log_line "$line" "SYSTEM"
                done
            else
                echo_error "系统日志文件不存在"
            fi
        fi
    fi
}

# 日志统计
log_statistics() {
    local time_range="$1"
    
    echo_title "日志统计信息"
    
    # 统计各服务日志文件大小
    echo_subtitle "日志文件大小"
    echo_table_header "服务" "日志文件" "大小" "行数"
    
    for service in "${ALL_SERVICES[@]}"; do
        local log_file_var="${service^^}_LOG_FILE"
        local log_file="${!log_file_var}"
        
        if [[ -z "$log_file" ]]; then
            log_file="$SCRIPT_DIR/logs/services/${service}_$(date +%Y%m%d).log"
        fi
        
        if [[ -f "$log_file" ]]; then
            local file_size=$(stat -c%s "$log_file" 2>/dev/null || echo "0")
            local formatted_size=$(format_size "$file_size")
            local line_count=$(wc -l < "$log_file" 2>/dev/null || echo "0")
            
            echo_table_row "$service" "$(basename "$log_file")" "$formatted_size" "$line_count"
        else
            echo_table_row "$service" "不存在" "0B" "0"
        fi
    done
    
    echo ""
    
    # 统计错误日志
    echo_subtitle "错误统计 (最近24小时)"
    local error_count=0
    local warning_count=0
    
    for service in "${ALL_SERVICES[@]}"; do
        local log_file_var="${service^^}_LOG_FILE"
        local log_file="${!log_file_var}"
        
        if [[ -f "$log_file" ]]; then
            local service_errors=$(grep -c "ERROR\|CRITICAL\|FATAL" "$log_file" 2>/dev/null || echo "0")
            local service_warnings=$(grep -c "WARN\|WARNING" "$log_file" 2>/dev/null || echo "0")
            
            if [[ $service_errors -gt 0 ]] || [[ $service_warnings -gt 0 ]]; then
                show_key_value "$service 错误" "$service_errors" "$RED"
                show_key_value "$service 警告" "$service_warnings" "$YELLOW"
            fi
            
            ((error_count += service_errors))
            ((warning_count += service_warnings))
        fi
    done
    
    echo ""
    show_key_value "总错误数" "$error_count" "$RED"
    show_key_value "总警告数" "$warning_count" "$YELLOW"
}

# 高级日志管理菜单
advanced_log_menu() {
    while true; do
        clear
        echo_title "高级日志管理"

        echo_color "$YELLOW" "高级功能选项："
        echo "  1. 日志搜索           2. 错误分析           3. 趋势分析"
        echo "  4. 性能分析           5. 日志轮转           6. 日志压缩"
        echo "  7. 清理过期日志       8. 生成分析报告       9. 自动维护"
        echo "  0. 返回主菜单"
        echo ""

        read -p "请选择功能 (0-9): " choice

        case "$choice" in
            1)
                echo ""
                read -p "请输入搜索关键词: " keyword
                read -p "服务名 (留空搜索所有): " service
                read -p "时间范围 (1h/today/week): " time_range
                "$SCRIPT_DIR/core/log_analyzer.sh" search "$keyword" "$service" "$time_range"
                read -p "按回车键继续..."
                ;;
            2)
                echo ""
                read -p "服务名 (留空分析所有): " service
                "$SCRIPT_DIR/core/log_analyzer.sh" errors "$service"
                read -p "按回车键继续..."
                ;;
            3)
                echo ""
                read -p "服务名 (留空分析所有): " service
                read -p "分析天数 (默认7天): " days
                "$SCRIPT_DIR/core/log_analyzer.sh" trends "$service" "${days:-7}"
                read -p "按回车键继续..."
                ;;
            4)
                echo ""
                read -p "服务名 (留空分析所有): " service
                "$SCRIPT_DIR/core/log_analyzer.sh" performance "$service"
                read -p "按回车键继续..."
                ;;
            5)
                echo ""
                echo "可用服务: ${ALL_SERVICES[*]}"
                read -p "请输入服务名: " service
                if [[ " ${ALL_SERVICES[*]} " =~ " $service " ]]; then
                    local log_file_var="${service^^}_LOG_FILE"
                    local log_file="${!log_file_var}"
                    if [[ -n "$log_file" ]] && [[ -f "$log_file" ]]; then
                        "$SCRIPT_DIR/core/log_rotator.sh" rotate "$log_file"
                    else
                        echo_error "日志文件不存在"
                    fi
                else
                    echo_error "无效的服务名"
                fi
                read -p "按回车键继续..."
                ;;
            6)
                echo ""
                read -p "压缩几天前的日志 (默认1天): " days
                "$SCRIPT_DIR/core/log_rotator.sh" compress "$SCRIPT_DIR/logs" "${days:-1}"
                read -p "按回车键继续..."
                ;;
            7)
                echo ""
                read -p "清理几天前的日志 (默认7天): " days
                if ask_confirmation "确定要清理 ${days:-7} 天前的日志吗？"; then
                    "$SCRIPT_DIR/core/log_rotator.sh" cleanup "$SCRIPT_DIR/logs" "${days:-7}"
                fi
                read -p "按回车键继续..."
                ;;
            8)
                echo ""
                read -p "服务名 (留空生成所有服务报告): " service
                "$SCRIPT_DIR/core/log_analyzer.sh" report "$service"
                read -p "按回车键继续..."
                ;;
            9)
                echo ""
                if ask_confirmation "确定要执行自动日志维护吗？"; then
                    "$SCRIPT_DIR/core/log_rotator.sh" auto
                fi
                read -p "按回车键继续..."
                ;;
            0)
                break
                ;;
            *)
                echo_error "无效选择"
                sleep 1
                ;;
        esac
    done
}

# 主函数
main() {
    case "${1:-help}" in
        "view")
            if [[ -n "$2" ]]; then
                view_service_logs "$2" "$3" "$4"
            else
                view_all_logs "$3" "$4"
            fi
            ;;
        "system")
            view_system_logs "$2" "$3"
            ;;
        "operations")
            view_operation_logs "$2" "$3"
            ;;
        "monitor")
            monitor_logs "$2"
            ;;
        "stats")
            log_statistics "$2"
            ;;
        "advanced")
            advanced_log_menu
            ;;
        "search")
            "$SCRIPT_DIR/core/log_analyzer.sh" search "$2" "$3" "$4" "$5"
            ;;
        "analyze")
            "$SCRIPT_DIR/core/log_analyzer.sh" errors "$2" "$3"
            ;;
        "rotate")
            "$SCRIPT_DIR/core/log_rotator.sh" auto
            ;;
        *)
            echo_error "未知命令: $1"
            echo "用法: $0 {view|system|operations|monitor|stats|advanced|search|analyze|rotate} [参数...]"
            echo ""
            echo "基础命令:"
            echo "  view [service] [time] [lines]    # 查看日志"
            echo "  system [time] [lines]            # 查看系统日志"
            echo "  operations [time] [lines]        # 查看操作日志"
            echo "  monitor [service]                # 实时监控日志"
            echo "  stats                            # 显示统计信息"
            echo ""
            echo "高级命令:"
            echo "  advanced                         # 高级日志管理菜单"
            echo "  search <keyword> [service]       # 搜索日志"
            echo "  analyze [service]                # 错误分析"
            echo "  rotate                           # 自动日志维护"
            echo ""
            echo "示例:"
            echo "  $0 view mysql 1h 100            # 查看MySQL最近1小时的100行日志"
            echo "  $0 search 'ERROR' backend        # 在后端日志中搜索错误"
            echo "  $0 advanced                      # 进入高级管理菜单"
            exit 1
            ;;
    esac
}

# 如果直接执行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
