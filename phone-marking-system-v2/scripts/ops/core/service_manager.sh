#!/bin/bash

# =============================================================================
# 服务管理核心模块
# 提供服务启动、停止、重启、状态检查等核心功能
# =============================================================================

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

# 加载依赖
source "$SCRIPT_DIR/utils/common.sh"
source "$SCRIPT_DIR/utils/colors.sh"
source "$SCRIPT_DIR/config/services.conf"

# 智能准备服务环境
prepare_service_environment() {
    local service_name="$1"

    case "$service_name" in
        "backend")
            local backend_dir="$PROJECT_ROOT/dash-fastapi-admin/dash-fastapi-backend"
            local requirements_file="$PROJECT_ROOT/dash-fastapi-admin/requirements.txt"

            if [[ -d "$backend_dir" && -f "$requirements_file" ]]; then
                echo_info "检查Backend服务环境..."
                if ! smart_install_requirements "$requirements_file"; then
                    echo_warning "Backend依赖检查失败，但继续尝试启动"
                fi
            fi
            ;;
        "frontend")
            local frontend_dir="$PROJECT_ROOT/dash-fastapi-admin/dash-fastapi-frontend"
            local requirements_file="$PROJECT_ROOT/dash-fastapi-admin/requirements.txt"

            if [[ -d "$frontend_dir" && -f "$requirements_file" ]]; then
                echo_info "检查Frontend服务环境..."
                if ! smart_install_requirements "$requirements_file"; then
                    echo_warning "Frontend依赖检查失败，但继续尝试启动"
                fi
            fi
            ;;
    esac
}

# 智能生成启动命令
generate_smart_start_command() {
    local service_name="$1"

    case "$service_name" in
        "backend")
            local backend_dir="$PROJECT_ROOT/dash-fastapi-admin/dash-fastapi-backend"
            local start_method=$(detect_service_start_method "backend" "$backend_dir")

            if [[ -n "$start_method" ]]; then
                if [[ "$start_method" == *"uvicorn"* ]]; then
                    echo "cd $backend_dir && $start_method 9099"
                else
                    echo "cd $backend_dir && $start_method"
                fi
            else
                # 回退到智能修复的命令
                echo "cd $backend_dir && $(detect_python) app.py"
            fi
            ;;
        "frontend")
            local frontend_dir="$PROJECT_ROOT/dash-fastapi-admin/dash-fastapi-frontend"
            # Frontend使用Dash应用，直接使用app.py启动
            echo "cd $frontend_dir && $(detect_python) app.py"
            ;;
        "nlp")
            local nlp_dir="$PROJECT_ROOT/src/services/nlp"
            local python_cmd=$(detect_python)
            if [[ -n "$python_cmd" && -f "$nlp_dir/nlp_microservice.py" ]]; then
                echo "cd $nlp_dir && $python_cmd nlp_microservice.py"
            else
                # 回退到配置文件中的命令，但智能替换python
                local service_upper=$(to_upper "$service_name")
                local start_cmd_var="${service_upper}_START_CMD"
                local original_cmd="${!start_cmd_var}"
                # 智能替换python命令
                echo "${original_cmd//python /$python_cmd }"
            fi
            ;;
        "batch")
            local batch_dir="$PROJECT_ROOT/src/services/batch"
            local python_cmd=$(detect_python)
            if [[ -n "$python_cmd" && -f "$batch_dir/batch_processing_service.py" ]]; then
                echo "cd $batch_dir && $python_cmd batch_processing_service.py"
            else
                # 回退到配置文件中的命令，但智能替换python
                local service_upper=$(to_upper "$service_name")
                local start_cmd_var="${service_upper}_START_CMD"
                local original_cmd="${!start_cmd_var}"
                # 智能替换python命令
                echo "${original_cmd//python /$python_cmd }"
            fi
            ;;
        "location"|"gateway")
            # 其他Python服务也进行智能替换
            local service_upper=$(to_upper "$service_name")
            local start_cmd_var="${service_upper}_START_CMD"
            local original_cmd="${!start_cmd_var}"
            local python_cmd=$(detect_python)

            if [[ -n "$python_cmd" && "$original_cmd" == *"python "* ]]; then
                # 智能替换python命令
                echo "${original_cmd//python /$python_cmd }"
            else
                echo "$original_cmd"
            fi
            ;;
        *)
            # 其他服务使用配置文件中的命令，但尝试智能替换python
            local service_upper=$(to_upper "$service_name")
            local start_cmd_var="${service_upper}_START_CMD"
            local original_cmd="${!start_cmd_var}"
            local python_cmd=$(detect_python)

            if [[ -n "$python_cmd" && "$original_cmd" == *"python "* ]]; then
                # 智能替换python命令
                echo "${original_cmd//python /$python_cmd }"
            else
                echo "$original_cmd"
            fi
            ;;
    esac
}

# 启动单个服务
start_service() {
    local service_name="$1"
    local force="${2:-false}"

    echo_progress "启动服务: $service_name"
    
    # 检查服务是否已经运行
    if is_service_running "$service_name" && [[ "$force" != "true" ]]; then
        echo_warning "服务 $service_name 已经在运行"
        return 0
    fi
    
    # 检查依赖服务
    if ! check_service_dependencies "$service_name"; then
        echo_error "服务 $service_name 的依赖服务未满足"
        return 1
    fi
    
    # 智能准备服务环境
    prepare_service_environment "$service_name"

    # 智能生成启动命令
    local start_cmd=$(generate_smart_start_command "$service_name")
    local service_upper=$(to_upper "$service_name")
    local log_file_var="${service_upper}_LOG_FILE"
    local log_file="${!log_file_var}"

    if [[ -z "$start_cmd" ]]; then
        echo_error "无法生成服务 $service_name 的启动命令"
        return 1
    fi

    echo_info "使用启动命令: $start_cmd"
    
    # 创建日志目录
    if [[ -n "$log_file" ]]; then
        mkdir -p "$(dirname "$log_file")"
    fi
    
    # 启动服务
    log_system "INFO" "启动服务: $service_name"

    # 区分系统服务和应用服务
    if [[ "$service_name" == "mysql" || "$service_name" == "redis" ]]; then
        # 系统服务直接启动
        if eval "$start_cmd" >/dev/null 2>&1; then
            echo_info "系统服务启动命令已执行: $service_name"
        else
            echo_warning "系统服务启动命令执行失败，可能已在运行: $service_name"
        fi

        # 系统服务启动后立即检查状态并返回
        sleep 2  # 给系统服务一点启动时间
        if is_service_running "$service_name"; then
            echo_success "系统服务 $service_name 运行正常"
            log_system "INFO" "系统服务运行正常: $service_name"
            return 0
        else
            echo_error "系统服务 $service_name 启动失败"
            log_system "ERROR" "系统服务启动失败: $service_name"
            return 1
        fi
    else
        # 应用服务后台启动
        if [[ -n "$log_file" ]]; then
            # 确保日志目录存在
            mkdir -p "$(dirname "$log_file")"

            # 使用bash -c执行完整命令，但确保子进程正确启动
            echo_info "执行启动命令: $start_cmd"
            (
                eval "$start_cmd" > "$log_file" 2>&1 &
                echo $! > "/tmp/${service_name}.pid"
            ) &
            sleep 1  # 给进程启动一点时间
            local saved_pid=$(cat "/tmp/${service_name}.pid" 2>/dev/null || echo "")
            echo_info "服务 $service_name 后台启动 (PID: $saved_pid)"
        else
            # 直接启动
            echo_info "执行启动命令: $start_cmd"
            (
                eval "$start_cmd" >/dev/null 2>&1 &
                echo $! > "/tmp/${service_name}.pid"
            ) &
            sleep 1  # 给进程启动一点时间
            local saved_pid=$(cat "/tmp/${service_name}.pid" 2>/dev/null || echo "")
            echo_info "服务 $service_name 后台启动 (PID: $saved_pid)"
        fi
    fi
    
    # 等待服务启动 - 根据服务类型调整超时时间
    local timeout="${SERVICE_START_TIMEOUT:-60}"

    # 为Python微服务提供更长的启动时间
    case "$service_name" in
        "nlp"|"batch"|"location")
            timeout=90  # Python微服务需要更多时间加载依赖
            ;;
        "backend"|"frontend")
            timeout=120  # FastAPI应用需要更多时间
            ;;
    esac

    local count=0
    echo_info "等待服务启动，超时时间: ${timeout}秒"

    while [[ $count -lt $timeout ]]; do
        if is_service_running "$service_name"; then
            local actual_pid=$(get_service_pid "$service_name")
            echo_success "服务 $service_name 启动成功 (PID: $actual_pid)"
            log_system "INFO" "服务启动成功: $service_name (PID: $actual_pid)"
            return 0
        fi

        sleep 1
        ((count++))

        # 显示进度
        if [[ $((count % 5)) -eq 0 ]]; then
            echo_progress "等待服务启动... ($count/$timeout)"
        fi
    done
    
    echo_error "服务 $service_name 启动超时"
    log_system "ERROR" "服务启动超时: $service_name"
    return 1
}

# 停止单个服务
stop_service() {
    local service_name="$1"
    local force="${2:-false}"
    
    echo_progress "停止服务: $service_name"
    
    # 检查服务是否在运行
    if ! is_service_running "$service_name"; then
        echo_warning "服务 $service_name 未在运行"
        return 0
    fi
    
    # 获取服务配置
    local service_upper=$(to_upper "$service_name")
    local stop_cmd_var="${service_upper}_STOP_CMD"
    local stop_cmd="${!stop_cmd_var}"
    
    if [[ -z "$stop_cmd" ]]; then
        echo_warning "未找到服务 $service_name 的停止命令，尝试通过PID停止"
        
        # 通过PID文件停止
        local pid_file="/tmp/${service_name}.pid"
        if [[ -f "$pid_file" ]]; then
            local pid=$(cat "$pid_file")
            if kill -0 "$pid" 2>/dev/null; then
                if [[ "$force" == "true" ]]; then
                    kill -9 "$pid"
                else
                    kill -15 "$pid"
                fi
                rm -f "$pid_file"
            fi
        fi
    else
        # 使用配置的停止命令
        eval "$stop_cmd"
    fi
    
    # 等待服务停止
    local timeout="${SERVICE_STOP_TIMEOUT:-30}"
    local count=0
    
    while [[ $count -lt $timeout ]]; do
        if ! is_service_running "$service_name"; then
            echo_success "服务 $service_name 停止成功"
            log_system "INFO" "服务停止成功: $service_name"
            return 0
        fi
        
        sleep 1
        ((count++))
    done
    
    # 强制停止
    if [[ "$force" == "true" ]]; then
        echo_warning "强制停止服务: $service_name"
        local pid_file="/tmp/${service_name}.pid"
        if [[ -f "$pid_file" ]]; then
            local pid=$(cat "$pid_file")
            if kill -0 "$pid" 2>/dev/null; then
                kill -9 "$pid"
                rm -f "$pid_file"
            fi
        fi
        
        # 通过进程名强制停止
        pkill -9 -f "$service_name"
        
        echo_success "服务 $service_name 已强制停止"
        return 0
    fi
    
    echo_error "服务 $service_name 停止超时"
    log_system "ERROR" "服务停止超时: $service_name"
    return 1
}

# 重启单个服务
restart_service() {
    local service_name="$1"
    
    echo_progress "重启服务: $service_name"
    
    # 停止服务
    if is_service_running "$service_name"; then
        stop_service "$service_name"
        sleep 2
    fi
    
    # 启动服务
    start_service "$service_name"
}

# 获取服务PID
get_service_pid() {
    local service_name="$1"

    # 首先检查PID文件
    local pid_file="/tmp/${service_name}.pid"
    if [[ -f "$pid_file" ]]; then
        local pid=$(cat "$pid_file" 2>/dev/null)
        if [[ -n "$pid" ]] && kill -0 "$pid" 2>/dev/null; then
            echo "$pid"
            return 0
        fi
    fi

    # 根据服务类型查找PID
    case "$service_name" in
        "nlp")
            pgrep -f "nlp_microservice.py" | head -1
            ;;
        "batch")
            pgrep -f "batch_processing_service.py" | head -1
            ;;
        "gateway")
            pgrep -f "api_gateway.py" | head -1
            ;;
        "backend")
            pgrep -f "uvicorn.*app:app.*9099" | head -1
            ;;
        "frontend")
            pgrep -f "app.py.*8089" | head -1
            ;;
        "location")
            pgrep -f "location.*8001" | head -1
            ;;
        *)
            # 通用查找方法
            local service_upper=$(to_upper "$service_name")
            local port_var="${service_upper}_PORT"
            local port="${!port_var}"
            if [[ -n "$port" ]]; then
                lsof -ti:$port 2>/dev/null | head -1
            fi
            ;;
    esac
}

# 智能检查服务是否运行
is_service_running() {
    local service_name="$1"

    # 首先检查健康检查
    local service_upper=$(to_upper "$service_name")
    local health_check_var="${service_upper}_HEALTH_CHECK"
    local health_check="${!health_check_var}"

    # 对于系统服务，使用系统命令检查
    if [[ "$service_name" == "mysql" || "$service_name" == "redis" ]]; then
        if [[ -n "$health_check" ]] && eval "$health_check" >/dev/null 2>&1; then
            return 0
        fi

        # 备用检查：端口监听
        local port_var="${service_upper}_PORT"
        local port="${!port_var}"
        if [[ -n "$port" ]] && lsof -i:$port >/dev/null 2>&1; then
            return 0
        fi

        return 1
    fi

    # 对于应用服务，优先检查健康检查接口
    if [[ -n "$health_check" ]]; then
        # 给服务一些时间启动
        sleep 2
        if eval "$health_check" >/dev/null 2>&1; then
            return 0
        fi
    fi
    
    # 检查PID文件
    local pid_file="/tmp/${service_name}.pid"
    if [[ -f "$pid_file" ]]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            return 0
        else
            rm -f "$pid_file"
        fi
    fi
    
    # 检查进程 - 更精确的匹配
    case "$service_name" in
        "frontend")
            if pgrep -f "dash-fastapi-frontend.*app.py" >/dev/null 2>&1; then
                return 0
            fi
            ;;
        "backend")
            if pgrep -f "dash-fastapi-backend.*uvicorn" >/dev/null 2>&1; then
                return 0
            fi
            ;;
        "nlp")
            if pgrep -f "nlp_microservice.py" >/dev/null 2>&1; then
                return 0
            fi
            ;;
        "batch")
            if pgrep -f "batch_processing_service.py" >/dev/null 2>&1; then
                return 0
            fi
            ;;
        "gateway")
            if pgrep -f "api_gateway.py" >/dev/null 2>&1; then
                return 0
            fi
            ;;
        "location")
            if pgrep -f "location_service.py" >/dev/null 2>&1; then
                return 0
            fi
            ;;
        *)
            # 对于其他服务，使用通用检查
            if pgrep -f "$service_name" >/dev/null 2>&1; then
                return 0
            fi
            ;;
    esac

    return 1
}

# 获取服务状态
get_service_status() {
    local service_name="$1"
    
    if is_service_running "$service_name"; then
        echo "running"
    else
        echo "stopped"
    fi
}

# 检查服务依赖
check_service_dependencies() {
    local service_name="$1"
    local dependencies=""

    # 兼容性处理：支持bash 3.x和4.x
    if [[ "${BASH_VERSION%%.*}" -ge 4 ]] 2>/dev/null && [[ -n "${SERVICE_DEPENDENCIES[$service_name]:-}" ]]; then
        dependencies="${SERVICE_DEPENDENCIES[$service_name]}"
    elif command -v get_service_dependencies >/dev/null 2>&1; then
        dependencies=$(get_service_dependencies "$service_name")
    fi
    
    if [[ -z "$dependencies" ]]; then
        return 0
    fi
    
    for dep in $dependencies; do
        if ! is_service_running "$dep"; then
            echo_error "依赖服务 $dep 未运行"
            return 1
        fi
    done
    
    return 0
}

# 启动所有服务
start_all() {
    echo_title "启动所有服务"
    
    local failed_services=()
    local total_services=${#ALL_SERVICES[@]}
    local current=0
    
    # 启动必需服务
    echo_subtitle "启动必需服务"
    for service in "${REQUIRED_SERVICES[@]}"; do
        ((current++))
        show_progress $current $total_services 50 "启动 $service"
        echo ""  # 换行清除进度条

        if start_service "$service"; then
            echo_success "✓ $service"
        else
            echo_error "✗ $service"
            failed_services+=("$service")
        fi
        
        # 服务间启动间隔
        sleep 2
    done
    
    # 启动可选服务
    echo_subtitle "启动可选服务"
    for service in "${OPTIONAL_SERVICES[@]}"; do
        ((current++))
        show_progress $current $total_services 50 "启动 $service"
        echo ""  # 换行清除进度条

        if start_service "$service"; then
            echo_success "✓ $service"
        else
            echo_warning "⚠ $service (可选服务)"
        fi
        
        sleep 1
    done
    
    echo ""
    
    # 显示结果
    if [[ ${#failed_services[@]} -eq 0 ]]; then
        echo_done "🎉 所有服务启动完成！"
        show_startup_success_info
    else
        echo_error "❌ 以下必需服务启动失败："
        for service in "${failed_services[@]}"; do
            echo_error "  - $service"
        done
        echo ""
        echo_warning "请检查日志并修复问题后重试"
        return 1
    fi
}

# 显示启动成功信息
show_startup_success_info() {
    echo ""
    echo_color "$GREEN" "=================================================================="
    echo_color "$CYAN" "    🎉 电话标记系统启动成功！"
    echo_color "$GREEN" "=================================================================="
    echo ""

    # 显示服务访问地址
    echo_subtitle "📡 服务访问地址"
    echo_color "$YELLOW" "核心服务："
    echo "  🌐 前端管理界面: http://127.0.0.1:8088"
    echo "  🔧 后端API接口:  http://127.0.0.1:9099"
    echo "  📚 API文档:      http://127.0.0.1:9099/docs"
    echo ""

    echo_color "$YELLOW" "微服务："
    echo "  📍 位置识别服务: http://127.0.0.1:8001"
    echo "  🧠 NLP分析服务:  http://127.0.0.1:8002"
    echo "  ⚡ 批量处理服务: http://127.0.0.1:8003"
    echo "  🚪 API网关:      http://127.0.0.1:8000"
    echo ""

    echo_color "$YELLOW" "基础服务："
    echo "  🗄️  MySQL数据库:  127.0.0.1:3306"
    echo "  🔄 Redis缓存:    127.0.0.1:6379"
    echo ""

    # 显示服务状态摘要
    show_service_summary

    # 显示告警信息（如果有）
    show_service_warnings

    echo ""
    echo_color "$GREEN" "=================================================================="
    echo_color "$CYAN" "    系统已就绪，可以开始使用！"
    echo_color "$GREEN" "=================================================================="
    echo ""

    echo_subtitle "🎛️ 运维管理"
    echo "  ./main.sh panel     # 启动运维面板"
    echo "  ./main.sh status    # 查看服务状态"
    echo "  ./main.sh health    # 健康检查"
    echo "  ./main.sh logs      # 查看日志"
    echo ""
}

# 显示服务告警信息
show_service_warnings() {
    local warnings=()

    # 检查NLP服务告警
    if is_service_running "nlp"; then
        local nlp_log_file="$SCRIPT_DIR/logs/services/nlp_$(date +%Y%m%d).log"
        if [[ -f "$nlp_log_file" ]] && grep -q "SmartTextAnalyzer不可用" "$nlp_log_file" 2>/dev/null; then
            warnings+=("NLP服务: 使用模拟分析模式（SmartTextAnalyzer组件不可用）")
        fi
    fi

    # 检查健康检查失败的服务
    for service in "${ALL_SERVICES[@]}"; do
        if is_service_running "$service"; then
            local service_upper=$(to_upper "$service")
            local health_check_var="${service_upper}_HEALTH_CHECK"
            local health_check="${!health_check_var}"
            if [[ -n "$health_check" ]] && ! eval "$health_check" >/dev/null 2>&1; then
                warnings+=("${service}服务: 健康检查失败，但进程正在运行")
            fi
        fi
    done

    # 显示告警信息
    if [[ ${#warnings[@]} -gt 0 ]]; then
        echo ""
        echo_subtitle "⚠️ 告警信息"
        for warning in "${warnings[@]}"; do
            echo_warning "  ⚠️  $warning"
        done
        echo ""
        echo_info "💡 这些告警不影响系统基本功能，可以正常使用"
    fi
}

# 停止所有服务
stop_all() {
    echo_title "停止所有服务"
    
    # 反向停止服务
    local services_reverse=()
    for ((i=${#ALL_SERVICES[@]}-1; i>=0; i--)); do
        services_reverse+=("${ALL_SERVICES[i]}")
    done
    
    for service in "${services_reverse[@]}"; do
        if is_service_running "$service"; then
            stop_service "$service"
        else
            echo_info "服务 $service 未在运行"
        fi
        sleep 1
    done
    
    echo_done "所有服务已停止"
}

# 重启所有服务
restart_all() {
    echo_title "重启所有服务"
    
    stop_all
    sleep 3
    start_all
}

# 显示所有服务状态
status_all() {
    echo_title "服务状态概览"
    
    echo_table_header "服务名称" "状态" "端口" "PID" "健康检查"
    
    for service in "${ALL_SERVICES[@]}"; do
        local status=$(get_service_status "$service")
        local service_upper=$(to_upper "$service")
        local port_var="${service_upper}_PORT"
        local port="${!port_var:-N/A}"
        local pid="N/A"
        local health="N/A"
        
        # 获取PID
        local pid_file="/tmp/${service}.pid"
        if [[ -f "$pid_file" ]]; then
            pid=$(cat "$pid_file")
        fi
        
        # 健康检查
        local health_check_var="${service_upper}_HEALTH_CHECK"
        local health_check="${!health_check_var}"
        if [[ -n "$health_check" ]]; then
            if eval "$health_check" >/dev/null 2>&1; then
                health="✓"
            else
                health="✗"
            fi
        fi
        
        # 状态颜色
        if [[ "$status" == "running" ]]; then
            status="🟢 运行中"
        else
            status="🔴 已停止"
        fi
        
        echo_table_row "$service" "$status" "$port" "$pid" "$health"
    done
    
    echo ""
    show_service_summary
}

# 显示服务摘要
show_service_summary() {
    local running_count=0
    local stopped_count=0
    
    for service in "${ALL_SERVICES[@]}"; do
        if is_service_running "$service"; then
            ((running_count++))
        else
            ((stopped_count++))
        fi
    done
    
    echo_subtitle "服务摘要"
    show_key_value "总服务数" "${#ALL_SERVICES[@]}"
    show_key_value "运行中" "$running_count" "$GREEN"
    show_key_value "已停止" "$stopped_count" "$RED"
    
    if [[ $running_count -eq ${#ALL_SERVICES[@]} ]]; then
        echo_success "🎉 所有服务运行正常！"
    elif [[ $running_count -eq ${#REQUIRED_SERVICES[@]} ]]; then
        echo_info "✅ 必需服务运行正常"
    else
        echo_warning "⚠️  部分服务未运行"
    fi
}

# 主函数
main() {
    case "${1:-help}" in
        "start_all")
            start_all
            ;;
        "stop_all")
            stop_all
            ;;
        "restart_all")
            restart_all
            ;;
        "status_all")
            status_all
            ;;
        "start")
            start_service "$2" "$3"
            ;;
        "stop")
            stop_service "$2" "$3"
            ;;
        "restart")
            restart_service "$2"
            ;;
        "status")
            echo "服务 $2 状态: $(get_service_status "$2")"
            ;;
        *)
            echo_error "未知命令: $1"
            echo "用法: $0 {start_all|stop_all|restart_all|status_all|start|stop|restart|status} [service_name] [force]"
            exit 1
            ;;
    esac
}

# 如果直接执行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
