#!/bin/bash

# =============================================================================
# 高级日志分析器
# 提供日志搜索、统计分析、异常检测、趋势分析等高级功能
# =============================================================================

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

# 加载依赖
source "$SCRIPT_DIR/utils/common.sh"
source "$SCRIPT_DIR/utils/colors.sh"
source "$SCRIPT_DIR/config/services.conf"

# 日志搜索功能
search_logs() {
    local keyword="$1"
    local service="$2"
    local time_range="$3"
    local case_sensitive="${4:-false}"
    
    echo_title "日志搜索结果"
    echo_info "搜索关键词: $keyword"
    
    local grep_options="-n"
    if [[ "$case_sensitive" == "false" ]]; then
        grep_options+="i"
    fi
    
    local search_files=()
    
    if [[ -n "$service" ]]; then
        # 搜索特定服务日志
        local log_file_var="${service^^}_LOG_FILE"
        local log_file="${!log_file_var}"
        
        if [[ -z "$log_file" ]]; then
            log_file="$SCRIPT_DIR/logs/services/${service}_$(date +%Y%m%d).log"
        fi
        
        if [[ -f "$log_file" ]]; then
            search_files+=("$log_file")
        fi
    else
        # 搜索所有日志文件
        search_files+=($(find "$SCRIPT_DIR/logs" -name "*.log" -type f 2>/dev/null))
    fi
    
    if [[ ${#search_files[@]} -eq 0 ]]; then
        echo_warning "未找到可搜索的日志文件"
        return 1
    fi
    
    local total_matches=0
    
    for log_file in "${search_files[@]}"; do
        local file_matches=$(grep $grep_options "$keyword" "$log_file" 2>/dev/null | wc -l)
        
        if [[ $file_matches -gt 0 ]]; then
            echo_subtitle "文件: $(basename "$log_file") (匹配 $file_matches 条)"
            
            # 显示匹配的行，带上下文
            grep $grep_options -C 2 "$keyword" "$log_file" 2>/dev/null | while IFS= read -r line; do
                if [[ "$line" =~ ^[0-9]+-.*$ ]]; then
                    # 匹配行，高亮显示
                    local line_num="${line%%:*}"
                    local content="${line#*:}"
                    echo_color "$YELLOW" "  $line_num: $content"
                elif [[ "$line" =~ ^[0-9]+.*$ ]]; then
                    # 上下文行
                    echo_color "$GRAY" "  $line"
                fi
            done
            
            echo ""
            ((total_matches += file_matches))
        fi
    done
    
    echo_info "总共找到 $total_matches 条匹配记录"
    
    # 记录搜索操作
    log_operation "日志搜索" "关键词: $keyword, 服务: ${service:-all}, 匹配: $total_matches"
}

# 错误日志分析
analyze_errors() {
    local service="$1"
    local time_range="${2:-today}"
    
    echo_title "错误日志分析"
    
    local log_files=()
    
    if [[ -n "$service" ]]; then
        local log_file_var="${service^^}_LOG_FILE"
        local log_file="${!log_file_var}"
        if [[ -f "$log_file" ]]; then
            log_files+=("$log_file")
        fi
    else
        log_files+=($(find "$SCRIPT_DIR/logs" -name "*.log" -type f 2>/dev/null))
    fi
    
    if [[ ${#log_files[@]} -eq 0 ]]; then
        echo_warning "未找到日志文件"
        return 1
    fi
    
    # 错误级别统计
    echo_subtitle "错误级别统计"
    local error_patterns=("ERROR" "CRITICAL" "FATAL" "EXCEPTION" "FAILED")
    local warning_patterns=("WARN" "WARNING")
    
    declare -A error_counts
    declare -A warning_counts
    declare -A error_details
    
    for log_file in "${log_files[@]}"; do
        local service_name=$(basename "$log_file" | cut -d'_' -f1)
        
        # 统计错误
        for pattern in "${error_patterns[@]}"; do
            local count=$(grep -ci "$pattern" "$log_file" 2>/dev/null || echo "0")
            error_counts["$service_name:$pattern"]=$count
            
            # 获取最新的错误详情
            if [[ $count -gt 0 ]]; then
                local latest_error=$(grep -i "$pattern" "$log_file" 2>/dev/null | tail -1)
                error_details["$service_name:$pattern"]="$latest_error"
            fi
        done
        
        # 统计警告
        for pattern in "${warning_patterns[@]}"; do
            local count=$(grep -ci "$pattern" "$log_file" 2>/dev/null || echo "0")
            warning_counts["$service_name:$pattern"]=$count
        done
    done
    
    # 显示错误统计
    echo_table_header "服务" "错误类型" "数量" "最新错误"
    
    for key in "${!error_counts[@]}"; do
        local service_name="${key%:*}"
        local error_type="${key#*:}"
        local count="${error_counts[$key]}"
        
        if [[ $count -gt 0 ]]; then
            local latest="${error_details[$key]}"
            local short_error="${latest:0:50}..."
            
            if [[ $count -gt 10 ]]; then
                echo_table_row "$service_name" "$error_type" "🔴 $count" "$short_error"
            elif [[ $count -gt 5 ]]; then
                echo_table_row "$service_name" "$error_type" "🟠 $count" "$short_error"
            else
                echo_table_row "$service_name" "$error_type" "🟡 $count" "$short_error"
            fi
        fi
    done
    
    echo ""
    
    # 显示警告统计
    echo_subtitle "警告统计"
    echo_table_header "服务" "警告类型" "数量"
    
    for key in "${!warning_counts[@]}"; do
        local service_name="${key%:*}"
        local warning_type="${key#*:}"
        local count="${warning_counts[$key]}"
        
        if [[ $count -gt 0 ]]; then
            echo_table_row "$service_name" "$warning_type" "$count"
        fi
    done
}

# 日志趋势分析
analyze_trends() {
    local service="$1"
    local days="${2:-7}"
    
    echo_title "日志趋势分析 (最近 $days 天)"
    
    local log_files=()
    
    if [[ -n "$service" ]]; then
        # 查找指定服务的历史日志文件
        local log_pattern="$SCRIPT_DIR/logs/services/${service}_*.log"
        log_files+=($(find "$SCRIPT_DIR/logs/services" -name "${service}_*.log" -type f 2>/dev/null | sort))
    else
        # 查找所有服务的日志文件
        log_files+=($(find "$SCRIPT_DIR/logs" -name "*.log" -type f 2>/dev/null))
    fi
    
    if [[ ${#log_files[@]} -eq 0 ]]; then
        echo_warning "未找到日志文件"
        return 1
    fi
    
    # 按日期统计日志条目
    echo_subtitle "每日日志条目统计"
    
    declare -A daily_counts
    declare -A daily_errors
    
    for ((i=days-1; i>=0; i--)); do
        local date=$(date -d "$i days ago" '+%Y-%m-%d')
        daily_counts["$date"]=0
        daily_errors["$date"]=0
        
        for log_file in "${log_files[@]}"; do
            # 统计该日期的日志条目
            local count=$(grep "^\\[$date" "$log_file" 2>/dev/null | wc -l)
            daily_counts["$date"]=$((${daily_counts["$date"]} + count))
            
            # 统计该日期的错误条目
            local error_count=$(grep "^\\[$date.*\\(ERROR\\|CRITICAL\\|FATAL\\)" "$log_file" 2>/dev/null | wc -l)
            daily_errors["$date"]=$((${daily_errors["$date"]} + error_count))
        done
    done
    
    # 显示趋势图（简单的ASCII图表）
    echo_table_header "日期" "总日志数" "错误数" "趋势"
    
    local max_count=0
    for date in "${!daily_counts[@]}"; do
        local count=${daily_counts["$date"]}
        if [[ $count -gt $max_count ]]; then
            max_count=$count
        fi
    done
    
    for ((i=days-1; i>=0; i--)); do
        local date=$(date -d "$i days ago" '+%Y-%m-%d')
        local count=${daily_counts["$date"]}
        local errors=${daily_errors["$date"]}
        
        # 生成简单的条形图
        local bar_length=20
        local filled=0
        if [[ $max_count -gt 0 ]]; then
            filled=$((count * bar_length / max_count))
        fi
        
        local bar=""
        for ((j=1; j<=filled; j++)); do
            bar+="█"
        done
        for ((j=filled+1; j<=bar_length; j++)); do
            bar+="░"
        done
        
        # 根据错误数量选择颜色
        if [[ $errors -gt 10 ]]; then
            echo_table_row "$date" "$count" "🔴 $errors" "$bar"
        elif [[ $errors -gt 0 ]]; then
            echo_table_row "$date" "$count" "🟡 $errors" "$bar"
        else
            echo_table_row "$date" "$count" "🟢 $errors" "$bar"
        fi
    done
}

# 性能日志分析
analyze_performance() {
    local service="$1"
    local metric="${2:-response_time}"
    
    echo_title "性能日志分析"
    
    local log_files=()
    
    if [[ -n "$service" ]]; then
        local log_file_var="${service^^}_LOG_FILE"
        local log_file="${!log_file_var}"
        if [[ -f "$log_file" ]]; then
            log_files+=("$log_file")
        fi
    else
        log_files+=($(find "$SCRIPT_DIR/logs" -name "*.log" -type f 2>/dev/null))
    fi
    
    if [[ ${#log_files[@]} -eq 0 ]]; then
        echo_warning "未找到日志文件"
        return 1
    fi
    
    echo_subtitle "性能指标分析: $metric"
    
    # 提取性能数据（这里需要根据实际日志格式调整）
    local performance_data=()
    
    for log_file in "${log_files[@]}"; do
        # 查找包含响应时间的日志行
        while IFS= read -r line; do
            if [[ "$line" =~ ([0-9]+)ms ]]; then
                local response_time="${BASH_REMATCH[1]}"
                performance_data+=("$response_time")
            fi
        done < <(grep -i "response\|time\|duration" "$log_file" 2>/dev/null)
    done
    
    if [[ ${#performance_data[@]} -eq 0 ]]; then
        echo_warning "未找到性能数据"
        return 1
    fi
    
    # 计算统计信息
    local total=0
    local min=${performance_data[0]}
    local max=${performance_data[0]}
    
    for value in "${performance_data[@]}"; do
        total=$((total + value))
        if [[ $value -lt $min ]]; then
            min=$value
        fi
        if [[ $value -gt $max ]]; then
            max=$value
        fi
    done
    
    local count=${#performance_data[@]}
    local avg=$((total / count))
    
    # 显示统计结果
    show_key_value "样本数量" "$count"
    show_key_value "平均值" "${avg}ms"
    show_key_value "最小值" "${min}ms" "$GREEN"
    show_key_value "最大值" "${max}ms" "$RED"
    
    # 性能等级评估
    if [[ $avg -lt 100 ]]; then
        echo_success "🟢 性能优秀 (平均响应时间 < 100ms)"
    elif [[ $avg -lt 500 ]]; then
        echo_info "🟡 性能良好 (平均响应时间 < 500ms)"
    elif [[ $avg -lt 1000 ]]; then
        echo_warning "🟠 性能一般 (平均响应时间 < 1s)"
    else
        echo_error "🔴 性能较差 (平均响应时间 > 1s)"
    fi
}

# 生成日志报告
generate_log_report() {
    local service="$1"
    local output_file="$SCRIPT_DIR/logs/reports/log_report_$(date +%Y%m%d_%H%M%S).html"
    
    echo_title "生成日志分析报告"
    
    # 创建报告目录
    mkdir -p "$(dirname "$output_file")"
    
    # 生成HTML报告
    cat > "$output_file" << EOF
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日志分析报告 - $(date '+%Y-%m-%d %H:%M:%S')</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f0f0f0; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .error { color: #d32f2f; }
        .warning { color: #f57c00; }
        .success { color: #388e3c; }
        .info { color: #1976d2; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f5f5f5; }
        .chart { height: 200px; background: #f9f9f9; margin: 10px 0; padding: 20px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 日志分析报告</h1>
        <p><strong>生成时间:</strong> $(date '+%Y-%m-%d %H:%M:%S')</p>
        <p><strong>分析服务:</strong> ${service:-所有服务}</p>
    </div>
EOF
    
    # 添加错误分析部分
    echo "    <div class=\"section\">" >> "$output_file"
    echo "        <h2>🔴 错误分析</h2>" >> "$output_file"
    
    # 这里可以添加更多的分析内容
    # 由于篇幅限制，这里只是一个框架
    
    echo "    </div>" >> "$output_file"
    echo "</body></html>" >> "$output_file"
    
    echo_success "报告已生成: $output_file"
    
    # 如果是macOS，尝试打开报告
    if command_exists open; then
        if ask_confirmation "是否打开报告文件？"; then
            open "$output_file"
        fi
    fi
}

# 主函数
main() {
    case "${1:-help}" in
        "search")
            search_logs "$2" "$3" "$4" "$5"
            ;;
        "errors")
            analyze_errors "$2" "$3"
            ;;
        "trends")
            analyze_trends "$2" "$3"
            ;;
        "performance")
            analyze_performance "$2" "$3"
            ;;
        "report")
            generate_log_report "$2"
            ;;
        *)
            echo_error "未知命令: $1"
            echo "用法: $0 {search|errors|trends|performance|report} [参数...]"
            echo ""
            echo "命令说明:"
            echo "  search <关键词> [服务名] [时间范围] [大小写敏感]  # 搜索日志"
            echo "  errors [服务名] [时间范围]                      # 错误分析"
            echo "  trends [服务名] [天数]                          # 趋势分析"
            echo "  performance [服务名] [指标]                     # 性能分析"
            echo "  report [服务名]                                 # 生成报告"
            exit 1
            ;;
    esac
}

# 如果直接执行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
