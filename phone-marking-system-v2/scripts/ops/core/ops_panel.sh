#!/bin/bash

# =============================================================================
# 运维管理面板
# 提供类似宝塔控制面板的交互式运维管理界面
# =============================================================================

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

# 加载依赖
source "$SCRIPT_DIR/utils/common.sh"
source "$SCRIPT_DIR/utils/colors.sh"
source "$SCRIPT_DIR/config/services.conf"
source "$SCRIPT_DIR/core/service_manager.sh"
source "$SCRIPT_DIR/core/health_checker.sh"
source "$SCRIPT_DIR/core/auto_repair.sh"
source "$SCRIPT_DIR/core/log_manager.sh"

# 全局变量
PANEL_RUNNING=true
REFRESH_INTERVAL=5

# 显示主菜单
show_main_menu() {
    clear
    echo_color "$BLUE" "=================================================================="
    echo_color "$CYAN" "    电话标记系统运维管理面板 v1.0.0"
    echo_color "$GREEN" "    类似宝塔控制面板的智能化运维管理系统"
    echo_color "$BLUE" "=================================================================="
    echo ""
    
    # 显示系统状态概览
    show_system_overview
    echo ""
    
    # 显示菜单选项
    echo_color "$YELLOW" "主菜单："
    echo_color "$GREEN" "  1. 服务管理"
    echo_color "$GREEN" "  2. 系统监控"
    echo_color "$GREEN" "  3. 日志管理"
    echo_color "$GREEN" "  4. 健康检查"
    echo_color "$GREEN" "  5. 自动修复"
    echo_color "$GREEN" "  6. 系统信息"
    echo_color "$GREEN" "  7. 配置管理"
    echo_color "$GREEN" "  8. 备份恢复"
    echo_color "$GREEN" "  9. 性能监控"
    echo_color "$GREEN" "  0. 退出面板"
    echo ""
    echo_color "$CYAN" "请选择功能 (0-9): "
}

# 显示系统状态概览
show_system_overview() {
    echo_subtitle "系统状态概览"
    
    # 服务状态统计
    local running_count=0
    local stopped_count=0
    
    for service in "${ALL_SERVICES[@]}"; do
        if is_service_running "$service"; then
            ((running_count++))
        else
            ((stopped_count++))
        fi
    done
    
    # 系统资源
    local cpu_usage=$(get_cpu_usage)
    local memory_usage=$(get_memory_usage)
    local disk_usage=$(df / | awk 'NR==2{print $5}' | sed 's/%//')
    
    # 显示状态
    printf "%-20s: " "服务状态"
    if [[ $running_count -eq ${#ALL_SERVICES[@]} ]]; then
        echo_color "$GREEN" "🟢 全部正常 ($running_count/${#ALL_SERVICES[@]})"
    elif [[ $running_count -ge ${#REQUIRED_SERVICES[@]} ]]; then
        echo_color "$YELLOW" "🟡 基本正常 ($running_count/${#ALL_SERVICES[@]})"
    else
        echo_color "$RED" "🔴 异常 ($running_count/${#ALL_SERVICES[@]})"
    fi
    
    printf "%-20s: " "CPU使用率"
    if [[ ${cpu_usage%.*} -lt 70 ]]; then
        echo_color "$GREEN" "${cpu_usage}%"
    elif [[ ${cpu_usage%.*} -lt 90 ]]; then
        echo_color "$YELLOW" "${cpu_usage}%"
    else
        echo_color "$RED" "${cpu_usage}%"
    fi
    
    printf "%-20s: " "内存使用率"
    if [[ ${memory_usage%.*} -lt 70 ]]; then
        echo_color "$GREEN" "${memory_usage}%"
    elif [[ ${memory_usage%.*} -lt 90 ]]; then
        echo_color "$YELLOW" "${memory_usage}%"
    else
        echo_color "$RED" "${memory_usage}%"
    fi
    
    printf "%-20s: " "磁盘使用率"
    if [[ $disk_usage -lt 70 ]]; then
        echo_color "$GREEN" "${disk_usage}%"
    elif [[ $disk_usage -lt 90 ]]; then
        echo_color "$YELLOW" "${disk_usage}%"
    else
        echo_color "$RED" "${disk_usage}%"
    fi
}

# 服务管理菜单
service_management_menu() {
    while true; do
        clear
        echo_title "服务管理"
        
        # 显示服务状态
        echo_table_header "服务名称" "状态" "端口" "操作"
        
        for service in "${ALL_SERVICES[@]}"; do
            local status=$(get_service_status "$service")
            local service_upper=$(to_upper "$service")
            local port_var="${service_upper}_PORT"
            local port="${!port_var:-N/A}"
            
            if [[ "$status" == "running" ]]; then
                status="🟢 运行中"
            else
                status="🔴 已停止"
            fi
            
            echo_table_row "$service" "$status" "$port" "启动|停止|重启"
        done
        
        echo ""
        echo_color "$YELLOW" "服务管理选项："
        echo "  1. 启动所有服务    2. 停止所有服务    3. 重启所有服务"
        echo "  4. 启动单个服务    5. 停止单个服务    6. 重启单个服务"
        echo "  7. 查看服务状态    0. 返回主菜单"
        echo ""
        
        read -p "请选择操作 (0-7): " choice
        
        case "$choice" in
            1)
                echo_progress "启动所有服务..."
                start_all
                read -p "按回车键继续..."
                ;;
            2)
                if ask_confirmation "确定要停止所有服务吗？"; then
                    stop_all
                fi
                read -p "按回车键继续..."
                ;;
            3)
                if ask_confirmation "确定要重启所有服务吗？"; then
                    restart_all
                fi
                read -p "按回车键继续..."
                ;;
            4)
                echo "可用服务: ${ALL_SERVICES[*]}"
                read -p "请输入要启动的服务名: " service_name
                if [[ " ${ALL_SERVICES[*]} " =~ " $service_name " ]]; then
                    start_service "$service_name"
                else
                    echo_error "无效的服务名"
                fi
                read -p "按回车键继续..."
                ;;
            5)
                echo "可用服务: ${ALL_SERVICES[*]}"
                read -p "请输入要停止的服务名: " service_name
                if [[ " ${ALL_SERVICES[*]} " =~ " $service_name " ]]; then
                    stop_service "$service_name"
                else
                    echo_error "无效的服务名"
                fi
                read -p "按回车键继续..."
                ;;
            6)
                echo "可用服务: ${ALL_SERVICES[*]}"
                read -p "请输入要重启的服务名: " service_name
                if [[ " ${ALL_SERVICES[*]} " =~ " $service_name " ]]; then
                    restart_service "$service_name"
                else
                    echo_error "无效的服务名"
                fi
                read -p "按回车键继续..."
                ;;
            7)
                status_all
                read -p "按回车键继续..."
                ;;
            0)
                break
                ;;
            *)
                echo_error "无效选择"
                sleep 1
                ;;
        esac
    done
}

# 系统监控菜单
system_monitoring_menu() {
    while true; do
        clear
        echo_title "系统监控"
        
        # 显示实时系统信息
        get_system_info
        
        echo_color "$YELLOW" "监控选项："
        echo "  1. 实时监控面板    2. 资源使用历史    3. 网络连接状态"
        echo "  4. 进程监控        5. 磁盘IO监控      6. 性能报告"
        echo "  7. 告警设置        0. 返回主菜单"
        echo ""
        
        read -p "请选择操作 (0-7): " choice
        
        case "$choice" in
            1)
                echo_info "启动实时监控面板 (按 Ctrl+C 退出)..."
                while true; do
                    clear
                    echo_title "实时系统监控"
                    show_system_overview
                    echo ""
                    get_system_info
                    sleep "$REFRESH_INTERVAL"
                done
                ;;
            2)
                echo_info "显示资源使用历史..."
                # 这里可以集成更复杂的历史数据展示
                echo "功能开发中..."
                read -p "按回车键继续..."
                ;;
            3)
                echo_title "网络连接状态"
                netstat -tuln | head -20
                echo ""
                echo "活跃连接数: $(netstat -an | grep ESTABLISHED | wc -l)"
                read -p "按回车键继续..."
                ;;
            4)
                echo_title "进程监控"
                ps aux | head -20
                read -p "按回车键继续..."
                ;;
            5)
                echo_title "磁盘IO监控"
                if command_exists iostat; then
                    iostat -x 1 1
                else
                    echo_warning "请安装 sysstat 包以使用 iostat 命令"
                fi
                read -p "按回车键继续..."
                ;;
            6)
                echo_info "生成性能报告..."
                generate_health_report
                read -p "按回车键继续..."
                ;;
            7)
                echo_info "告警设置功能开发中..."
                read -p "按回车键继续..."
                ;;
            0)
                break
                ;;
            *)
                echo_error "无效选择"
                sleep 1
                ;;
        esac
    done
}

# 日志管理菜单
log_management_menu() {
    while true; do
        clear
        echo_title "日志管理"

        # 显示日志状态概览
        echo_subtitle "日志状态概览"
        local total_logs=$(find "$SCRIPT_DIR/logs" -name "*.log" -type f 2>/dev/null | wc -l)
        local total_size=$(find "$SCRIPT_DIR/logs" -name "*.log" -type f -exec stat -c%s {} + 2>/dev/null | awk '{sum+=$1} END {print sum}')
        local compressed_logs=$(find "$SCRIPT_DIR/logs" -name "*.log.gz" -type f 2>/dev/null | wc -l)

        show_key_value "日志文件数" "$total_logs 个"
        show_key_value "总大小" "$(format_size ${total_size:-0})"
        show_key_value "压缩文件" "$compressed_logs 个"
        echo ""

        echo_color "$YELLOW" "日志管理选项："
        echo "  1. 查看系统日志      2. 查看服务日志      3. 查看操作日志"
        echo "  4. 实时监控日志      5. 日志搜索          6. 错误分析"
        echo "  7. 趋势分析          8. 性能分析          9. 高级管理"
        echo "  10. 日志轮转         11. 清理旧日志       12. 生成报告"
        echo "  0. 返回主菜单"
        echo ""

        read -p "请选择操作 (0-12): " choice

        case "$choice" in
            1)
                read -p "时间范围 (1h/today/yesterday/week): " time_range
                "$SCRIPT_DIR/core/log_manager.sh" system "$time_range"
                read -p "按回车键继续..."
                ;;
            2)
                echo "可用服务: ${ALL_SERVICES[*]}"
                read -p "请输入服务名: " service_name
                read -p "时间范围 (1h/today/yesterday/week): " time_range
                if [[ " ${ALL_SERVICES[*]} " =~ " $service_name " ]]; then
                    "$SCRIPT_DIR/core/log_manager.sh" view "$service_name" "$time_range"
                else
                    echo_error "无效的服务名"
                fi
                read -p "按回车键继续..."
                ;;
            3)
                read -p "时间范围 (1h/today/yesterday/week): " time_range
                "$SCRIPT_DIR/core/log_manager.sh" operations "$time_range"
                read -p "按回车键继续..."
                ;;
            4)
                echo "可用服务: ${ALL_SERVICES[*]} (留空监控所有)"
                read -p "请输入服务名: " service_name
                echo_info "开始实时监控 (按 Ctrl+C 退出)..."
                if [[ -n "$service_name" ]]; then
                    "$SCRIPT_DIR/core/log_monitor.sh" start "$service_name"
                else
                    "$SCRIPT_DIR/core/log_monitor.sh" start
                fi
                ;;
            5)
                read -p "搜索关键词: " keyword
                read -p "服务名 (留空搜索所有): " service_name
                "$SCRIPT_DIR/core/log_analyzer.sh" search "$keyword" "$service_name"
                read -p "按回车键继续..."
                ;;
            6)
                read -p "服务名 (留空分析所有): " service_name
                "$SCRIPT_DIR/core/log_analyzer.sh" errors "$service_name"
                read -p "按回车键继续..."
                ;;
            7)
                read -p "服务名 (留空分析所有): " service_name
                read -p "分析天数 (默认7天): " days
                "$SCRIPT_DIR/core/log_analyzer.sh" trends "$service_name" "${days:-7}"
                read -p "按回车键继续..."
                ;;
            8)
                read -p "服务名 (留空分析所有): " service_name
                "$SCRIPT_DIR/core/log_analyzer.sh" performance "$service_name"
                read -p "按回车键继续..."
                ;;
            9)
                "$SCRIPT_DIR/core/log_manager.sh" advanced
                ;;
            10)
                if ask_confirmation "确定要执行日志轮转吗？"; then
                    "$SCRIPT_DIR/core/log_rotator.sh" auto
                fi
                read -p "按回车键继续..."
                ;;
            11)
                read -p "清理几天前的日志 (默认7天): " days
                if ask_confirmation "确定要清理 ${days:-7} 天前的旧日志吗？"; then
                    "$SCRIPT_DIR/core/log_rotator.sh" cleanup "$SCRIPT_DIR/logs" "${days:-7}"
                fi
                read -p "按回车键继续..."
                ;;
            12)
                read -p "服务名 (留空生成综合报告): " service_name
                "$SCRIPT_DIR/core/log_analyzer.sh" report "$service_name"
                read -p "按回车键继续..."
                ;;
            0)
                break
                ;;
            *)
                echo_error "无效选择"
                sleep 1
                ;;
        esac
    done
}

# 健康检查菜单
health_check_menu() {
    clear
    echo_title "系统健康检查"
    
    echo_progress "执行全面健康检查..."
    check_all
    
    echo ""
    echo_color "$YELLOW" "健康检查选项："
    echo "  1. 重新检查    2. 生成报告    3. 查看历史报告    0. 返回主菜单"
    echo ""
    
    read -p "请选择操作 (0-3): " choice
    
    case "$choice" in
        1)
            health_check_menu
            ;;
        2)
            generate_health_report
            read -p "按回车键继续..."
            ;;
        3)
            echo_info "查看历史健康报告..."
            ls -la "$SCRIPT_DIR/logs/system/health_report_"*.log 2>/dev/null || echo "暂无历史报告"
            read -p "按回车键继续..."
            ;;
        0)
            return
            ;;
        *)
            echo_error "无效选择"
            sleep 1
            health_check_menu
            ;;
    esac
}

# 自动修复菜单
auto_repair_menu() {
    clear
    echo_title "自动修复"

    echo_warning "自动修复将尝试解决检测到的问题，请确认后继续"

    if ask_confirmation "是否执行自动修复？"; then
        repair_all
    else
        echo_info "已取消自动修复"
    fi

    read -p "按回车键继续..."
}

# 备份恢复菜单
backup_restore_menu() {
    while true; do
        clear
        echo_title "备份恢复管理"

        # 显示备份状态概览
        echo_subtitle "备份状态概览"
        local backup_root="$SCRIPT_DIR/backups"
        if [[ -d "$backup_root" ]]; then
            local db_backups=$(find "$backup_root/database" -name "*.sql*" 2>/dev/null | wc -l)
            local config_backups=$(find "$backup_root/config" -name "*.tar.gz" 2>/dev/null | wc -l)
            local log_backups=$(find "$backup_root/logs" -name "*.tar.gz" 2>/dev/null | wc -l)
            local full_backups=$(find "$backup_root/full" -type d -mindepth 1 2>/dev/null | wc -l)

            show_key_value "数据库备份" "$db_backups 个"
            show_key_value "配置文件备份" "$config_backups 个"
            show_key_value "日志备份" "$log_backups 个"
            show_key_value "完整备份" "$full_backups 个"
        else
            echo_warning "备份目录不存在"
        fi
        echo ""

        echo_color "$YELLOW" "备份恢复选项："
        echo "  1. 数据库备份      2. 配置文件备份    3. 日志文件备份"
        echo "  4. 系统文件备份    5. 完整备份        6. 数据库恢复"
        echo "  7. 配置文件恢复    8. 日志文件恢复    9. 列出备份"
        echo "  10. 清理过期备份   11. 备份统计       0. 返回主菜单"
        echo ""

        read -p "请选择操作 (0-11): " choice

        case "$choice" in
            1)
                echo_progress "执行数据库备份..."
                "$SCRIPT_DIR/core/backup_manager.sh" database
                read -p "按回车键继续..."
                ;;
            2)
                echo_progress "执行配置文件备份..."
                "$SCRIPT_DIR/core/backup_manager.sh" config
                read -p "按回车键继续..."
                ;;
            3)
                read -p "备份最近几天的日志 (默认7天): " days
                echo_progress "执行日志文件备份..."
                "$SCRIPT_DIR/core/backup_manager.sh" logs "" "${days:-7}"
                read -p "按回车键继续..."
                ;;
            4)
                echo_progress "执行系统文件备份..."
                "$SCRIPT_DIR/core/backup_manager.sh" system
                read -p "按回车键继续..."
                ;;
            5)
                read -p "完整备份名称 (留空自动生成): " backup_name
                echo_progress "执行完整备份..."
                "$SCRIPT_DIR/core/backup_manager.sh" full "$backup_name"
                read -p "按回车键继续..."
                ;;
            6)
                echo_subtitle "可用的数据库备份:"
                "$SCRIPT_DIR/core/restore_manager.sh" list database
                echo ""
                read -p "请输入备份文件名: " backup_file
                if [[ -n "$backup_file" ]]; then
                    "$SCRIPT_DIR/core/restore_manager.sh" database "$backup_file"
                fi
                read -p "按回车键继续..."
                ;;
            7)
                echo_subtitle "可用的配置文件备份:"
                "$SCRIPT_DIR/core/restore_manager.sh" list config
                echo ""
                read -p "请输入备份文件名: " backup_file
                if [[ -n "$backup_file" ]]; then
                    "$SCRIPT_DIR/core/restore_manager.sh" config "$backup_file"
                fi
                read -p "按回车键继续..."
                ;;
            8)
                echo_subtitle "可用的日志文件备份:"
                "$SCRIPT_DIR/core/restore_manager.sh" list logs
                echo ""
                read -p "请输入备份文件名: " backup_file
                if [[ -n "$backup_file" ]]; then
                    "$SCRIPT_DIR/core/restore_manager.sh" logs "$backup_file"
                fi
                read -p "按回车键继续..."
                ;;
            9)
                "$SCRIPT_DIR/core/backup_manager.sh" list
                read -p "按回车键继续..."
                ;;
            10)
                read -p "清理几天前的备份 (默认30天): " days
                if ask_confirmation "确定要清理 ${days:-30} 天前的备份吗？"; then
                    "$SCRIPT_DIR/core/backup_manager.sh" cleanup "${days:-30}"
                fi
                read -p "按回车键继续..."
                ;;
            11)
                "$SCRIPT_DIR/core/backup_manager.sh" stats
                read -p "按回车键继续..."
                ;;
            0)
                break
                ;;
            *)
                echo_error "无效选择"
                sleep 1
                ;;
        esac
    done
}

# 主循环
main_loop() {
    while [[ "$PANEL_RUNNING" == "true" ]]; do
        show_main_menu
        
        read -n 1 -r choice
        echo ""
        
        case "$choice" in
            1)
                service_management_menu
                ;;
            2)
                system_monitoring_menu
                ;;
            3)
                log_management_menu
                ;;
            4)
                health_check_menu
                ;;
            5)
                auto_repair_menu
                ;;
            6)
                clear
                echo_title "系统信息"
                get_system_info
                read -p "按回车键继续..."
                ;;
            7)
                echo_info "配置管理功能开发中..."
                read -p "按回车键继续..."
                ;;
            8)
                backup_restore_menu
                ;;
            9)
                echo_info "性能监控功能开发中..."
                read -p "按回车键继续..."
                ;;
            0)
                echo_color "$GREEN" "感谢使用运维管理面板！"
                PANEL_RUNNING=false
                ;;
            *)
                echo_error "无效选择，请重新输入"
                sleep 1
                ;;
        esac
    done
}

# 主函数
main() {
    # 检查终端支持
    if [[ ! -t 0 ]]; then
        echo_error "运维面板需要在交互式终端中运行"
        exit 1
    fi
    
    # 设置信号处理
    trap 'echo_color "$YELLOW" "\n面板已退出"; exit 0' INT TERM
    
    # 启动主循环
    main_loop
}

# 如果直接执行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
