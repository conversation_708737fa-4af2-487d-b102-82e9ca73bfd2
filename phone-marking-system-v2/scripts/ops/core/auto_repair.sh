#!/bin/bash

# =============================================================================
# 自动修复模块
# 提供服务异常自动修复、配置恢复、依赖问题解决等功能
# =============================================================================

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

# 加载依赖
source "$SCRIPT_DIR/utils/common.sh"
source "$SCRIPT_DIR/utils/colors.sh"
source "$SCRIPT_DIR/config/services.conf"
source "$SCRIPT_DIR/core/service_manager.sh"
source "$SCRIPT_DIR/core/health_checker.sh"

# 修复单个服务
repair_service() {
    local service_name="$1"
    local max_attempts="${2:-3}"
    
    echo_progress "修复服务: $service_name"
    
    local attempt=1
    while [[ $attempt -le $max_attempts ]]; do
        echo_info "第 $attempt 次修复尝试..."
        
        # 检查服务状态
        if check_service_health "$service_name" false; then
            echo_success "服务 $service_name 已恢复正常"
            log_system "INFO" "服务修复成功: $service_name (尝试次数: $attempt)"
            return 0
        fi
        
        # 尝试重启服务
        echo_info "尝试重启服务..."
        if restart_service "$service_name"; then
            sleep 5
            if check_service_health "$service_name" false; then
                echo_success "服务 $service_name 重启后恢复正常"
                log_system "INFO" "服务重启修复成功: $service_name"
                return 0
            fi
        fi
        
        # 检查端口冲突
        local port_var="${service_name^^}_PORT"
        local port="${!port_var}"
        if [[ -n "$port" ]]; then
            local conflicting_pid=$(lsof -ti:$port 2>/dev/null)
            if [[ -n "$conflicting_pid" ]]; then
                echo_warning "发现端口 $port 冲突，尝试解决..."
                if kill -15 "$conflicting_pid" 2>/dev/null; then
                    sleep 3
                    echo_info "已终止冲突进程，重新启动服务..."
                    if start_service "$service_name"; then
                        sleep 5
                        if check_service_health "$service_name" false; then
                            echo_success "端口冲突解决，服务恢复正常"
                            return 0
                        fi
                    fi
                fi
            fi
        fi
        
        # 检查依赖服务
        echo_info "检查依赖服务..."
        local dependencies="${SERVICE_DEPENDENCIES[$service_name]}"
        if [[ -n "$dependencies" ]]; then
            for dep in $dependencies; do
                if ! check_service_health "$dep" false; then
                    echo_warning "依赖服务 $dep 异常，尝试修复..."
                    if repair_service "$dep" 2; then
                        echo_info "依赖服务 $dep 已修复，重新启动 $service_name..."
                        if start_service "$service_name"; then
                            sleep 5
                            if check_service_health "$service_name" false; then
                                echo_success "依赖修复后服务恢复正常"
                                return 0
                            fi
                        fi
                    fi
                fi
            done
        fi
        
        # 清理临时文件和PID文件
        echo_info "清理临时文件..."
        rm -f "/tmp/${service_name}.pid"
        
        ((attempt++))
        if [[ $attempt -le $max_attempts ]]; then
            echo_warning "第 $((attempt-1)) 次修复失败，等待 ${RESTART_INTERVAL:-10} 秒后重试..."
            sleep "${RESTART_INTERVAL:-10}"
        fi
    done
    
    echo_error "服务 $service_name 修复失败，已尝试 $max_attempts 次"
    log_system "ERROR" "服务修复失败: $service_name (尝试次数: $max_attempts)"
    return 1
}

# 修复系统资源问题
repair_system_resources() {
    echo_subtitle "修复系统资源问题"
    
    local fixed_issues=0
    
    # 清理临时文件
    echo_info "清理临时文件..."
    if find /tmp -type f -name "*.tmp" -mtime +1 -delete 2>/dev/null; then
        echo_success "已清理临时文件"
        ((fixed_issues++))
    fi
    
    # 清理日志文件
    echo_info "清理旧日志文件..."
    if cleanup_old_logs "$SCRIPT_DIR/logs" "${LOG_RETENTION_DAYS:-7}"; then
        echo_success "已清理旧日志文件"
        ((fixed_issues++))
    fi
    
    # 检查磁盘空间
    local disk_usage=$(df / | awk 'NR==2{print $5}' | sed 's/%//')
    if [[ $disk_usage -gt ${DISK_ALERT_THRESHOLD:-90} ]]; then
        echo_warning "磁盘空间不足，尝试清理..."
        
        # 清理包管理器缓存
        if command_exists apt-get; then
            sudo apt-get clean >/dev/null 2>&1
            echo_info "已清理APT缓存"
        elif command_exists yum; then
            sudo yum clean all >/dev/null 2>&1
            echo_info "已清理YUM缓存"
        fi
        
        # 清理Docker镜像（如果存在）
        if command_exists docker; then
            docker system prune -f >/dev/null 2>&1
            echo_info "已清理Docker缓存"
        fi
        
        ((fixed_issues++))
    fi
    
    # 重启系统服务（如果需要）
    local memory_usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    if [[ $memory_usage -gt ${MEMORY_ALERT_THRESHOLD:-85} ]]; then
        echo_warning "内存使用率过高，建议重启相关服务"
        # 这里可以添加内存清理逻辑
    fi
    
    if [[ $fixed_issues -gt 0 ]]; then
        echo_success "已修复 $fixed_issues 个系统资源问题"
        return 0
    else
        echo_info "未发现需要修复的系统资源问题"
        return 0
    fi
}

# 修复数据库连接问题
repair_database_connection() {
    echo_subtitle "修复数据库连接问题"
    
    # 检查MySQL
    if ! mysql -h"$MYSQL_HOST" -P"$MYSQL_PORT" -u"$MYSQL_USER" -e "SELECT 1" >/dev/null 2>&1; then
        echo_warning "MySQL连接失败，尝试修复..."
        
        # 检查MySQL服务状态
        if ! systemctl is-active mysql >/dev/null 2>&1; then
            echo_info "启动MySQL服务..."
            if sudo systemctl start mysql; then
                sleep 5
                if mysql -h"$MYSQL_HOST" -P"$MYSQL_PORT" -u"$MYSQL_USER" -e "SELECT 1" >/dev/null 2>&1; then
                    echo_success "MySQL服务已启动并连接正常"
                    return 0
                fi
            fi
        fi
        
        # 检查MySQL配置
        if [[ -f "/etc/mysql/mysql.conf.d/mysqld.cnf" ]]; then
            if ! grep -q "bind-address.*$MYSQL_HOST" /etc/mysql/mysql.conf.d/mysqld.cnf; then
                echo_warning "MySQL绑定地址可能有问题"
            fi
        fi
        
        echo_error "MySQL连接修复失败"
        return 1
    fi
    
    # 检查Redis
    if ! redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" ping >/dev/null 2>&1; then
        echo_warning "Redis连接失败，尝试修复..."
        
        # 检查Redis服务状态
        if ! systemctl is-active redis >/dev/null 2>&1; then
            echo_info "启动Redis服务..."
            if sudo systemctl start redis; then
                sleep 3
                if redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" ping >/dev/null 2>&1; then
                    echo_success "Redis服务已启动并连接正常"
                    return 0
                fi
            fi
        fi
        
        echo_error "Redis连接修复失败"
        return 1
    fi
    
    echo_success "数据库连接正常"
    return 0
}

# 修复端口冲突
repair_port_conflicts() {
    echo_subtitle "修复端口冲突"
    
    local fixed_conflicts=0
    
    for service in "${ALL_SERVICES[@]}"; do
        local port_var="${service^^}_PORT"
        local port="${!port_var}"
        
        if [[ -n "$port" ]]; then
            local conflicting_pid=$(lsof -ti:$port 2>/dev/null)
            if [[ -n "$conflicting_pid" ]]; then
                local process_name=$(ps -p $conflicting_pid -o comm= 2>/dev/null)
                
                # 如果不是目标服务进程，则终止
                if [[ -n "$process_name" ]] && [[ "$process_name" != *"$service"* ]]; then
                    echo_warning "端口 $port 被进程 $process_name (PID: $conflicting_pid) 占用"
                    
                    if ask_confirmation "是否终止冲突进程？" "y"; then
                        if kill -15 "$conflicting_pid" 2>/dev/null; then
                            sleep 2
                            if ! kill -0 "$conflicting_pid" 2>/dev/null; then
                                echo_success "已终止冲突进程 $process_name"
                                ((fixed_conflicts++))
                            else
                                echo_warning "强制终止进程..."
                                kill -9 "$conflicting_pid" 2>/dev/null
                                ((fixed_conflicts++))
                            fi
                        fi
                    fi
                fi
            fi
        fi
    done
    
    if [[ $fixed_conflicts -gt 0 ]]; then
        echo_success "已修复 $fixed_conflicts 个端口冲突"
        return 0
    else
        echo_info "未发现端口冲突"
        return 0
    fi
}

# 修复配置文件
repair_configurations() {
    echo_subtitle "检查和修复配置文件"
    
    local config_files=(
        "$PROJECT_ROOT/dash-fastapi-admin/dash-fastapi-backend/config/env.py"
        "$PROJECT_ROOT/dash-fastapi-admin/dash-fastapi-frontend/config/env.py"
        "$SCRIPT_DIR/config/services.conf"
    )
    
    local fixed_configs=0
    
    for config_file in "${config_files[@]}"; do
        if [[ -f "$config_file" ]]; then
            if validate_config "$config_file"; then
                echo_success "✅ 配置文件正常: $(basename "$config_file")"
            else
                echo_warning "⚠️  配置文件异常: $(basename "$config_file")"
                
                # 尝试从备份恢复
                local backup_file="${config_file}.backup"
                if [[ -f "$backup_file" ]]; then
                    if ask_confirmation "是否从备份恢复配置文件？" "y"; then
                        if cp "$backup_file" "$config_file"; then
                            echo_success "已从备份恢复配置文件"
                            ((fixed_configs++))
                        fi
                    fi
                fi
            fi
        else
            echo_error "❌ 配置文件不存在: $(basename "$config_file")"
        fi
    done
    
    if [[ $fixed_configs -gt 0 ]]; then
        echo_success "已修复 $fixed_configs 个配置文件"
    fi
    
    return 0
}

# 修复所有问题
repair_all() {
    echo_title "自动修复系统问题"
    
    local total_fixes=0
    local failed_repairs=()
    
    # 1. 修复系统资源问题
    if repair_system_resources; then
        ((total_fixes++))
    fi
    
    # 2. 修复数据库连接
    if repair_database_connection; then
        ((total_fixes++))
    fi
    
    # 3. 修复端口冲突
    if repair_port_conflicts; then
        ((total_fixes++))
    fi
    
    # 4. 修复配置文件
    if repair_configurations; then
        ((total_fixes++))
    fi
    
    # 5. 修复服务问题
    echo_subtitle "修复服务问题"
    for service in "${REQUIRED_SERVICES[@]}"; do
        if ! check_service_health "$service" false; then
            echo_warning "服务 $service 异常，尝试修复..."
            if repair_service "$service"; then
                ((total_fixes++))
            else
                failed_repairs+=("$service")
            fi
        fi
    done
    
    # 6. 修复可选服务
    for service in "${OPTIONAL_SERVICES[@]}"; do
        if ! check_service_health "$service" false; then
            echo_info "可选服务 $service 异常，尝试修复..."
            if repair_service "$service" 2; then
                ((total_fixes++))
            else
                echo_warning "可选服务 $service 修复失败（不影响核心功能）"
            fi
        fi
    done
    
    echo ""
    
    # 显示修复结果
    if [[ ${#failed_repairs[@]} -eq 0 ]]; then
        echo_done "🎉 自动修复完成！共修复 $total_fixes 个问题"
        
        # 执行最终健康检查
        echo_info "执行最终健康检查..."
        if check_all >/dev/null 2>&1; then
            echo_success "✅ 系统健康检查通过"
        else
            echo_warning "⚠️  部分问题仍需手动处理"
        fi
    else
        echo_error "❌ 以下服务修复失败，需要手动处理："
        for service in "${failed_repairs[@]}"; do
            echo_error "  - $service"
        done
        echo ""
        echo_info "建议检查相关日志文件获取详细错误信息"
        return 1
    fi
    
    log_system "INFO" "自动修复完成，修复问题数: $total_fixes，失败服务数: ${#failed_repairs[@]}"
    return 0
}

# 主函数
main() {
    case "${1:-repair_all}" in
        "repair_all")
            repair_all
            ;;
        "repair_service")
            repair_service "$2" "$3"
            ;;
        "repair_resources")
            repair_system_resources
            ;;
        "repair_database")
            repair_database_connection
            ;;
        "repair_ports")
            repair_port_conflicts
            ;;
        "repair_configs")
            repair_configurations
            ;;
        *)
            echo_error "未知命令: $1"
            echo "用法: $0 {repair_all|repair_service|repair_resources|repair_database|repair_ports|repair_configs} [service_name] [max_attempts]"
            exit 1
            ;;
    esac
}

# 如果直接执行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
