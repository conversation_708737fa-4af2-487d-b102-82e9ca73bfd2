#!/bin/bash

# =============================================================================
# 日志轮转和清理工具
# 提供自动日志轮转、压缩、清理等功能，防止日志文件过大
# =============================================================================

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

# 加载依赖
source "$SCRIPT_DIR/utils/common.sh"
source "$SCRIPT_DIR/utils/colors.sh"
source "$SCRIPT_DIR/config/services.conf"

# 日志轮转配置
LOG_ROTATE_SIZE=${LOG_MAX_SIZE:-100}  # MB
LOG_RETENTION_DAYS=${LOG_RETENTION_DAYS:-7}
LOG_COMPRESS_ENABLED=${LOG_ROTATION_ENABLED:-true}

# 检查日志文件大小
check_log_size() {
    local log_file="$1"
    local max_size_mb="$2"
    
    if [[ ! -f "$log_file" ]]; then
        return 1
    fi
    
    local file_size=$(stat -c%s "$log_file" 2>/dev/null || stat -f%z "$log_file" 2>/dev/null || echo "0")
    local size_mb=$((file_size / 1024 / 1024))
    
    if [[ $size_mb -gt $max_size_mb ]]; then
        return 0  # 需要轮转
    else
        return 1  # 不需要轮转
    fi
}

# 轮转单个日志文件
rotate_log_file() {
    local log_file="$1"
    local keep_count="${2:-5}"
    
    if [[ ! -f "$log_file" ]]; then
        echo_warning "日志文件不存在: $log_file"
        return 1
    fi
    
    local log_dir=$(dirname "$log_file")
    local log_name=$(basename "$log_file")
    local log_base="${log_name%.*}"
    local log_ext="${log_name##*.}"
    
    echo_progress "轮转日志文件: $log_name"
    
    # 移动现有的轮转文件
    for ((i=keep_count; i>1; i--)); do
        local old_file="$log_dir/${log_base}.${i}.$log_ext"
        local new_file="$log_dir/${log_base}.$((i+1)).$log_ext"
        
        if [[ -f "$old_file" ]]; then
            mv "$old_file" "$new_file" 2>/dev/null
        fi
    done
    
    # 移动当前日志文件
    local rotated_file="$log_dir/${log_base}.1.$log_ext"
    if mv "$log_file" "$rotated_file" 2>/dev/null; then
        echo_success "✅ 日志文件已轮转: $log_name -> ${log_base}.1.$log_ext"
        
        # 创建新的空日志文件
        touch "$log_file"
        chmod 644 "$log_file"
        
        # 压缩轮转的日志文件
        if [[ "$LOG_COMPRESS_ENABLED" == "true" ]] && command_exists gzip; then
            echo_progress "压缩日志文件: ${log_base}.1.$log_ext"
            if gzip "$rotated_file" 2>/dev/null; then
                echo_success "✅ 日志文件已压缩: ${log_base}.1.$log_ext.gz"
            else
                echo_warning "⚠️  日志文件压缩失败"
            fi
        fi
        
        # 删除过期的轮转文件
        for ((i=keep_count+1; i<=10; i++)); do
            local old_file="$log_dir/${log_base}.${i}.$log_ext"
            local old_gz_file="$log_dir/${log_base}.${i}.$log_ext.gz"
            
            if [[ -f "$old_file" ]]; then
                rm -f "$old_file"
                echo_info "🗑️  删除过期日志: ${log_base}.${i}.$log_ext"
            fi
            
            if [[ -f "$old_gz_file" ]]; then
                rm -f "$old_gz_file"
                echo_info "🗑️  删除过期日志: ${log_base}.${i}.$log_ext.gz"
            fi
        done
        
        log_system "INFO" "日志轮转完成: $log_file"
        return 0
    else
        echo_error "❌ 日志文件轮转失败: $log_file"
        return 1
    fi
}

# 按时间轮转日志
rotate_by_time() {
    local log_file="$1"
    local interval="${2:-daily}"  # daily, weekly, monthly
    
    if [[ ! -f "$log_file" ]]; then
        return 1
    fi
    
    local log_dir=$(dirname "$log_file")
    local log_name=$(basename "$log_file")
    local log_base="${log_name%.*}"
    local log_ext="${log_name##*.}"
    
    local timestamp=""
    case "$interval" in
        "daily")
            timestamp=$(date '+%Y%m%d')
            ;;
        "weekly")
            timestamp=$(date '+%Y%U')
            ;;
        "monthly")
            timestamp=$(date '+%Y%m')
            ;;
        *)
            timestamp=$(date '+%Y%m%d_%H%M%S')
            ;;
    esac
    
    local rotated_file="$log_dir/${log_base}_${timestamp}.$log_ext"
    
    # 检查是否已经轮转过
    if [[ -f "$rotated_file" ]]; then
        return 0
    fi
    
    echo_progress "按时间轮转日志: $log_name"
    
    if cp "$log_file" "$rotated_file" 2>/dev/null; then
        # 清空原文件
        > "$log_file"
        
        echo_success "✅ 日志按时间轮转完成: $log_name -> ${log_base}_${timestamp}.$log_ext"
        
        # 压缩轮转的日志文件
        if [[ "$LOG_COMPRESS_ENABLED" == "true" ]] && command_exists gzip; then
            if gzip "$rotated_file" 2>/dev/null; then
                echo_success "✅ 日志文件已压缩: ${log_base}_${timestamp}.$log_ext.gz"
            fi
        fi
        
        log_system "INFO" "日志按时间轮转完成: $log_file"
        return 0
    else
        echo_error "❌ 日志按时间轮转失败: $log_file"
        return 1
    fi
}

# 清理过期日志
cleanup_old_logs() {
    local log_dir="$1"
    local days="${2:-$LOG_RETENTION_DAYS}"
    
    echo_title "清理过期日志文件"
    echo_info "目录: $log_dir"
    echo_info "保留天数: $days"
    
    if [[ ! -d "$log_dir" ]]; then
        echo_warning "日志目录不存在: $log_dir"
        return 1
    fi
    
    local deleted_count=0
    local freed_space=0
    
    # 查找并删除过期的日志文件
    while IFS= read -r -d '' file; do
        local file_size=$(stat -c%s "$file" 2>/dev/null || stat -f%z "$file" 2>/dev/null || echo "0")
        local file_name=$(basename "$file")
        
        echo_info "🗑️  删除过期日志: $file_name ($(format_size $file_size))"
        
        if rm -f "$file"; then
            ((deleted_count++))
            freed_space=$((freed_space + file_size))
        else
            echo_warning "⚠️  删除失败: $file_name"
        fi
    done < <(find "$log_dir" -name "*.log" -type f -mtime +$days -print0 2>/dev/null)
    
    # 查找并删除过期的压缩日志文件
    while IFS= read -r -d '' file; do
        local file_size=$(stat -c%s "$file" 2>/dev/null || stat -f%z "$file" 2>/dev/null || echo "0")
        local file_name=$(basename "$file")
        
        echo_info "🗑️  删除过期压缩日志: $file_name ($(format_size $file_size))"
        
        if rm -f "$file"; then
            ((deleted_count++))
            freed_space=$((freed_space + file_size))
        else
            echo_warning "⚠️  删除失败: $file_name"
        fi
    done < <(find "$log_dir" -name "*.log.gz" -type f -mtime +$days -print0 2>/dev/null)
    
    echo ""
    echo_success "✅ 清理完成"
    show_key_value "删除文件数" "$deleted_count"
    show_key_value "释放空间" "$(format_size $freed_space)"
    
    log_system "INFO" "日志清理完成: 删除 $deleted_count 个文件，释放 $(format_size $freed_space) 空间"
}

# 压缩日志文件
compress_logs() {
    local log_dir="$1"
    local days_old="${2:-1}"
    
    echo_title "压缩日志文件"
    echo_info "目录: $log_dir"
    echo_info "压缩 $days_old 天前的日志文件"
    
    if [[ ! -d "$log_dir" ]]; then
        echo_warning "日志目录不存在: $log_dir"
        return 1
    fi
    
    if ! command_exists gzip; then
        echo_error "❌ gzip 命令不可用"
        return 1
    fi
    
    local compressed_count=0
    local saved_space=0
    
    # 查找并压缩符合条件的日志文件
    while IFS= read -r -d '' file; do
        local file_name=$(basename "$file")
        local original_size=$(stat -c%s "$file" 2>/dev/null || stat -f%z "$file" 2>/dev/null || echo "0")
        
        echo_progress "压缩日志文件: $file_name"
        
        if gzip "$file" 2>/dev/null; then
            local compressed_size=$(stat -c%s "${file}.gz" 2>/dev/null || stat -f%z "${file}.gz" 2>/dev/null || echo "0")
            local space_saved=$((original_size - compressed_size))
            
            echo_success "✅ 压缩完成: $file_name.gz (节省 $(format_size $space_saved))"
            
            ((compressed_count++))
            saved_space=$((saved_space + space_saved))
        else
            echo_warning "⚠️  压缩失败: $file_name"
        fi
    done < <(find "$log_dir" -name "*.log" -type f -mtime +$days_old ! -name "*$(date +%Y%m%d)*" -print0 2>/dev/null)
    
    echo ""
    echo_success "✅ 压缩完成"
    show_key_value "压缩文件数" "$compressed_count"
    show_key_value "节省空间" "$(format_size $saved_space)"
    
    log_system "INFO" "日志压缩完成: 压缩 $compressed_count 个文件，节省 $(format_size $saved_space) 空间"
}

# 自动日志维护
auto_maintenance() {
    echo_title "自动日志维护"
    
    local maintenance_log="$SCRIPT_DIR/logs/system/log_maintenance_$(date +%Y%m%d).log"
    
    {
        echo "========================================"
        echo "日志维护开始: $(get_timestamp)"
        echo "========================================"
        
        # 检查所有服务日志文件
        for service in "${ALL_SERVICES[@]}"; do
            local service_upper=$(to_upper "$service")
            local log_file_var="${service_upper}_LOG_FILE"
            local log_file="${!log_file_var}"
            
            if [[ -z "$log_file" ]]; then
                log_file="$SCRIPT_DIR/logs/services/${service}_$(date +%Y%m%d).log"
            fi
            
            if [[ -f "$log_file" ]]; then
                echo "检查服务日志: $service"
                
                # 检查文件大小并轮转
                if check_log_size "$log_file" "$LOG_ROTATE_SIZE"; then
                    echo "日志文件过大，执行轮转: $log_file"
                    rotate_log_file "$log_file" 5
                fi
            fi
        done
        
        # 压缩旧日志
        echo "压缩旧日志文件..."
        compress_logs "$SCRIPT_DIR/logs/services" 1
        compress_logs "$SCRIPT_DIR/logs/system" 1
        
        # 清理过期日志
        echo "清理过期日志文件..."
        cleanup_old_logs "$SCRIPT_DIR/logs/services" "$LOG_RETENTION_DAYS"
        cleanup_old_logs "$SCRIPT_DIR/logs/system" "$LOG_RETENTION_DAYS"
        cleanup_old_logs "$SCRIPT_DIR/logs/operations" "$LOG_RETENTION_DAYS"
        
        echo "========================================"
        echo "日志维护完成: $(get_timestamp)"
        echo "========================================"
        
    } | tee -a "$maintenance_log"
    
    echo_success "🎉 自动日志维护完成"
    echo_info "维护日志: $maintenance_log"
}

# 日志统计信息
log_statistics() {
    echo_title "日志文件统计信息"
    
    local total_files=0
    local total_size=0
    local compressed_files=0
    local compressed_size=0
    
    # 统计各目录的日志文件
    local log_dirs=("$SCRIPT_DIR/logs/services" "$SCRIPT_DIR/logs/system" "$SCRIPT_DIR/logs/operations")
    
    for log_dir in "${log_dirs[@]}"; do
        if [[ -d "$log_dir" ]]; then
            echo_subtitle "目录: $(basename "$log_dir")"
            echo_table_header "文件名" "大小" "修改时间" "类型"
            
            # 统计普通日志文件
            while IFS= read -r -d '' file; do
                local file_name=$(basename "$file")
                local file_size=$(stat -c%s "$file" 2>/dev/null || stat -f%z "$file" 2>/dev/null || echo "0")
                local mod_time=$(stat -c%y "$file" 2>/dev/null | cut -d' ' -f1 || stat -f%Sm "$file" 2>/dev/null || echo "未知")
                
                echo_table_row "$file_name" "$(format_size $file_size)" "$mod_time" "日志"
                
                ((total_files++))
                total_size=$((total_size + file_size))
            done < <(find "$log_dir" -name "*.log" -type f -print0 2>/dev/null)
            
            # 统计压缩日志文件
            while IFS= read -r -d '' file; do
                local file_name=$(basename "$file")
                local file_size=$(stat -c%s "$file" 2>/dev/null || stat -f%z "$file" 2>/dev/null || echo "0")
                local mod_time=$(stat -c%y "$file" 2>/dev/null | cut -d' ' -f1 || stat -f%Sm "$file" 2>/dev/null || echo "未知")
                
                echo_table_row "$file_name" "$(format_size $file_size)" "$mod_time" "压缩"
                
                ((compressed_files++))
                compressed_size=$((compressed_size + file_size))
            done < <(find "$log_dir" -name "*.log.gz" -type f -print0 2>/dev/null)
            
            echo ""
        fi
    done
    
    # 显示总计
    echo_subtitle "统计总计"
    show_key_value "普通日志文件" "$total_files 个"
    show_key_value "普通日志大小" "$(format_size $total_size)"
    show_key_value "压缩日志文件" "$compressed_files 个"
    show_key_value "压缩日志大小" "$(format_size $compressed_size)"
    show_key_value "总文件数" "$((total_files + compressed_files)) 个"
    show_key_value "总大小" "$(format_size $((total_size + compressed_size)))"
}

# 主函数
main() {
    case "${1:-help}" in
        "rotate")
            if [[ -n "$2" ]]; then
                rotate_log_file "$2" "${3:-5}"
            else
                echo_error "请指定要轮转的日志文件"
                exit 1
            fi
            ;;
        "rotate-time")
            if [[ -n "$2" ]]; then
                rotate_by_time "$2" "${3:-daily}"
            else
                echo_error "请指定要轮转的日志文件"
                exit 1
            fi
            ;;
        "cleanup")
            cleanup_old_logs "${2:-$SCRIPT_DIR/logs}" "${3:-$LOG_RETENTION_DAYS}"
            ;;
        "compress")
            compress_logs "${2:-$SCRIPT_DIR/logs}" "${3:-1}"
            ;;
        "auto")
            auto_maintenance
            ;;
        "stats")
            log_statistics
            ;;
        *)
            echo_error "未知命令: $1"
            echo "用法: $0 {rotate|rotate-time|cleanup|compress|auto|stats} [参数...]"
            echo ""
            echo "命令说明:"
            echo "  rotate <日志文件> [保留数量]           # 按大小轮转日志"
            echo "  rotate-time <日志文件> [间隔]          # 按时间轮转日志"
            echo "  cleanup [目录] [保留天数]              # 清理过期日志"
            echo "  compress [目录] [天数]                 # 压缩日志文件"
            echo "  auto                                   # 自动维护"
            echo "  stats                                  # 统计信息"
            exit 1
            ;;
    esac
}

# 如果直接执行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
