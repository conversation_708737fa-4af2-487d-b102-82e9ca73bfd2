#!/bin/bash

# =============================================================================
# 数据恢复管理模块
# 提供数据库恢复、配置文件恢复、日志恢复、系统恢复等功能
# =============================================================================

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# 加载依赖
source "$SCRIPT_DIR/utils/common.sh"
source "$SCRIPT_DIR/utils/colors.sh"
source "$SCRIPT_DIR/config/services.conf"

# 备份目录
BACKUP_ROOT_DIR="$SCRIPT_DIR/backups"

# 数据库恢复
restore_database() {
    local backup_file="$1"
    
    if [[ -z "$backup_file" ]]; then
        echo_error "请指定备份文件"
        return 1
    fi
    
    # 如果只提供文件名，尝试在备份目录中查找
    if [[ ! -f "$backup_file" ]]; then
        local full_path="$BACKUP_ROOT_DIR/database/$backup_file"
        if [[ -f "$full_path" ]]; then
            backup_file="$full_path"
        else
            echo_error "备份文件不存在: $backup_file"
            return 1
        fi
    fi
    
    echo_title "数据库恢复"
    echo_info "备份文件: $backup_file"
    
    # 检查MySQL连接
    if ! mysql -h"$MYSQL_HOST" -P"$MYSQL_PORT" -u"$MYSQL_USER" -e "SELECT 1" >/dev/null 2>&1; then
        echo_error "❌ 无法连接到MySQL数据库"
        return 1
    fi
    
    # 检查备份文件
    local restore_file="$backup_file"
    if [[ "$backup_file" == *.gz ]]; then
        echo_progress "解压备份文件..."
        local temp_file=$(mktemp)
        if gunzip -c "$backup_file" > "$temp_file"; then
            restore_file="$temp_file"
            echo_success "✅ 备份文件解压完成"
        else
            echo_error "❌ 备份文件解压失败"
            rm -f "$temp_file"
            return 1
        fi
    fi
    
    # 确认恢复操作
    echo_warning "⚠️  警告: 此操作将覆盖现有数据库数据！"
    if ! ask_confirmation "确定要恢复数据库吗？"; then
        [[ "$restore_file" != "$backup_file" ]] && rm -f "$restore_file"
        echo_info "恢复操作已取消"
        return 0
    fi
    
    echo_progress "正在恢复数据库..."
    
    # 执行数据库恢复
    if mysql -h"$MYSQL_HOST" -P"$MYSQL_PORT" -u"$MYSQL_USER" < "$restore_file" 2>/dev/null; then
        echo_success "✅ 数据库恢复完成"
        log_system "INFO" "数据库恢复完成: $backup_file"
        
        # 清理临时文件
        [[ "$restore_file" != "$backup_file" ]] && rm -f "$restore_file"
        
        return 0
    else
        echo_error "❌ 数据库恢复失败"
        log_system "ERROR" "数据库恢复失败: $backup_file"
        
        # 清理临时文件
        [[ "$restore_file" != "$backup_file" ]] && rm -f "$restore_file"
        
        return 1
    fi
}

# 配置文件恢复
restore_config() {
    local backup_file="$1"
    
    if [[ -z "$backup_file" ]]; then
        echo_error "请指定备份文件"
        return 1
    fi
    
    # 如果只提供文件名，尝试在备份目录中查找
    if [[ ! -f "$backup_file" ]]; then
        local full_path="$BACKUP_ROOT_DIR/config/$backup_file"
        if [[ -f "$full_path" ]]; then
            backup_file="$full_path"
        else
            echo_error "备份文件不存在: $backup_file"
            return 1
        fi
    fi
    
    echo_title "配置文件恢复"
    echo_info "备份文件: $backup_file"
    
    # 检查备份文件
    if [[ ! "$backup_file" == *.tar.gz ]]; then
        echo_error "❌ 无效的配置备份文件格式"
        return 1
    fi
    
    # 创建临时目录
    local temp_dir=$(mktemp -d)
    
    echo_progress "解压备份文件..."
    if tar -xzf "$backup_file" -C "$temp_dir" 2>/dev/null; then
        echo_success "✅ 备份文件解压完成"
    else
        echo_error "❌ 备份文件解压失败"
        rm -rf "$temp_dir"
        return 1
    fi
    
    # 查找配置文件
    local config_backup_dir="$temp_dir/config_backup"
    if [[ ! -d "$config_backup_dir" ]]; then
        echo_error "❌ 备份文件中未找到配置目录"
        rm -rf "$temp_dir"
        return 1
    fi
    
    # 显示备份信息
    if [[ -f "$config_backup_dir/backup_info.txt" ]]; then
        echo_subtitle "备份信息"
        cat "$config_backup_dir/backup_info.txt"
        echo ""
    fi
    
    # 确认恢复操作
    echo_warning "⚠️  警告: 此操作将覆盖现有配置文件！"
    if ! ask_confirmation "确定要恢复配置文件吗？"; then
        rm -rf "$temp_dir"
        echo_info "恢复操作已取消"
        return 0
    fi
    
    echo_progress "正在恢复配置文件..."
    
    # 备份当前配置文件
    local backup_timestamp=$(date +%Y%m%d_%H%M%S)
    local current_backup_dir="$BACKUP_ROOT_DIR/config/current_backup_$backup_timestamp"
    mkdir -p "$current_backup_dir"
    
    # 恢复配置文件
    local restored_count=0
    local failed_count=0
    
    # 遍历备份中的配置文件
    find "$config_backup_dir" -type f ! -name "backup_info.txt" | while read -r backup_config; do
        local relative_path=$(realpath --relative-to="$config_backup_dir" "$backup_config" 2>/dev/null)
        local target_path="$PROJECT_ROOT/$relative_path"
        local target_dir=$(dirname "$target_path")
        
        # 备份当前文件
        if [[ -f "$target_path" ]]; then
            local current_backup_path="$current_backup_dir/$relative_path"
            mkdir -p "$(dirname "$current_backup_path")"
            cp "$target_path" "$current_backup_path" 2>/dev/null
        fi
        
        # 恢复文件
        mkdir -p "$target_dir"
        if cp "$backup_config" "$target_path" 2>/dev/null; then
            echo_success "✓ 已恢复: $relative_path"
            ((restored_count++))
        else
            echo_error "✗ 恢复失败: $relative_path"
            ((failed_count++))
        fi
    done
    
    # 清理临时目录
    rm -rf "$temp_dir"
    
    echo ""
    echo_success "✅ 配置文件恢复完成"
    show_key_value "恢复文件数" "$restored_count"
    show_key_value "失败文件数" "$failed_count"
    show_key_value "当前配置备份" "$current_backup_dir"
    
    log_system "INFO" "配置文件恢复完成: $backup_file (成功: $restored_count, 失败: $failed_count)"
    
    return $([[ $failed_count -eq 0 ]] && echo 0 || echo 1)
}

# 日志恢复
restore_logs() {
    local backup_file="$1"
    
    if [[ -z "$backup_file" ]]; then
        echo_error "请指定备份文件"
        return 1
    fi
    
    # 如果只提供文件名，尝试在备份目录中查找
    if [[ ! -f "$backup_file" ]]; then
        local full_path="$BACKUP_ROOT_DIR/logs/$backup_file"
        if [[ -f "$full_path" ]]; then
            backup_file="$full_path"
        else
            echo_error "备份文件不存在: $backup_file"
            return 1
        fi
    fi
    
    echo_title "日志文件恢复"
    echo_info "备份文件: $backup_file"
    
    # 检查备份文件
    if [[ ! "$backup_file" == *.tar.gz ]]; then
        echo_error "❌ 无效的日志备份文件格式"
        return 1
    fi
    
    # 创建临时目录
    local temp_dir=$(mktemp -d)
    
    echo_progress "解压备份文件..."
    if tar -xzf "$backup_file" -C "$temp_dir" 2>/dev/null; then
        echo_success "✅ 备份文件解压完成"
    else
        echo_error "❌ 备份文件解压失败"
        rm -rf "$temp_dir"
        return 1
    fi
    
    # 查找日志文件
    local logs_backup_dir="$temp_dir/logs_backup"
    if [[ ! -d "$logs_backup_dir" ]]; then
        echo_error "❌ 备份文件中未找到日志目录"
        rm -rf "$temp_dir"
        return 1
    fi
    
    # 显示备份信息
    if [[ -f "$logs_backup_dir/backup_info.txt" ]]; then
        echo_subtitle "备份信息"
        cat "$logs_backup_dir/backup_info.txt"
        echo ""
    fi
    
    # 确认恢复操作
    echo_warning "⚠️  注意: 此操作将恢复历史日志文件"
    if ! ask_confirmation "确定要恢复日志文件吗？"; then
        rm -rf "$temp_dir"
        echo_info "恢复操作已取消"
        return 0
    fi
    
    echo_progress "正在恢复日志文件..."
    
    # 创建恢复目录
    local restore_dir="$SCRIPT_DIR/logs/restored_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$restore_dir"
    
    # 恢复日志文件
    if cp -r "$logs_backup_dir"/* "$restore_dir/" 2>/dev/null; then
        local file_count=$(find "$restore_dir" -type f ! -name "backup_info.txt" | wc -l)
        
        echo_success "✅ 日志文件恢复完成"
        show_key_value "恢复目录" "$restore_dir"
        show_key_value "恢复文件数" "$file_count"
        
        log_system "INFO" "日志文件恢复完成: $backup_file -> $restore_dir"
        
        # 清理临时目录
        rm -rf "$temp_dir"
        
        return 0
    else
        echo_error "❌ 日志文件恢复失败"
        rm -rf "$temp_dir"
        rm -rf "$restore_dir"
        return 1
    fi
}

# 系统恢复
restore_system() {
    local backup_file="$1"
    
    if [[ -z "$backup_file" ]]; then
        echo_error "请指定备份文件"
        return 1
    fi
    
    # 如果只提供文件名，尝试在备份目录中查找
    if [[ ! -f "$backup_file" ]]; then
        local full_path="$BACKUP_ROOT_DIR/system/$backup_file"
        if [[ -f "$full_path" ]]; then
            backup_file="$full_path"
        else
            echo_error "备份文件不存在: $backup_file"
            return 1
        fi
    fi
    
    echo_title "系统文件恢复"
    echo_info "备份文件: $backup_file"
    
    echo_error "❌ 系统文件恢复功能暂未实现"
    echo_warning "⚠️  系统文件恢复需要谨慎操作，建议手动恢复"
    echo_info "您可以手动解压备份文件并选择性恢复需要的文件"
    
    return 1
}

# 列出可恢复的备份
list_restore_options() {
    local backup_type="${1:-all}"
    
    echo_title "可恢复的备份文件"
    
    case "$backup_type" in
        "database"|"db")
            echo_subtitle "数据库备份"
            if [[ -d "$BACKUP_ROOT_DIR/database" ]]; then
                find "$BACKUP_ROOT_DIR/database" -name "*.sql" -o -name "*.sql.gz" | sort -r | head -10 | while read -r file; do
                    local file_size=$(stat -c%s "$file" 2>/dev/null || stat -f%z "$file" 2>/dev/null || echo "0")
                    local file_time=$(stat -c%y "$file" 2>/dev/null | cut -d' ' -f1 || echo "未知")
                    echo_info "📄 $(basename "$file") ($(format_size $file_size), $file_time)"
                done
            else
                echo_warning "暂无数据库备份"
            fi
            ;;
        "config")
            echo_subtitle "配置文件备份"
            if [[ -d "$BACKUP_ROOT_DIR/config" ]]; then
                find "$BACKUP_ROOT_DIR/config" -name "*.tar.gz" | sort -r | head -10 | while read -r file; do
                    local file_size=$(stat -c%s "$file" 2>/dev/null || stat -f%z "$file" 2>/dev/null || echo "0")
                    local file_time=$(stat -c%y "$file" 2>/dev/null | cut -d' ' -f1 || echo "未知")
                    echo_info "📦 $(basename "$file") ($(format_size $file_size), $file_time)"
                done
            else
                echo_warning "暂无配置文件备份"
            fi
            ;;
        "logs")
            echo_subtitle "日志文件备份"
            if [[ -d "$BACKUP_ROOT_DIR/logs" ]]; then
                find "$BACKUP_ROOT_DIR/logs" -name "*.tar.gz" | sort -r | head -10 | while read -r file; do
                    local file_size=$(stat -c%s "$file" 2>/dev/null || stat -f%z "$file" 2>/dev/null || echo "0")
                    local file_time=$(stat -c%y "$file" 2>/dev/null | cut -d' ' -f1 || echo "未知")
                    echo_info "📋 $(basename "$file") ($(format_size $file_size), $file_time)"
                done
            else
                echo_warning "暂无日志文件备份"
            fi
            ;;
        "all")
            list_restore_options "database"
            echo ""
            list_restore_options "config"
            echo ""
            list_restore_options "logs"
            ;;
    esac
}

# 主函数
main() {
    case "${1:-help}" in
        "database"|"db")
            restore_database "$2"
            ;;
        "config")
            restore_config "$2"
            ;;
        "logs")
            restore_logs "$2"
            ;;
        "system")
            restore_system "$2"
            ;;
        "list")
            list_restore_options "$2"
            ;;
        *)
            echo_error "未知命令: $1"
            echo "用法: $0 {database|config|logs|system|list} [备份文件]"
            echo ""
            echo "恢复命令:"
            echo "  database <文件>           # 恢复数据库"
            echo "  config <文件>             # 恢复配置文件"
            echo "  logs <文件>               # 恢复日志文件"
            echo "  system <文件>             # 恢复系统文件"
            echo ""
            echo "查询命令:"
            echo "  list [类型]               # 列出可恢复的备份"
            echo ""
            echo "示例:"
            echo "  $0 list                   # 列出所有可恢复备份"
            echo "  $0 database backup.sql.gz # 恢复数据库"
            echo "  $0 config config.tar.gz  # 恢复配置文件"
            exit 1
            ;;
    esac
}

# 如果直接执行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
