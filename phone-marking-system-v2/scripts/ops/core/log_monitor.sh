#!/bin/bash

# =============================================================================
# 实时日志监控工具
# 提供实时日志监控、异常检测、告警通知等功能
# =============================================================================

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

# 加载依赖
source "$SCRIPT_DIR/utils/common.sh"
source "$SCRIPT_DIR/utils/colors.sh"
source "$SCRIPT_DIR/config/services.conf"
source "$SCRIPT_DIR/config/logging.conf"

# 监控状态
MONITOR_RUNNING=false
MONITOR_PID=""

# 错误计数器
declare -A ERROR_COUNTERS
declare -A WARNING_COUNTERS
declare -A LAST_ERROR_TIME

# 启动实时监控
start_monitor() {
    local services=("$@")
    
    if [[ ${#services[@]} -eq 0 ]]; then
        services=("${ALL_SERVICES[@]}")
    fi
    
    echo_title "启动实时日志监控"
    echo_info "监控服务: ${services[*]}"
    echo_info "按 Ctrl+C 停止监控"
    echo_separator
    
    MONITOR_RUNNING=true
    
    # 设置信号处理
    trap 'stop_monitor' INT TERM
    
    # 获取要监控的日志文件
    local log_files=()
    for service in "${services[@]}"; do
        local service_upper=$(to_upper "$service")
        local log_file_var="${service_upper}_LOG_FILE"
        local log_file="${!log_file_var}"
        
        if [[ -z "$log_file" ]]; then
            log_file="$SCRIPT_DIR/logs/services/${service}_$(date +%Y%m%d).log"
        fi
        
        if [[ -f "$log_file" ]]; then
            log_files+=("$log_file")
            echo_info "监控文件: $log_file"
        else
            echo_warning "日志文件不存在: $log_file"
        fi
    done
    
    if [[ ${#log_files[@]} -eq 0 ]]; then
        echo_error "没有找到可监控的日志文件"
        return 1
    fi
    
    echo ""
    
    # 使用tail -f监控多个文件
    if command_exists multitail; then
        # 使用multitail进行多文件监控
        multitail "${log_files[@]}" | while IFS= read -r line; do
            process_log_line "$line"
        done
    else
        # 使用tail -f的简单版本
        tail -f "${log_files[@]}" | while IFS= read -r line; do
            process_log_line "$line"
        done
    fi
}

# 处理日志行
process_log_line() {
    local line="$1"
    local timestamp=$(date '+%H:%M:%S')
    
    # 提取服务名（从文件路径或日志内容）
    local service="UNKNOWN"
    if [[ "$line" =~ \[([A-Z_]+)\] ]]; then
        service="${BASH_REMATCH[1]}"
    fi
    
    # 检测日志级别和内容
    if [[ "$line" =~ (ERROR|CRITICAL|FATAL) ]]; then
        handle_error_log "$service" "$line" "$timestamp"
    elif [[ "$line" =~ (WARN|WARNING) ]]; then
        handle_warning_log "$service" "$line" "$timestamp"
    elif [[ "$line" =~ (INFO) ]]; then
        handle_info_log "$service" "$line" "$timestamp"
    else
        handle_debug_log "$service" "$line" "$timestamp"
    fi
    
    # 检测特殊模式
    detect_patterns "$service" "$line" "$timestamp"
}

# 处理错误日志
handle_error_log() {
    local service="$1"
    local line="$2"
    local timestamp="$3"
    
    # 增加错误计数
    ERROR_COUNTERS["$service"]=$((${ERROR_COUNTERS["$service"]:-0} + 1))
    LAST_ERROR_TIME["$service"]="$timestamp"
    
    # 显示错误日志
    echo_color "$RED" "[$timestamp] 🔴 ERROR [$service] $line"
    
    # 检查错误频率
    check_error_frequency "$service"
    
    # 记录到错误日志
    echo "[$timestamp] ERROR [$service] $line" >> "$SCRIPT_DIR/logs/errors/errors_$(date +%Y%m%d).log"
}

# 处理警告日志
handle_warning_log() {
    local service="$1"
    local line="$2"
    local timestamp="$3"
    
    # 增加警告计数
    WARNING_COUNTERS["$service"]=$((${WARNING_COUNTERS["$service"]:-0} + 1))
    
    # 显示警告日志
    echo_color "$YELLOW" "[$timestamp] 🟡 WARN [$service] $line"
}

# 处理信息日志
handle_info_log() {
    local service="$1"
    local line="$2"
    local timestamp="$3"
    
    # 只显示重要的信息日志
    if [[ "$line" =~ (started|stopped|connected|disconnected|failed|success) ]]; then
        echo_color "$GREEN" "[$timestamp] 🟢 INFO [$service] $line"
    fi
}

# 处理调试日志
handle_debug_log() {
    local service="$1"
    local line="$2"
    local timestamp="$3"
    
    # 调试日志通常不显示，除非在调试模式
    if [[ "$DEBUG_MODE_ENABLED" == "true" ]]; then
        echo_color "$GRAY" "[$timestamp] 🔍 DEBUG [$service] $line"
    fi
}

# 检测特殊模式
detect_patterns() {
    local service="$1"
    local line="$2"
    local timestamp="$3"
    
    # 检测性能问题
    if [[ "$line" =~ ([0-9]+)ms ]] && [[ ${BASH_REMATCH[1]} -gt ${SLOW_LOG_THRESHOLD_MS:-1000} ]]; then
        echo_color "$PURPLE" "[$timestamp] 🐌 SLOW [$service] 响应时间: ${BASH_REMATCH[1]}ms"
        send_alert "性能告警" "服务 $service 响应时间过长: ${BASH_REMATCH[1]}ms"
    fi
    
    # 检测安全问题
    for pattern in "${SUSPICIOUS_ACTIVITY_PATTERNS[@]}"; do
        if [[ "$line" =~ $pattern ]]; then
            echo_color "$RED" "[$timestamp] 🚨 SECURITY [$service] 检测到可疑活动: $pattern"
            send_alert "安全告警" "服务 $service 检测到可疑活动: $pattern"
            
            # 记录到安全日志
            echo "[$timestamp] SECURITY [$service] $line" >> "$SCRIPT_DIR/logs/security/security_$(date +%Y%m%d).log"
            break
        fi
    done
    
    # 检测数据库连接问题
    if [[ "$line" =~ (connection.*failed|database.*error|mysql.*error) ]]; then
        echo_color "$RED" "[$timestamp] 🗄️  DATABASE [$service] 数据库连接问题"
        send_alert "数据库告警" "服务 $service 数据库连接问题"
    fi
    
    # 检测内存问题
    if [[ "$line" =~ (out of memory|memory.*error|oom) ]]; then
        echo_color "$RED" "[$timestamp] 🧠 MEMORY [$service] 内存问题"
        send_alert "内存告警" "服务 $service 内存不足"
    fi
}

# 检查错误频率
check_error_frequency() {
    local service="$1"
    local error_count=${ERROR_COUNTERS["$service"]:-0}
    local threshold=${ERROR_THRESHOLD_PER_MINUTE:-10}
    
    # 简单的频率检查（这里可以实现更复杂的时间窗口逻辑）
    if [[ $error_count -gt $threshold ]]; then
        echo_color "$RED" "⚠️  警告: 服务 $service 错误频率过高 ($error_count 个错误)"
        send_alert "错误频率告警" "服务 $service 错误频率过高: $error_count 个错误"
        
        # 重置计数器
        ERROR_COUNTERS["$service"]=0
    fi
}

# 发送告警
send_alert() {
    local title="$1"
    local message="$2"
    local timestamp=$(get_timestamp)
    
    # 记录告警
    echo "[$timestamp] ALERT: $title - $message" >> "$SCRIPT_DIR/logs/system/alerts_$(date +%Y%m%d).log"
    
    # 邮件告警
    if [[ "$EMAIL_ALERT_ENABLED" == "true" ]] && [[ -n "$EMAIL_TO" ]]; then
        send_email_alert "$title" "$message"
    fi
    
    # Webhook告警
    if [[ "$WEBHOOK_ALERT_ENABLED" == "true" ]] && [[ -n "$WEBHOOK_URL" ]]; then
        send_webhook_alert "$title" "$message"
    fi
    
    # 短信告警
    if [[ "$SMS_ALERT_ENABLED" == "true" ]] && [[ -n "$SMS_PHONE_NUMBERS" ]]; then
        send_sms_alert "$title" "$message"
    fi
}

# 发送邮件告警
send_email_alert() {
    local title="$1"
    local message="$2"
    
    if command_exists mail; then
        echo "$message" | mail -s "$title" "$EMAIL_TO"
    elif command_exists sendmail; then
        {
            echo "To: $EMAIL_TO"
            echo "Subject: $title"
            echo ""
            echo "$message"
        } | sendmail "$EMAIL_TO"
    fi
}

# 发送Webhook告警
send_webhook_alert() {
    local title="$1"
    local message="$2"
    
    if command_exists curl; then
        local payload=$(cat << EOF
{
    "title": "$title",
    "message": "$message",
    "timestamp": "$(get_timestamp)",
    "severity": "warning"
}
EOF
)
        
        curl -X POST \
             -H "Content-Type: application/json" \
             -d "$payload" \
             --max-time "${WEBHOOK_TIMEOUT:-30}" \
             "$WEBHOOK_URL" >/dev/null 2>&1
    fi
}

# 发送短信告警
send_sms_alert() {
    local title="$1"
    local message="$2"
    
    # 这里需要根据具体的短信API实现
    if [[ -n "$SMS_API_URL" ]] && [[ -n "$SMS_API_KEY" ]]; then
        # 示例实现
        local sms_message="$title: $message"
        
        if command_exists curl; then
            curl -X POST \
                 -H "Authorization: Bearer $SMS_API_KEY" \
                 -H "Content-Type: application/json" \
                 -d "{\"to\":\"$SMS_PHONE_NUMBERS\",\"message\":\"$sms_message\"}" \
                 "$SMS_API_URL" >/dev/null 2>&1
        fi
    fi
}

# 停止监控
stop_monitor() {
    echo ""
    echo_warning "停止日志监控..."
    
    MONITOR_RUNNING=false
    
    # 显示监控统计
    echo_subtitle "监控统计"
    
    for service in "${!ERROR_COUNTERS[@]}"; do
        local errors=${ERROR_COUNTERS["$service"]}
        local warnings=${WARNING_COUNTERS["$service"]:-0}
        local last_error=${LAST_ERROR_TIME["$service"]:-"无"}
        
        echo_table_row "$service" "错误: $errors" "警告: $warnings" "最后错误: $last_error"
    done
    
    echo_info "监控已停止"
    exit 0
}

# 监控仪表板
monitor_dashboard() {
    local refresh_interval="${1:-5}"
    
    echo_title "日志监控仪表板"
    echo_info "刷新间隔: ${refresh_interval}秒"
    echo_info "按 Ctrl+C 退出"
    
    while true; do
        clear
        echo_title "日志监控仪表板 - $(date '+%Y-%m-%d %H:%M:%S')"
        
        # 显示系统状态
        echo_subtitle "系统状态"
        show_system_overview
        
        echo ""
        
        # 显示最近的错误
        echo_subtitle "最近错误 (最近5分钟)"
        local error_log="$SCRIPT_DIR/logs/errors/errors_$(date +%Y%m%d).log"
        if [[ -f "$error_log" ]]; then
            local recent_errors=$(tail -10 "$error_log" 2>/dev/null | head -5)
            if [[ -n "$recent_errors" ]]; then
                echo "$recent_errors" | while IFS= read -r line; do
                    echo_color "$RED" "$line"
                done
            else
                echo_success "✅ 无最近错误"
            fi
        else
            echo_info "暂无错误日志"
        fi
        
        echo ""
        
        # 显示服务状态
        echo_subtitle "服务状态"
        for service in "${ALL_SERVICES[@]}"; do
            if is_service_running "$service"; then
                echo_status "running" "$service"
            else
                echo_status "stopped" "$service"
            fi
        done
        
        sleep "$refresh_interval"
    done
}

# 日志统计监控
monitor_statistics() {
    local interval="${1:-60}"
    
    echo_title "日志统计监控"
    echo_info "统计间隔: ${interval}秒"
    
    while true; do
        local timestamp=$(get_timestamp)
        
        # 统计各服务的日志数量
        for service in "${ALL_SERVICES[@]}"; do
            local service_upper=$(to_upper "$service")
            local log_file_var="${service_upper}_LOG_FILE"
            local log_file="${!log_file_var}"
            
            if [[ -f "$log_file" ]]; then
                local total_lines=$(wc -l < "$log_file" 2>/dev/null || echo "0")
                local error_lines=$(grep -c "ERROR\|CRITICAL\|FATAL" "$log_file" 2>/dev/null || echo "0")
                local warning_lines=$(grep -c "WARN\|WARNING" "$log_file" 2>/dev/null || echo "0")
                
                # 记录统计数据
                echo "$timestamp,$service,$total_lines,$error_lines,$warning_lines" >> "$SCRIPT_DIR/logs/performance/log_stats_$(date +%Y%m%d).csv"
            fi
        done
        
        echo_info "[$timestamp] 日志统计已记录"
        sleep "$interval"
    done
}

# 主函数
main() {
    case "${1:-help}" in
        "start")
            shift
            start_monitor "$@"
            ;;
        "dashboard")
            monitor_dashboard "$2"
            ;;
        "stats")
            monitor_statistics "$2"
            ;;
        "stop")
            if [[ -n "$MONITOR_PID" ]]; then
                kill "$MONITOR_PID" 2>/dev/null
                echo_success "监控已停止"
            else
                echo_warning "没有运行中的监控进程"
            fi
            ;;
        *)
            echo_error "未知命令: $1"
            echo "用法: $0 {start|dashboard|stats|stop} [参数...]"
            echo ""
            echo "命令说明:"
            echo "  start [服务名...]              # 启动实时日志监控"
            echo "  dashboard [刷新间隔]           # 显示监控仪表板"
            echo "  stats [统计间隔]               # 启动统计监控"
            echo "  stop                           # 停止监控"
            echo ""
            echo "示例:"
            echo "  $0 start mysql redis           # 监控MySQL和Redis日志"
            echo "  $0 dashboard 10                # 显示仪表板，10秒刷新"
            echo "  $0 stats 30                    # 每30秒统计一次"
            exit 1
            ;;
    esac
}

# 如果直接执行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
