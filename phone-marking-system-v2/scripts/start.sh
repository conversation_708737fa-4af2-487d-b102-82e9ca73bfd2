#!/bin/bash

# 电话号码标记识别系统 V2 - 启动脚本
# Phone Marking System V2 - Start Script

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# 显示横幅
show_banner() {
    echo -e "${CYAN}"
    cat << "EOF"
╔══════════════════════════════════════════════════════════════════════════════╗
║                     电话号码标记识别系统 V2                                   ║
║                  Phone Number Marking System V2                             ║
║                                                                              ║
║  🚀 版本: v2.0.0                                                            ║
║  🏗️ 架构: 微服务架构                                                         ║
║  💻 技术栈: Python + FastAPI + SQLite + Vue.js                             ║
║  📁 结构: 模块化 + 标准化                                                     ║
╚══════════════════════════════════════════════════════════════════════════════╝
EOF
    echo -e "${NC}"
}

# 检查Python环境
check_python() {
    log_info "检查Python环境..."
    
    if ! command -v python3 &> /dev/null; then
        log_error "Python3 未安装，请先安装Python 3.8+"
        exit 1
    fi
    
    PYTHON_VERSION=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
    log_info "Python版本: $PYTHON_VERSION"
    
    # 检查Python版本是否满足要求
    if python3 -c 'import sys; exit(0 if sys.version_info >= (3, 8) else 1)'; then
        log_success "✓ Python版本满足要求"
    else
        log_error "Python版本过低，需要Python 3.8+"
        exit 1
    fi
}

# 检查项目结构
check_project_structure() {
    log_info "检查项目结构..."
    
    REQUIRED_DIRS=(
        "src"
        "src/core"
        "src/services"
        "src/web"
        "config"
        "data"
        "tests"
        "docs"
        "logs"
    )
    
    for dir in "${REQUIRED_DIRS[@]}"; do
        if [[ -d "$dir" ]]; then
            log_success "✓ 目录存在: $dir"
        else
            log_error "✗ 目录缺失: $dir"
            exit 1
        fi
    done
}

# 检查配置文件
check_config_files() {
    log_info "检查配置文件..."
    
    CONFIG_FILES=(
        "config/config.json"
        "config/brand_configs.json"
        "config/nlp_config.json"
    )
    
    for config_file in "${CONFIG_FILES[@]}"; do
        if [[ -f "$config_file" ]]; then
            log_success "✓ 配置文件存在: $config_file"
        else
            log_warn "⚠ 配置文件缺失: $config_file"
        fi
    done
}

# 安装依赖
install_dependencies() {
    log_info "检查并安装依赖..."
    
    if [[ -f "requirements.txt" ]]; then
        log_info "安装Python依赖包..."
        pip install --upgrade pip
        pip install -r requirements.txt
        log_success "✓ 依赖安装完成"
    else
        log_warn "未找到requirements.txt文件"
    fi
}

# 创建必要目录
create_directories() {
    log_info "创建必要目录..."
    
    DIRECTORIES=(
        "data/databases"
        "data/uploads"
        "data/exports"
        "data/temp"
        "logs/system"
        "logs/services"
        "logs/access"
    )
    
    for dir in "${DIRECTORIES[@]}"; do
        if [[ ! -d "$dir" ]]; then
            mkdir -p "$dir"
            log_success "✓ 创建目录: $dir"
        fi
    done
}

# 检查端口占用
check_ports() {
    log_info "检查端口占用..."
    
    PORTS=(8000 8001 8002 8003 8080)
    OCCUPIED_PORTS=()
    
    for port in "${PORTS[@]}"; do
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            OCCUPIED_PORTS+=($port)
            log_warn "⚠ 端口 $port 已被占用"
        else
            log_success "✓ 端口 $port 可用"
        fi
    done
    
    if [[ ${#OCCUPIED_PORTS[@]} -gt 0 ]]; then
        log_warn "发现占用端口: ${OCCUPIED_PORTS[*]}"
        read -p "是否继续启动？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "启动已取消"
            exit 1
        fi
    fi
}

# 初始化数据库
init_database() {
    log_info "初始化数据库..."
    
    # 检查数据库文件是否存在
    if [[ -f "data/databases/phone_marks.db" ]]; then
        log_success "✓ 主数据库已存在"
    else
        log_info "创建主数据库..."
        # 这里可以添加数据库初始化逻辑
        touch "data/databases/phone_marks.db"
        log_success "✓ 主数据库创建完成"
    fi
}

# 启动系统
start_system() {
    log_info "启动系统..."
    
    case "${1:-interactive}" in
        "daemon")
            log_info "以守护进程模式启动..."
            nohup python3 main.py > logs/system/startup.log 2>&1 &
            echo $! > .pid
            log_success "✓ 系统已在后台启动，PID: $(cat .pid)"
            ;;
        "interactive")
            log_info "以交互模式启动..."
            python3 main.py
            ;;
        "services")
            log_info "启动所有微服务..."
            python3 src/start_microservices.py
            ;;
        "web")
            log_info "仅启动Web管理系统..."
            python3 -m src.services.admin.web_admin_system
            ;;
        *)
            log_error "未知的启动模式: $1"
            show_help
            exit 1
            ;;
    esac
}

# 显示服务信息
show_service_info() {
    echo ""
    log_info "🌐 服务访问地址:"
    echo -e "${GREEN}  📊 Web管理系统: ${BLUE}http://127.0.0.1:8080${NC}"
    echo -e "${GREEN}  🌐 API网关:     ${BLUE}http://127.0.0.1:8000${NC}"
    echo -e "${GREEN}  📖 API文档:     ${BLUE}http://127.0.0.1:8000/docs${NC}"
    echo -e "${GREEN}  📍 归属地服务:  ${BLUE}http://127.0.0.1:8001${NC}"
    echo -e "${GREEN}  🧠 NLP服务:     ${BLUE}http://127.0.0.1:8002${NC}"
    echo -e "${GREEN}  📦 批量处理:    ${BLUE}http://127.0.0.1:8003${NC}"
    echo ""
    echo -e "${YELLOW}👤 默认管理员账户:${NC}"
    echo -e "${GREEN}  用户名: ${BLUE}admin${NC}"
    echo -e "${GREEN}  密码:   ${BLUE}admin123${NC}"
    echo ""
    echo -e "${PURPLE}📁 项目结构:${NC}"
    echo -e "${GREEN}  源代码:   ${BLUE}src/${NC}"
    echo -e "${GREEN}  配置文件: ${BLUE}config/${NC}"
    echo -e "${GREEN}  数据目录: ${BLUE}data/${NC}"
    echo -e "${GREEN}  测试目录: ${BLUE}tests/${NC}"
    echo -e "${GREEN}  文档目录: ${BLUE}docs/${NC}"
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 等待服务启动
    sleep 3
    
    SERVICES=(
        "http://127.0.0.1:8080"
        "http://127.0.0.1:8000"
        "http://127.0.0.1:8001"
        "http://127.0.0.1:8002"
        "http://127.0.0.1:8003"
    )
    
    for service in "${SERVICES[@]}"; do
        if curl -s "$service" > /dev/null 2>&1; then
            log_success "✓ 服务健康: $service"
        else
            log_warn "⚠ 服务异常: $service"
        fi
    done
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "启动模式:"
    echo "  interactive    交互模式启动 (默认)"
    echo "  daemon         守护进程模式启动"
    echo "  services       启动所有微服务"
    echo "  web            仅启动Web管理系统"
    echo ""
    echo "其他选项:"
    echo "  --help, -h     显示此帮助信息"
    echo "  --check        仅执行环境检查"
    echo "  --install      仅安装依赖"
    echo "  --health       执行健康检查"
    echo "  --info         显示服务信息"
    echo ""
    echo "示例:"
    echo "  $0                    # 交互模式启动"
    echo "  $0 daemon             # 守护进程模式启动"
    echo "  $0 services           # 启动所有微服务"
    echo "  $0 web                # 仅启动Web系统"
    echo "  $0 --check            # 环境检查"
    echo "  $0 --install          # 安装依赖"
}

# 主函数
main() {
    # 显示横幅
    show_banner
    
    # 解析参数
    case "${1:-interactive}" in
        "--help"|"-h")
            show_help
            exit 0
            ;;
        "--check")
            check_python
            check_project_structure
            check_config_files
            create_directories
            check_ports
            log_success "✅ 环境检查完成"
            exit 0
            ;;
        "--install")
            check_python
            install_dependencies
            log_success "✅ 依赖安装完成"
            exit 0
            ;;
        "--health")
            health_check
            exit 0
            ;;
        "--info")
            show_service_info
            exit 0
            ;;
        "interactive"|"daemon"|"services"|"web")
            # 执行完整的启动流程
            check_python
            check_project_structure
            check_config_files
            install_dependencies
            create_directories
            check_ports
            init_database
            
            # 启动系统
            start_system "$1"
            
            # 如果是守护进程模式，执行健康检查
            if [[ "$1" == "daemon" ]]; then
                health_check
                show_service_info
                log_success "✅ 系统启动完成"
            elif [[ "$1" == "interactive" ]]; then
                show_service_info
            fi
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
}

# 错误处理
trap 'log_error "启动过程中发生错误，请检查日志"; exit 1' ERR

# 执行主函数
main "$@"
