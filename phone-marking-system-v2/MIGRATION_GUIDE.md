# 项目迁移指南

## 📋 迁移概述

本指南帮助您从旧的混乱项目结构迁移到新的规范化项目结构 `phone-marking-system-v2`。

## 🔄 迁移前后对比

### 旧项目结构问题
- ❌ 文件散乱，缺乏分类
- ❌ 没有标准的Python包结构
- ❌ 配置文件混杂在代码中
- ❌ 测试文件分散
- ❌ 缺乏文档组织
- ❌ 数据库文件和代码混在一起

### 新项目结构优势
- ✅ 按功能模块清晰分类
- ✅ 标准Python包结构
- ✅ 配置文件统一管理
- ✅ 完整的测试体系
- ✅ 规范的文档组织
- ✅ 数据与代码分离

## 📁 文件迁移映射表

| 旧文件位置 | 新文件位置 | 说明 |
|------------|------------|------|
| `location_microservice.py` | `src/services/location/location_microservice.py` | 归属地微服务 |
| `nlp_microservice.py` | `src/services/nlp/nlp_microservice.py` | NLP微服务 |
| `api_gateway.py` | `src/services/gateway/api_gateway.py` | API网关 |
| `batch_processing_service.py` | `src/services/batch/batch_processing_service.py` | 批量处理服务 |
| `web_admin_system.py` | `src/services/admin/web_admin_system.py` | Web管理系统 |
| `db_manager.py` | `src/core/database/db_manager.py` | 数据库管理器 |
| `enhanced_cache.py` | `src/core/cache/enhanced_cache.py` | 增强缓存 |
| `service_monitor.py` | `src/core/monitoring/service_monitor.py` | 服务监控 |
| `main_controller.py` | `src/main_controller.py` | 主控制器 |
| `config.json` | `config/config.json` | 主配置文件 |
| `*.db` | `data/databases/*.db` | 数据库文件 |
| `*.xlsx` | `data/exports/*.xlsx` | 导出文件 |
| `test_*.py` | `tests/unit/test_*.py` | 单元测试 |
| `*.md` | `docs/*.md` | 文档文件 |

## 🚀 迁移步骤

### 1. 环境准备
```bash
# 进入新项目目录
cd phone-marking-system-v2

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置文件更新
```bash
# 检查配置文件
ls config/

# 根据需要更新配置
vim config/config.json
```

### 3. 数据库迁移
```bash
# 检查数据库文件
ls data/databases/

# 如果需要，运行数据库升级脚本
python src/core/database/upgrade_database.py
```

### 4. 导入路径更新

#### 旧的导入方式
```python
# 旧项目中的导入
from location_microservice import LocationMicroservice
from db_manager import DatabaseManager
from enhanced_cache import EnhancedCache
```

#### 新的导入方式
```python
# 新项目中的导入
from src.services.location.location_microservice import LocationMicroservice
from src.core.database.db_manager import DatabaseManager
from src.core.cache.enhanced_cache import EnhancedCache
```

### 5. 配置路径更新

#### 旧的配置路径
```python
# 旧项目中的配置路径
config_path = "config.json"
nlp_config = "nlp_config.json"
```

#### 新的配置路径
```python
# 新项目中的配置路径
config_path = "config/config.json"
nlp_config = "config/nlp_config.json"
```

### 6. 数据库路径更新

#### 旧的数据库路径
```python
# 旧项目中的数据库路径
db_path = "phone_marks.db"
cache_db = "cache_data.db"
```

#### 新的数据库路径
```python
# 新项目中的数据库路径
db_path = "data/databases/phone_marks.db"
cache_db = "data/databases/cache_data.db"
```

## 🔧 代码修改指南

### 1. 主程序入口修改

#### 旧的启动方式
```bash
python main_controller.py
```

#### 新的启动方式
```bash
python main.py
# 或者
python src/main_controller.py
```

### 2. 服务启动修改

#### 旧的服务启动
```python
# 直接运行服务文件
python location_microservice.py
python nlp_microservice.py
```

#### 新的服务启动
```python
# 通过主控制器启动
python main.py start
# 或者单独启动服务
python -m src.services.location.location_microservice
```

### 3. 测试运行修改

#### 旧的测试运行
```bash
python test_location_microservice.py
```

#### 新的测试运行
```bash
# 运行单元测试
python -m pytest tests/unit/test_location_microservice.py

# 运行所有测试
python -m pytest tests/
```

## 📝 配置文件迁移

### 1. 主配置文件
将原来的 `config.json` 移动到 `config/config.json`，并根据需要更新路径：

```json
{
  "database": {
    "path": "data/databases/phone_marks.db"
  },
  "cache": {
    "path": "data/databases/cache_data.db"
  },
  "logs": {
    "path": "logs/system/"
  }
}
```

### 2. 服务配置文件
各个微服务的配置文件都移动到 `config/` 目录下：
- `nlp_config.json` → `config/nlp_config.json`
- `brand_configs.json` → `config/brand_configs.json`
- `scheduler_config.json` → `config/scheduler_config.json`

## 🧪 测试迁移验证

### 1. 运行单元测试
```bash
cd phone-marking-system-v2
python -m pytest tests/unit/ -v
```

### 2. 运行集成测试
```bash
python -m pytest tests/integration/ -v
```

### 3. 运行性能测试
```bash
python -m pytest tests/performance/ -v
```

## 📊 迁移检查清单

### ✅ 文件结构检查
- [ ] 所有源代码文件已移动到 `src/` 目录
- [ ] 配置文件已移动到 `config/` 目录
- [ ] 数据库文件已移动到 `data/databases/` 目录
- [ ] 测试文件已移动到 `tests/` 目录
- [ ] 文档文件已移动到 `docs/` 目录

### ✅ 代码修改检查
- [ ] 导入路径已更新
- [ ] 配置文件路径已更新
- [ ] 数据库路径已更新
- [ ] 日志路径已更新

### ✅ 功能验证检查
- [ ] 主程序可以正常启动
- [ ] 各个微服务可以正常运行
- [ ] 数据库连接正常
- [ ] 缓存系统工作正常
- [ ] Web界面可以访问

### ✅ 测试验证检查
- [ ] 单元测试通过
- [ ] 集成测试通过
- [ ] 性能测试通过
- [ ] 端到端测试通过

## 🚨 常见问题解决

### 1. 导入错误
**问题**: `ModuleNotFoundError: No module named 'xxx'`

**解决**: 
```python
# 在主程序开头添加路径
import sys
from pathlib import Path
PROJECT_ROOT = Path(__file__).parent
sys.path.insert(0, str(PROJECT_ROOT))
```

### 2. 配置文件找不到
**问题**: `FileNotFoundError: config.json`

**解决**: 更新配置文件路径
```python
config_path = "config/config.json"
```

### 3. 数据库连接失败
**问题**: `sqlite3.OperationalError: unable to open database file`

**解决**: 更新数据库路径并确保目录存在
```python
db_path = "data/databases/phone_marks.db"
Path(db_path).parent.mkdir(parents=True, exist_ok=True)
```

### 4. 权限问题
**问题**: `PermissionError: [Errno 13] Permission denied`

**解决**: 检查文件权限
```bash
chmod +x scripts/*.sh
chmod 755 data/
```

## 🎯 迁移后的优势

### 1. 开发效率提升
- 模块化开发，职责清晰
- 标准化结构，易于理解
- 完整的测试体系

### 2. 维护成本降低
- 代码组织清晰，易于定位问题
- 配置集中管理，便于修改
- 日志分类存储，便于调试

### 3. 扩展能力增强
- 微服务架构，便于横向扩展
- 插件化设计，便于功能扩展
- 标准接口，便于集成

### 4. 团队协作改善
- 标准化结构，降低学习成本
- 模块化开发，减少冲突
- 完整文档，便于交接

## 📞 技术支持

如果在迁移过程中遇到问题，可以：

1. 查看 `docs/` 目录下的相关文档
2. 运行测试验证功能是否正常
3. 检查 `logs/` 目录下的日志文件
4. 参考 `REORGANIZATION_SUMMARY.json` 了解迁移详情

迁移完成后，您将拥有一个结构清晰、易于维护和扩展的现代化项目！
