"""
测试归属地功能集成
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from location_manager import LocationManager
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_location_manager():
    """测试归属地管理器"""
    print("=== 测试归属地查询功能 ===")
    
    lm = LocationManager()
    
    # 测试号码列表
    test_numbers = [
        '13800138000',  # 北京移动
        '15912345678',  # 云南移动
        '02088888888',  # 广州固话
        '01012345678',  # 北京固话
        '057188888888', # 杭州固话
        '18612345678',  # 手机号
        '400123456',    # 400号码
        '95588',        # 短号
    ]
    
    for phone in test_numbers:
        print(f"\n测试号码: {phone}")
        result = lm.get_phone_location(phone)
        print(f"结果: {result}")
        print(f"归属地: {result.get('province', '')} {result.get('city', '')} {result.get('isp', '')}")
    
    # 显示统计信息
    stats = lm.get_statistics()
    print(f"\n=== 数据库统计 ===")
    print(f"手机号段记录: {stats.get('mobile_records', 0):,}")
    print(f"固话区号记录: {stats.get('landline_records', 0):,}")
    print(f"缓存记录: {stats.get('cache_records', 0):,}")

def test_ml_kit_integration():
    """测试ML Kit集成"""
    print("\n=== 测试ML Kit集成 ===")
    
    # 模拟ML Kit的归属地获取功能
    try:
        # 这里我们只测试归属地查询部分，不实际运行ML Kit
        from ml_kit_phone_marker import MLKitPhoneMarker
        
        # 创建实例但不连接设备
        marker = MLKitPhoneMarker()
        
        # 测试综合归属地信息获取
        test_phone = '13800138000'
        ml_results = []  # 空的ML结果
        
        location_info = marker._get_comprehensive_location_info(test_phone, ml_results)
        print(f"号码 {test_phone} 的归属地信息:")
        print(f"省份: {location_info.get('province', '')}")
        print(f"城市: {location_info.get('city', '')}")
        print(f"运营商: {location_info.get('isp', '')}")
        print(f"数据源: {location_info.get('source', '')}")
        
    except Exception as e:
        print(f"ML Kit集成测试失败: {e}")

if __name__ == '__main__':
    test_location_manager()
    test_ml_kit_integration()
