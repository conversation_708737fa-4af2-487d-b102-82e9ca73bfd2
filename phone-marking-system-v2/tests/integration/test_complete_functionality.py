"""
完整功能测试脚本
测试归属地功能的完整集成
"""

import sys
import os
import sqlite3
import pandas as pd
from datetime import datetime

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_database_integration():
    """测试数据库集成"""
    print("=== 测试数据库集成 ===")
    
    # 模拟一些测试记录
    test_records = [
        {
            'phone_number': '13800138000',
            'mark_info': '骚扰电话',
            'province': '北京',
            'city': '北京',
            'location': '北京北京',
            'isp': '中国移动',
            'location_source': 'database',
            'phone_model': 'Test Phone',
            'android_version': '10',
            'status': 'success',
            'processing_time': 2.5,
            'method': 'ML Kit'
        },
        {
            'phone_number': '02088888888',
            'mark_info': '推销电话',
            'province': '广东',
            'city': '广州',
            'location': '广东广州',
            'isp': '',
            'location_source': 'database',
            'phone_model': 'Test Phone',
            'android_version': '10',
            'status': 'success',
            'processing_time': 1.8,
            'method': 'ML Kit'
        }
    ]
    
    # 保存测试记录到数据库
    try:
        conn = sqlite3.connect('phone_marks.db')
        cursor = conn.cursor()
        
        for record in test_records:
            cursor.execute('''
                INSERT INTO dial_records 
                (phone_number, mark_info, province, city, location, isp, location_source, 
                 phone_model, android_version, status, processing_time, method)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                record['phone_number'],
                record['mark_info'],
                record['province'],
                record['city'],
                record['location'],
                record['isp'],
                record['location_source'],
                record['phone_model'],
                record['android_version'],
                record['status'],
                record['processing_time'],
                record['method']
            ))
        
        conn.commit()
        conn.close()
        print("✓ 测试记录保存成功")
        
    except Exception as e:
        print(f"✗ 数据库保存失败: {e}")
        return False
    
    return True

def test_export_functionality():
    """测试导出功能"""
    print("\n=== 测试导出功能 ===")
    
    try:
        # 从数据库读取数据
        conn = sqlite3.connect('phone_marks.db')
        
        # 查询最近的记录
        df = pd.read_sql_query('''
            SELECT phone_number, mark_info, province, city, location, isp, location_source,
                   phone_model, android_version, status, processing_time, method, dial_time
            FROM dial_records 
            ORDER BY dial_time DESC 
            LIMIT 10
        ''', conn)
        
        conn.close()
        
        if df.empty:
            print("✗ 没有找到测试数据")
            return False
        
        # 格式化处理时间
        df['processing_time_formatted'] = df['processing_time'].apply(
            lambda x: f"{x:.2f}秒" if pd.notnull(x) else "未知"
        )
        
        # 定义导出列
        export_columns = [
            'phone_number',
            'mark_info', 
            'province',
            'city',
            'location',
            'isp',
            'location_source',
            'phone_model',
            'android_version',
            'status',
            'processing_time_formatted',
            'method',
            'dial_time'
        ]
        
        # 检查并添加缺失的列
        for col in ['isp', 'location_source']:
            if col not in df.columns:
                df[col] = ''
        
        df_export = df[export_columns].copy()
        
        # 导出到Excel
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"test_phone_mark_results_{timestamp}.xlsx"
        df_export.to_excel(filename, index=False, engine='openpyxl')
        
        print(f"✓ 成功导出到Excel文件: {filename}")
        print(f"✓ 导出记录数: {len(df_export)}")
        
        # 显示导出的数据样本
        print("\n导出数据样本:")
        for _, row in df_export.head(3).iterrows():
            print(f"号码: {row['phone_number']}")
            print(f"标记: {row['mark_info']}")
            print(f"归属地: {row['province']} {row['city']}")
            print(f"运营商: {row['isp']}")
            print(f"数据源: {row['location_source']}")
            print("---")
        
        return True
        
    except Exception as e:
        print(f"✗ 导出功能测试失败: {e}")
        return False

def test_location_query_performance():
    """测试归属地查询性能"""
    print("\n=== 测试归属地查询性能 ===")
    
    try:
        from location_manager import LocationManager
        import time
        
        lm = LocationManager()
        
        # 测试号码列表
        test_numbers = [
            '13800138000', '15912345678', '18612345678',  # 手机号
            '02088888888', '01012345678', '057188888888'   # 固话
        ]
        
        total_time = 0
        success_count = 0
        
        for phone in test_numbers:
            start_time = time.time()
            result = lm.get_phone_location(phone)
            end_time = time.time()
            
            query_time = (end_time - start_time) * 1000  # 毫秒
            total_time += query_time
            
            if result.get('province') and result.get('city'):
                success_count += 1
                print(f"✓ {phone}: {result['province']} {result['city']} ({query_time:.2f}ms)")
            else:
                print(f"✗ {phone}: 未找到归属地信息 ({query_time:.2f}ms)")
        
        avg_time = total_time / len(test_numbers)
        success_rate = (success_count / len(test_numbers)) * 100
        
        print(f"\n性能统计:")
        print(f"平均查询时间: {avg_time:.2f}ms")
        print(f"查询成功率: {success_rate:.1f}%")
        print(f"总查询时间: {total_time:.2f}ms")
        
        # 性能要求检查
        if avg_time < 5.0:  # 平均查询时间小于5ms
            print("✓ 查询性能良好")
        else:
            print("⚠ 查询性能需要优化")
        
        return success_rate >= 80  # 成功率要求80%以上
        
    except Exception as e:
        print(f"✗ 性能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始完整功能测试...")
    
    test_results = []
    
    # 1. 数据库集成测试
    test_results.append(test_database_integration())
    
    # 2. 导出功能测试
    test_results.append(test_export_functionality())
    
    # 3. 性能测试
    test_results.append(test_location_query_performance())
    
    # 总结测试结果
    print(f"\n=== 测试结果总结 ===")
    passed_tests = sum(test_results)
    total_tests = len(test_results)
    
    print(f"通过测试: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！归属地功能集成成功！")
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
    
    return passed_tests == total_tests

if __name__ == '__main__':
    main()
