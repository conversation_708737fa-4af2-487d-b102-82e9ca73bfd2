"""
测试优化后的导出格式
"""

import sys
import os
import sqlite3
import pandas as pd
from datetime import datetime

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_export_without_location():
    """测试移除location字段后的导出功能"""
    print("=== 测试优化后的导出格式 ===")
    
    try:
        # 从数据库读取数据
        conn = sqlite3.connect('phone_marks.db')
        
        # 查询最近的记录
        df = pd.read_sql_query('''
            SELECT phone_number, mark_info, province, city, location, isp, location_source,
                   phone_model, android_version, status, processing_time, method, dial_time
            FROM dial_records 
            ORDER BY dial_time DESC 
            LIMIT 5
        ''', conn)
        
        conn.close()
        
        if df.empty:
            print("✗ 没有找到测试数据")
            return False
        
        # 格式化处理时间
        df['processing_time_formatted'] = df['processing_time'].apply(
            lambda x: f"{x:.2f}秒" if pd.notnull(x) else "未知"
        )
        
        # 定义导出列（移除location字段）
        export_columns = [
            'phone_number',
            'mark_info',
            'province',
            'city',
            'isp',
            'location_source',
            'phone_model',
            'android_version',
            'status',
            'processing_time_formatted',
            'method',
            'dial_time'
        ]
        
        # 检查并添加缺失的列
        for col in ['isp', 'location_source']:
            if col not in df.columns:
                df[col] = ''
        
        df_export = df[export_columns].copy()
        
        # 导出到Excel
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"optimized_export_test_{timestamp}.xlsx"
        df_export.to_excel(filename, index=False, engine='openpyxl')
        
        print(f"✓ 成功导出到Excel文件: {filename}")
        print(f"✓ 导出记录数: {len(df_export)}")
        print(f"✓ 导出字段数: {len(export_columns)}")
        
        # 显示导出的列名
        print(f"\n导出字段列表:")
        for i, col in enumerate(export_columns, 1):
            print(f"{i:2d}. {col}")
        
        # 显示导出的数据样本
        print(f"\n导出数据样本:")
        for _, row in df_export.head(3).iterrows():
            print(f"号码: {row['phone_number']}")
            print(f"标记: {row['mark_info']}")
            print(f"归属地: {row['province']} {row['city']}")
            print(f"运营商: {row['isp']}")
            print(f"数据源: {row['location_source']}")
            print("---")
        
        # 验证location字段确实被移除
        if 'location' not in df_export.columns:
            print("✓ location字段已成功移除")
        else:
            print("✗ location字段仍然存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 导出格式测试失败: {e}")
        return False

def compare_old_vs_new():
    """对比优化前后的字段差异"""
    print(f"\n=== 字段对比 ===")
    
    old_columns = [
        'phone_number', 'mark_info', 'province', 'city', 'location',
        'isp', 'location_source', 'phone_model', 'android_version', 
        'status', 'processing_time_formatted', 'method', 'dial_time'
    ]
    
    new_columns = [
        'phone_number', 'mark_info', 'province', 'city',
        'isp', 'location_source', 'phone_model', 'android_version',
        'status', 'processing_time_formatted', 'method', 'dial_time'
    ]
    
    print(f"优化前字段数: {len(old_columns)}")
    print(f"优化后字段数: {len(new_columns)}")
    print(f"减少字段数: {len(old_columns) - len(new_columns)}")
    
    removed_fields = set(old_columns) - set(new_columns)
    print(f"移除的字段: {list(removed_fields)}")
    
    print(f"\n✓ 优化效果:")
    print(f"• 移除了冗余的location字段")
    print(f"• 保留了province和city字段提供完整归属地信息")
    print(f"• 导出文件更简洁，避免信息重复")

def main():
    """主测试函数"""
    print("开始测试优化后的导出格式...\n")
    
    # 测试导出功能
    export_success = test_export_without_location()
    
    # 对比字段变化
    compare_old_vs_new()
    
    if export_success:
        print(f"\n🎉 导出格式优化成功！")
        print(f"✅ location字段已移除，导出更简洁")
    else:
        print(f"\n⚠️ 导出格式测试失败")
    
    return export_success

if __name__ == '__main__':
    main()
