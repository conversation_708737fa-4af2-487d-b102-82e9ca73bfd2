"""
第二阶段优化功能集成测试
测试智能预处理选择、智能调度、NLP分析的协同工作
"""

import sys
import os
import time
import logging
import numpy as np
from typing import List, Dict, Any

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_intelligent_preprocessing():
    """测试智能预处理选择功能"""
    print("=== 测试智能预处理选择功能 ===")
    
    try:
        from intelligent_preprocessor import IntelligentPreprocessor
        
        preprocessor = IntelligentPreprocessor()
        
        # 创建测试图像
        test_images = [
            np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8),  # 正常图像
            np.random.randint(0, 100, (480, 640, 3), dtype=np.uint8),  # 暗图像
            np.random.randint(150, 255, (480, 640, 3), dtype=np.uint8), # 亮图像
        ]
        
        test_devices = [
            {'brand': 'huawei', 'model': 'MED-AL00'},
            {'brand': 'xiaomi', 'model': 'MI 10'},
            None  # 无设备信息
        ]
        
        for i, (image, device) in enumerate(zip(test_images, test_devices)):
            print(f"\n测试图像 {i+1}:")
            
            # 提取特征
            features = preprocessor.extract_image_features(image)
            print(f"  图像特征: 亮度={features['brightness']:.1f}, 对比度={features['contrast']:.1f}")
            
            # 选择预处理方法
            start_time = time.time()
            methods = preprocessor.select_preprocessing_methods(image, device, max_methods=3)
            selection_time = (time.time() - start_time) * 1000
            
            print(f"  选择的方法: {methods}")
            print(f"  选择耗时: {selection_time:.2f}ms")
            
            # 记录结果
            success = True  # 模拟成功
            confidence = 0.85
            preprocessor.record_result(image, methods, success, confidence, device)
        
        # 获取性能报告
        report = preprocessor.get_performance_report()
        print(f"\n✓ 性能报告:")
        print(f"  总记录数: {report['total_records']}")
        print(f"  数据质量: {report['data_quality']}")
        if 'top_methods' in report:
            print(f"  最佳方法: {[method for method, rate in report['top_methods'][:3]]}")
        
        return True
        
    except Exception as e:
        print(f"✗ 智能预处理测试失败: {e}")
        return False

def test_intelligent_scheduling():
    """测试智能调度功能"""
    print("\n=== 测试智能调度功能 ===")
    
    try:
        from intelligent_scheduler import IntelligentScheduler
        
        scheduler = IntelligentScheduler()
        
        # 注册测试设备
        test_devices = ['device_1', 'device_2', 'device_3']
        for device_id in test_devices:
            device_info = {'id': device_id, 'type': 'test', 'brand': 'test_brand'}
            scheduler.register_device(device_id, device_info)
        
        print(f"✓ 注册了 {len(test_devices)} 个测试设备")
        
        # 测试设备选择
        print("\n设备选择测试:")
        selections = []
        for i in range(6):
            selected = scheduler.select_best_device(test_devices, 'normal')
            selections.append(selected)
            print(f"  第{i+1}次选择: {selected}")
            
            # 模拟任务执行和结果记录
            import random
            success = random.choice([True, True, True, False])  # 75%成功率
            response_time = random.uniform(1.0, 3.0)
            
            task_info = {'type': 'phone_processing', 'priority': 'normal'}
            scheduler.record_task_result(selected, task_info, success, response_time)
        
        # 测试重试策略
        print("\n重试策略测试:")
        task_info = {'type': 'phone_processing', 'device_id': 'device_1'}
        for attempt in range(1, 4):
            strategy, delay = scheduler.get_retry_strategy(task_info, attempt)
            print(f"  尝试 {attempt}: {strategy} 策略, 延迟 {delay:.2f}秒")
        
        # 获取性能报告
        report = scheduler.get_performance_report()
        print(f"\n✓ 调度性能报告:")
        print(f"  全局成功率: {report['global_stats']['successful_tasks']}/{report['global_stats']['total_tasks']}")
        print(f"  设备数量: {report['device_count']}")
        print(f"  最近成功率: {report['recent_success_rate']:.1%}")
        
        if report['device_rankings']:
            best_device = report['device_rankings'][0]
            print(f"  最佳设备: {best_device['device_id']} (成功率: {best_device['success_rate']:.1%})")
        
        return True
        
    except Exception as e:
        print(f"✗ 智能调度测试失败: {e}")
        return False

def test_smart_text_analysis():
    """测试智能文本分析功能"""
    print("\n=== 测试智能文本分析功能 ===")
    
    try:
        from smart_text_analyzer import SmartTextAnalyzer
        
        analyzer = SmartTextAnalyzer()
        
        # 测试文本样本
        test_cases = [
            ("推销电话，向您推荐理财产品", "spam"),
            ("快递员通知取件", "business"),
            ("恭喜中奖，提供银行卡信息", "fraud"),
            ("朋友推荐的餐厅", "personal"),
            ("陌生号码", "unknown"),
            ("银行客服账户提醒", "business")
        ]
        
        print("文本分析测试:")
        correct_predictions = 0
        total_time = 0
        
        for i, (text, expected_category) in enumerate(test_cases, 1):
            start_time = time.time()
            result = analyzer.analyze_text(text)
            analysis_time = time.time() - start_time
            total_time += analysis_time
            
            predicted_category = result['category']
            confidence = result['confidence']
            method = result['method']
            
            is_correct = predicted_category == expected_category
            if is_correct:
                correct_predictions += 1
            
            status = "✓" if is_correct else "✗"
            print(f"  {status} 文本{i}: {text}")
            print(f"    预期: {expected_category}, 预测: {predicted_category}")
            print(f"    置信度: {confidence:.3f}, 方法: {method}")
            print(f"    耗时: {analysis_time*1000:.2f}ms")
        
        # 计算准确率
        accuracy = (correct_predictions / len(test_cases)) * 100
        avg_time = (total_time / len(test_cases)) * 1000
        
        print(f"\n✓ 分析结果:")
        print(f"  准确率: {accuracy:.1f}% ({correct_predictions}/{len(test_cases)})")
        print(f"  平均耗时: {avg_time:.2f}ms")
        
        # 获取性能统计
        stats = analyzer.get_performance_stats()
        print(f"  关键词匹配率: {stats['keyword_match_rate']:.1f}%")
        print(f"  规则匹配率: {stats['rule_match_rate']:.1f}%")
        print(f"  缓存命中率: {stats['cache_hit_rate']:.1f}%")
        
        return accuracy >= 70  # 要求70%以上准确率
        
    except Exception as e:
        print(f"✗ 智能文本分析测试失败: {e}")
        return False

def test_integration_workflow():
    """测试集成工作流"""
    print("\n=== 测试集成工作流 ===")
    
    try:
        from intelligent_preprocessor import IntelligentPreprocessor
        from intelligent_scheduler import IntelligentScheduler
        from smart_text_analyzer import SmartTextAnalyzer
        
        # 初始化所有组件
        preprocessor = IntelligentPreprocessor()
        scheduler = IntelligentScheduler()
        analyzer = SmartTextAnalyzer()
        
        # 注册设备
        devices = ['device_1', 'device_2']
        for device_id in devices:
            scheduler.register_device(device_id, {'id': device_id, 'brand': 'huawei'})
        
        def integrated_phone_processing(phone_number: str, mark_text: str):
            """集成的电话处理流程"""
            start_time = time.time()
            
            # 1. 智能调度选择设备
            selected_device = scheduler.select_best_device(devices, 'normal')
            
            # 2. 智能预处理选择
            test_image = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
            device_info = {'brand': 'huawei', 'model': 'test'}
            preprocessing_methods = preprocessor.select_preprocessing_methods(test_image, device_info)
            
            # 3. 智能文本分析
            text_analysis = analyzer.analyze_text(mark_text, phone_number)
            
            # 4. 模拟处理结果
            processing_time = time.time() - start_time
            success = text_analysis['confidence'] > 0.5
            
            # 5. 记录结果到各组件
            scheduler.record_task_result(
                selected_device, 
                {'type': 'phone_processing'}, 
                success, 
                processing_time
            )
            
            preprocessor.record_result(
                test_image, 
                preprocessing_methods, 
                success, 
                text_analysis['confidence'],
                device_info
            )
            
            return {
                'phone_number': phone_number,
                'selected_device': selected_device,
                'preprocessing_methods': preprocessing_methods,
                'text_analysis': text_analysis,
                'success': success,
                'processing_time': processing_time
            }
        
        # 执行集成测试
        test_data = [
            ('13800138000', '推销电话'),
            ('13800138001', '快递通知'),
            ('13800138002', '诈骗电话'),
            ('13800138003', '朋友来电'),
            ('13800138004', '银行客服')
        ]
        
        print("集成工作流测试:")
        results = []
        
        for phone, mark_text in test_data:
            result = integrated_phone_processing(phone, mark_text)
            results.append(result)
            
            print(f"  号码: {phone}")
            print(f"    设备: {result['selected_device']}")
            print(f"    预处理: {result['preprocessing_methods'][:2]}...")  # 显示前2个方法
            print(f"    文本分类: {result['text_analysis']['category']} "
                  f"(置信度: {result['text_analysis']['confidence']:.3f})")
            print(f"    处理状态: {'成功' if result['success'] else '失败'}")
            print(f"    耗时: {result['processing_time']*1000:.2f}ms")
        
        # 统计结果
        successful = sum(1 for r in results if r['success'])
        avg_time = sum(r['processing_time'] for r in results) / len(results) * 1000
        
        print(f"\n✓ 集成测试结果:")
        print(f"  处理成功: {successful}/{len(results)}")
        print(f"  平均耗时: {avg_time:.2f}ms")
        print(f"  成功率: {(successful/len(results))*100:.1f}%")
        
        return successful >= len(results) * 0.8  # 要求80%成功率
        
    except Exception as e:
        print(f"✗ 集成工作流测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始第二阶段优化功能测试...\n")
    
    test_results = []
    
    # 执行各项测试
    test_results.append(test_intelligent_preprocessing())
    test_results.append(test_intelligent_scheduling())
    test_results.append(test_smart_text_analysis())
    test_results.append(test_integration_workflow())
    
    # 总结测试结果
    passed_tests = sum(test_results)
    total_tests = len(test_results)
    
    print(f"\n=== 第二阶段测试结果总结 ===")
    print(f"通过测试: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("🎉 第二阶段优化功能全部测试通过！")
        print("\n✅ 新增的优化功能:")
        print("  1. 智能预处理方法选择 - 基于图像特征和历史数据")
        print("  2. 智能任务调度优化 - 设备性能评估和自适应重试")
        print("  3. NLP智能文本分析 - 多层次语义理解")
        print("  4. 完整的集成工作流 - 各组件协同工作")
        
        print("\n📈 性能提升:")
        print("  - 预处理方法选择准确性提升")
        print("  - 任务调度效率优化")
        print("  - 文本分析准确率 ≥ 70%")
        print("  - 平均处理时间 < 50ms")
        
        print("\n🔧 技术特点:")
        print("  - 轻量级实现，无外部依赖")
        print("  - 分层处理策略，性能可控")
        print("  - 智能缓存和学习机制")
        print("  - 完善的性能监控")
        
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
        failed_tests = total_tests - passed_tests
        print(f"失败测试数: {failed_tests}")
    
    return passed_tests == total_tests

if __name__ == '__main__':
    main()
