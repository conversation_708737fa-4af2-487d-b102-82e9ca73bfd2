#!/usr/bin/env python3
"""
Dash-FastAPI-Admin 集成测试脚本
测试新的管理后台系统是否可以正常运行
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
PROJECT_ROOT = Path(__file__).parent
sys.path.insert(0, str(PROJECT_ROOT))
sys.path.insert(0, str(PROJECT_ROOT / 'src'))
sys.path.insert(0, str(PROJECT_ROOT / 'config'))

def check_dependencies():
    """检查依赖包是否安装"""
    required_packages = [
        'dash',
        'feffery_antd_components', 
        'feffery_utils_components',
        'plotly',
        'pandas',
        'pymysql',
        'sqlalchemy'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} - 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} - 未安装")
    
    if missing_packages:
        print(f"\n⚠️  缺少以下依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("\n✅ 所有依赖包检查通过!")
    return True

def test_database_connection():
    """测试数据库连接"""
    try:
        from config.database_config import test_database_connection, init_database
        
        print("\n🔧 测试数据库连接...")
        
        # 尝试初始化数据库
        if init_database():
            print("✅ 数据库初始化成功")
            
            # 测试连接
            if test_database_connection():
                print("✅ 数据库连接测试通过")
                return True
            else:
                print("❌ 数据库连接测试失败")
                return False
        else:
            print("❌ 数据库初始化失败")
            return False
            
    except Exception as e:
        print(f"❌ 数据库测试异常: {e}")
        print("💡 提示: 请确保MySQL服务已启动，用户名为root，密码为空")
        return False

def test_dash_app():
    """测试Dash应用"""
    try:
        print("\n🚀 启动Dash管理后台...")
        
        # 导入Dash应用
        from src.web.dash_admin_demo import app
        
        print("✅ Dash应用加载成功")
        print("🌐 启动Web服务器...")
        print("📱 访问地址: http://127.0.0.1:8088")
        print("👤 默认账号: admin / admin123")
        print("\n🔥 系统功能预览:")
        print("   📊 仪表板 - 系统概览和统计")
        print("   📁 数据管理 - 批量导入和导出")
        print("   📈 统计分析 - 数据分析和报表")
        print("   📱 设备管理 - 手机设备监控")
        print("   ⏰ 任务监控 - 批量任务进度")
        print("\n按 Ctrl+C 停止服务")
        
        # 启动应用
        app.run_server(
            debug=True,
            host="127.0.0.1", 
            port=8088,
            dev_tools_hot_reload=True
        )
        
    except KeyboardInterrupt:
        print("\n\n👋 服务已停止")
        return True
    except Exception as e:
        print(f"❌ Dash应用启动失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 Dash-FastAPI-Admin 集成测试")
    print("=" * 50)
    
    # 1. 检查依赖
    if not check_dependencies():
        print("\n❌ 依赖检查失败，请安装缺少的包后重试")
        return False
    
    # 2. 测试数据库（可选）
    print("\n" + "=" * 50)
    db_ok = test_database_connection()
    if not db_ok:
        print("⚠️  数据库连接失败，但可以继续测试Dash界面（使用模拟数据）")
        input("按回车键继续...")
    
    # 3. 启动Dash应用
    print("\n" + "=" * 50)
    test_dash_app()
    
    return True

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 测试已取消")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        sys.exit(1)
