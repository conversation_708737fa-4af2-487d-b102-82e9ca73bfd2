"""
数据库配置文件
支持MySQL数据库连接配置（root用户，空密码）
"""

import os
from typing import Dict, Any
from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import pymysql

# 安装 PyMySQL 作为 MySQLdb 的替代
pymysql.install_as_MySQLdb()

class DatabaseConfig:
    """数据库配置类"""
    
    def __init__(self):
        # 数据库连接配置
        self.DB_HOST = os.getenv("DB_HOST", "localhost")
        self.DB_PORT = int(os.getenv("DB_PORT", "3306"))
        self.DB_USER = os.getenv("DB_USER", "root")
        self.DB_PASSWORD = os.getenv("DB_PASSWORD", "")  # 空密码
        self.DB_NAME = os.getenv("DB_NAME", "phone_marking_system")
        
        # 构建数据库URL
        self.DATABASE_URL = f"mysql+pymysql://{self.DB_USER}:{self.DB_PASSWORD}@{self.DB_HOST}:{self.DB_PORT}/{self.DB_NAME}?charset=utf8mb4"
        
        # SQLAlchemy配置
        self.engine = None
        self.SessionLocal = None
        self.Base = declarative_base()
        
    def init_database(self):
        """初始化数据库连接"""
        try:
            # 创建数据库引擎
            self.engine = create_engine(
                self.DATABASE_URL,
                pool_pre_ping=True,
                pool_recycle=300,
                pool_size=10,
                max_overflow=20,
                echo=False  # 设置为True可以看到SQL语句
            )
            
            # 创建会话工厂
            self.SessionLocal = sessionmaker(
                autocommit=False,
                autoflush=False,
                bind=self.engine
            )
            
            # 测试连接
            with self.engine.connect() as conn:
                conn.execute("SELECT 1")
                print(f"✅ 数据库连接成功: {self.DB_HOST}:{self.DB_PORT}/{self.DB_NAME}")
                
            return True
            
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            return False
    
    def create_database_if_not_exists(self):
        """如果数据库不存在则创建"""
        try:
            # 连接到MySQL服务器（不指定数据库）
            server_url = f"mysql+pymysql://{self.DB_USER}:{self.DB_PASSWORD}@{self.DB_HOST}:{self.DB_PORT}/?charset=utf8mb4"
            server_engine = create_engine(server_url)
            
            with server_engine.connect() as conn:
                # 检查数据库是否存在
                result = conn.execute(f"SHOW DATABASES LIKE '{self.DB_NAME}'")
                if not result.fetchone():
                    # 创建数据库
                    conn.execute(f"CREATE DATABASE {self.DB_NAME} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
                    print(f"✅ 数据库 {self.DB_NAME} 创建成功")
                else:
                    print(f"✅ 数据库 {self.DB_NAME} 已存在")
                    
            server_engine.dispose()
            return True
            
        except Exception as e:
            print(f"❌ 创建数据库失败: {e}")
            return False
    
    def get_session(self):
        """获取数据库会话"""
        if self.SessionLocal is None:
            raise Exception("数据库未初始化，请先调用 init_database()")
        return self.SessionLocal()
    
    def create_tables(self):
        """创建所有表"""
        try:
            # 导入所有模型
            from .models import *  # 这里需要导入所有的模型类
            
            # 创建所有表
            self.Base.metadata.create_all(bind=self.engine)
            print("✅ 数据库表创建成功")
            return True
            
        except Exception as e:
            print(f"❌ 创建数据库表失败: {e}")
            return False

# 全局数据库配置实例
db_config = DatabaseConfig()

# 依赖注入函数，用于FastAPI
def get_db():
    """获取数据库会话的依赖注入函数"""
    db = db_config.get_session()
    try:
        yield db
    finally:
        db.close()

# 数据库初始化函数
def init_database():
    """初始化数据库的便捷函数"""
    print("🔧 开始初始化数据库...")
    
    # 1. 创建数据库（如果不存在）
    if not db_config.create_database_if_not_exists():
        return False
    
    # 2. 初始化数据库连接
    if not db_config.init_database():
        return False
    
    # 3. 创建表结构
    if not db_config.create_tables():
        return False
    
    print("🎉 数据库初始化完成！")
    return True

# 数据库连接测试函数
def test_database_connection():
    """测试数据库连接"""
    try:
        if db_config.engine is None:
            db_config.init_database()
        
        with db_config.engine.connect() as conn:
            result = conn.execute("SELECT VERSION() as version")
            version = result.fetchone()
            print(f"✅ MySQL版本: {version[0]}")
            
            # 测试数据库操作
            result = conn.execute(f"SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema = '{db_config.DB_NAME}'")
            table_count = result.fetchone()
            print(f"✅ 数据库表数量: {table_count[0]}")
            
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接测试失败: {e}")
        return False

# 环境配置
class Config:
    """应用配置类"""
    
    # 数据库配置
    DATABASE_URL = db_config.DATABASE_URL
    
    # Redis配置
    REDIS_HOST = os.getenv("REDIS_HOST", "localhost")
    REDIS_PORT = int(os.getenv("REDIS_PORT", "6379"))
    REDIS_DB = int(os.getenv("REDIS_DB", "0"))
    REDIS_PASSWORD = os.getenv("REDIS_PASSWORD", "")
    REDIS_URL = f"redis://:{REDIS_PASSWORD}@{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}" if REDIS_PASSWORD else f"redis://{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}"
    
    # JWT配置
    SECRET_KEY = os.getenv("SECRET_KEY", "your-secret-key-change-in-production")
    ALGORITHM = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES = 30
    
    # 应用配置
    DEBUG = os.getenv("DEBUG", "True").lower() == "true"
    HOST = os.getenv("HOST", "127.0.0.1")
    PORT = int(os.getenv("PORT", "8080"))
    
    # 文件上传配置
    UPLOAD_DIR = os.getenv("UPLOAD_DIR", "uploads")
    MAX_FILE_SIZE = int(os.getenv("MAX_FILE_SIZE", "50")) * 1024 * 1024  # 50MB
    
    # 日志配置
    LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
    LOG_FILE = os.getenv("LOG_FILE", "logs/app.log")

# 创建配置实例
config = Config()

if __name__ == "__main__":
    # 测试数据库配置
    print("🧪 测试数据库配置...")
    
    # 初始化数据库
    if init_database():
        # 测试连接
        test_database_connection()
    else:
        print("❌ 数据库初始化失败")
