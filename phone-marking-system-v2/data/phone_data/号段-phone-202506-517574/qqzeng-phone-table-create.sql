
-- qqzeng-phone  2025    
-- 开发参考 创建表以及导入数据库
-- https://www.qqzeng.com/article/dev.html

-- MySQL

CREATE TABLE `phone_location` (
  `prefix` VARCHAR(45) DEFAULT NULL COMMENT '号码前缀（如 138）',
  `phone` VARCHAR(45) NOT NULL COMMENT '号段',
  `province` VARCHAR(45) DEFAULT NULL COMMENT '省份',
  `city` VARCHAR(45) DEFAULT NULL COMMENT '城市',
  `isp` VARCHAR(45) DEFAULT NULL COMMENT '运营商（如 中国移动）',
  `tel_code` VARCHAR(45) DEFAULT '+86' COMMENT '国际电话代码',
  `postal_code` VARCHAR(45) DEFAULT NULL COMMENT '邮政编码',
  `area_code` VARCHAR(45) DEFAULT NULL COMMENT '行政区代码（如 440300）'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='电话号码归属地表';


-- PgSQL

CREATE TABLE "public"."phone_location" ( 
  "prefix" TEXT NULL,
  "phone" TEXT NOT NULL,
  "province" TEXT NULL,
  "city" TEXT NULL,
  "isp" TEXT NULL,
  "tel_code" TEXT NULL,
  "postal_code" TEXT NULL,
  "area_code" TEXT NULL
);



-- SQL Server

CREATE TABLE dbo.phone_location (
  prefix NVARCHAR(45) NULL,
  phone NVARCHAR(45) NOT NULL,
  province NVARCHAR(45) NULL,
  city NVARCHAR(45) NULL,
  isp NVARCHAR(45) NULL,
  tel_code NVARCHAR(45)  NULL,
  postal_code NVARCHAR(45) NULL,
  area_code NVARCHAR(45) NULL
);


-- Oracle

CREATE TABLE phone_location (
  prefix NVARCHAR2(45) DEFAULT NULL,
  phone NVARCHAR2(45) NOT NULL,
  province NVARCHAR2(45) DEFAULT NULL,
  city NVARCHAR2(45) DEFAULT NULL,
  isp NVARCHAR2(45) DEFAULT NULL,
  tel_code NVARCHAR2(45) DEFAULT NULL,
  postal_code NVARCHAR2(45) DEFAULT NULL,
  area_code NVARCHAR2(45) DEFAULT NULL
);

