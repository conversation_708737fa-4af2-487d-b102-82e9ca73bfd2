# 电话号码标记识别系统 - 项目结构

## 📁 项目目录结构

```
phone-marking-system/
├── README.md                           # 项目说明文档
├── requirements.txt                    # Python依赖包
├── setup.py                           # 项目安装配置
├── config/                            # 配置文件目录
│   ├── system_config.json             # 系统主配置
│   ├── database_config.json           # 数据库配置
│   ├── services_config.json           # 微服务配置
│   └── logging_config.json            # 日志配置
├── src/                               # 源代码目录
│   ├── __init__.py
│   ├── main.py                        # 主程序入口
│   ├── core/                          # 核心模块
│   │   ├── __init__.py
│   │   ├── database/                  # 数据库模块
│   │   │   ├── __init__.py
│   │   │   ├── sqlite_manager.py      # SQLite管理器
│   │   │   ├── mysql_manager.py       # MySQL管理器
│   │   │   └── models.py              # 数据模型
│   │   ├── cache/                     # 缓存模块
│   │   │   ├── __init__.py
│   │   │   ├── enhanced_cache.py      # 增强缓存
│   │   │   └── redis_cache.py         # Redis缓存
│   │   ├── monitoring/                # 监控模块
│   │   │   ├── __init__.py
│   │   │   ├── service_monitor.py     # 服务监控
│   │   │   ├── distributed_tracing.py # 分布式追踪
│   │   │   └── metrics.py             # 指标收集
│   │   └── utils/                     # 工具模块
│   │       ├── __init__.py
│   │       ├── logger.py              # 日志工具
│   │       ├── config_loader.py       # 配置加载器
│   │       └── validators.py          # 验证器
│   ├── services/                      # 微服务模块
│   │   ├── __init__.py
│   │   ├── location/                  # 归属地服务
│   │   │   ├── __init__.py
│   │   │   ├── service.py             # 服务主体
│   │   │   ├── handlers.py            # 请求处理器
│   │   │   └── models.py              # 数据模型
│   │   ├── nlp/                       # NLP分析服务
│   │   │   ├── __init__.py
│   │   │   ├── service.py
│   │   │   ├── analyzer.py            # 文本分析器
│   │   │   └── models.py
│   │   ├── gateway/                   # API网关
│   │   │   ├── __init__.py
│   │   │   ├── service.py
│   │   │   ├── router.py              # 路由管理
│   │   │   └── middleware.py          # 中间件
│   │   ├── batch/                     # 批量处理服务
│   │   │   ├── __init__.py
│   │   │   ├── service.py
│   │   │   ├── processor.py           # 批量处理器
│   │   │   └── file_handler.py        # 文件处理器
│   │   └── admin/                     # 管理服务
│   │       ├── __init__.py
│   │       ├── service.py
│   │       ├── auth.py                # 认证模块
│   │       └── rbac.py                # 权限控制
│   └── web/                           # Web前端
│       ├── __init__.py
│       ├── app.py                     # Flask/FastAPI应用
│       ├── static/                    # 静态资源
│       │   ├── css/                   # 样式文件
│       │   │   ├── bootstrap.min.css
│       │   │   ├── admin.css          # 管理界面样式
│       │   │   └── dashboard.css      # 仪表板样式
│       │   ├── js/                    # JavaScript文件
│       │   │   ├── jquery.min.js
│       │   │   ├── bootstrap.min.js
│       │   │   ├── chart.js           # 图表库
│       │   │   ├── admin.js           # 管理界面脚本
│       │   │   └── dashboard.js       # 仪表板脚本
│       │   ├── images/                # 图片资源
│       │   │   ├── logo.png
│       │   │   └── icons/
│       │   └── fonts/                 # 字体文件
│       ├── templates/                 # 模板文件
│       │   ├── base.html              # 基础模板
│       │   ├── login.html             # 登录页面
│       │   ├── dashboard.html         # 仪表板
│       │   ├── data_import.html       # 数据导入
│       │   ├── data_export.html       # 数据导出
│       │   ├── device_manage.html     # 设备管理
│       │   ├── user_manage.html       # 用户管理
│       │   ├── system_logs.html       # 系统日志
│       │   └── statistics.html        # 统计分析
│       └── api/                       # API接口
│           ├── __init__.py
│           ├── auth.py                # 认证接口
│           ├── data.py                # 数据接口
│           ├── device.py              # 设备接口
│           └── system.py              # 系统接口
├── data/                              # 数据目录
│   ├── databases/                     # 数据库文件
│   │   ├── main.db                    # 主数据库
│   │   ├── cache.db                   # 缓存数据库
│   │   └── logs.db                    # 日志数据库
│   ├── uploads/                       # 上传文件
│   ├── exports/                       # 导出文件
│   └── temp/                          # 临时文件
├── logs/                              # 日志目录
│   ├── system/                        # 系统日志
│   ├── services/                      # 服务日志
│   └── access/                        # 访问日志
├── tests/                             # 测试目录
│   ├── __init__.py
│   ├── unit/                          # 单元测试
│   │   ├── test_database.py
│   │   ├── test_cache.py
│   │   └── test_services.py
│   ├── integration/                   # 集成测试
│   │   ├── test_api.py
│   │   └── test_workflow.py
│   └── performance/                   # 性能测试
│       └── test_load.py
├── docs/                              # 文档目录
│   ├── api/                           # API文档
│   ├── deployment/                    # 部署文档
│   ├── user_guide/                    # 用户指南
│   └── development/                   # 开发文档
├── scripts/                           # 脚本目录
│   ├── install.sh                     # 安装脚本
│   ├── start.sh                       # 启动脚本
│   ├── stop.sh                        # 停止脚本
│   └── backup.sh                      # 备份脚本
└── docker/                            # Docker配置
    ├── Dockerfile
    ├── docker-compose.yml
    └── nginx.conf
```

## 📋 文件分类说明

### 🔧 核心模块 (src/core/)
- **database/**: 数据库相关功能
- **cache/**: 缓存系统
- **monitoring/**: 监控和追踪
- **utils/**: 通用工具

### 🚀 微服务 (src/services/)
- **location/**: 归属地查询服务
- **nlp/**: NLP文本分析服务
- **gateway/**: API网关服务
- **batch/**: 批量处理服务
- **admin/**: 管理服务

### 🌐 Web前端 (src/web/)
- **static/**: 静态资源文件
- **templates/**: HTML模板
- **api/**: Web API接口

### 📊 数据存储 (data/)
- **databases/**: 数据库文件
- **uploads/**: 用户上传文件
- **exports/**: 导出结果文件

### 📝 配置文件 (config/)
- **system_config.json**: 系统主配置
- **database_config.json**: 数据库配置
- **services_config.json**: 微服务配置

## 🎨 命名规范

### 文件命名
- Python文件: `snake_case.py`
- 配置文件: `snake_case.json`
- 模板文件: `snake_case.html`
- 样式文件: `kebab-case.css`
- 脚本文件: `kebab-case.js`

### 目录命名
- 模块目录: `snake_case/`
- 静态资源: `kebab-case/`
- 配置目录: `snake_case/`

### 类和函数命名
- 类名: `PascalCase`
- 函数名: `snake_case`
- 常量: `UPPER_SNAKE_CASE`
- 变量: `snake_case`

## 🔄 迁移计划

### 第一阶段：目录结构创建
1. 创建新的目录结构
2. 移动现有文件到对应目录
3. 更新导入路径

### 第二阶段：代码重构
1. 按模块重新组织代码
2. 统一命名规范
3. 优化模块依赖关系

### 第三阶段：Web界面美化
1. 引入现代UI框架
2. 设计响应式界面
3. 优化用户体验

### 第四阶段：文档完善
1. 更新API文档
2. 编写用户指南
3. 完善部署文档
