# 微服务使用指南

## 🚀 快速开始

### 1. 启动所有微服务

```bash
# 启动所有微服务（推荐）
python3 start_microservices.py start

# 或者单独启动各个服务
python3 start_microservices.py start --service location
python3 start_microservices.py start --service nlp
python3 start_microservices.py start --service gateway
```

### 2. 检查服务状态

```bash
# 查看所有服务状态
python3 start_microservices.py status

# 健康检查
python3 start_microservices.py health
```

### 3. 停止服务

```bash
# 停止所有服务
python3 start_microservices.py stop

# 停止指定服务
python3 start_microservices.py stop --service gateway
```

## 📡 API接口使用

### 1. 通过API网关访问（推荐）

**基础地址**: `http://127.0.0.1:8000`

#### 归属地查询
```bash
# GET方式查询
curl "http://127.0.0.1:8000/api/v1/location/13800138000"

# POST方式查询
curl -X POST "http://127.0.0.1:8000/api/v1/location/13800138000" \
  -H "Content-Type: application/json"
```

#### 文本分析
```bash
# 单个文本分析
curl -X POST "http://127.0.0.1:8000/api/v1/analyze" \
  -H "Content-Type: application/json" \
  -d '{"text": "这是一个推销电话", "phone_number": "13800138000"}'

# GET方式分析
curl "http://127.0.0.1:8000/api/v1/nlp/analyze/推销电话"
```

#### 完整工作流
```bash
# 完整的电话处理流程
curl -X POST "http://127.0.0.1:8000/api/v1/process-phone" \
  -H "Content-Type: application/json" \
  -d '{
    "phone_number": "13800138000",
    "mark_text": "这是一个推销电话"
  }'
```

### 2. 直接访问微服务

#### 归属地服务 (端口 8001)
```bash
# 查询归属地
curl "http://127.0.0.1:8001/api/v1/location/13800138000"

# 批量查询
curl "http://127.0.0.1:8001/api/v1/batch-location?phone_numbers=13800138000,13900139000"

# 健康检查
curl "http://127.0.0.1:8001/health"
```

#### NLP分析服务 (端口 8002)
```bash
# 文本分析
curl -X POST "http://127.0.0.1:8002/api/v1/analyze" \
  -H "Content-Type: application/json" \
  -d '{"text": "推销电话", "phone_number": "13800138000"}'

# 批量分析
curl -X POST "http://127.0.0.1:8002/api/v1/batch-analyze" \
  -H "Content-Type: application/json" \
  -d '{
    "texts": ["推销电话", "快递通知", "银行客服"],
    "include_details": true
  }'

# 获取支持的分类
curl "http://127.0.0.1:8002/api/v1/categories"
```

## 🔧 配置说明

### 服务端口配置

| 服务 | 端口 | 说明 |
|------|------|------|
| API网关 | 8000 | 统一入口，推荐使用 |
| 归属地服务 | 8001 | 电话号码归属地查询 |
| NLP分析服务 | 8002 | 智能文本分析 |

### 自定义配置

可以通过修改各服务的配置来自定义行为：

```python
# 归属地服务配置
location_config = {
    'host': '127.0.0.1',
    'port': 8001,
    'cache_enabled': True,
    'cache_ttl': 3600
}

# NLP服务配置
nlp_config = {
    'host': '127.0.0.1',
    'port': 8002,
    'cache_enabled': True,
    'max_batch_size': 50,
    'enable_deep_analysis': False
}

# API网关配置
gateway_config = {
    'host': '127.0.0.1',
    'port': 8000,
    'rate_limit_requests_per_minute': 1000,
    'enable_cors': True
}
```

## 📊 监控和统计

### 1. 服务健康检查

```bash
# 网关健康检查
curl "http://127.0.0.1:8000/health"

# 各服务健康检查
curl "http://127.0.0.1:8001/health"
curl "http://127.0.0.1:8002/health"
```

### 2. 服务统计信息

```bash
# 网关统计
curl "http://127.0.0.1:8000/api/v1/stats"

# 归属地服务统计
curl "http://127.0.0.1:8001/api/v1/stats"

# NLP服务统计
curl "http://127.0.0.1:8002/api/v1/stats"

# 服务列表
curl "http://127.0.0.1:8000/api/v1/services"
```

### 3. 性能监控

服务会自动收集以下指标：
- 请求总数和成功率
- 平均响应时间
- 缓存命中率
- 错误率和类型分布

## 🐛 故障排除

### 1. 服务启动失败

**检查端口占用**：
```bash
# 检查端口是否被占用
lsof -i :8000
lsof -i :8001
lsof -i :8002

# 杀死占用端口的进程
kill -9 <PID>
```

**检查依赖**：
```bash
# 检查Python版本
python3 --version

# 检查必要的模块
python3 -c "import asyncio, logging, time, json"
```

### 2. 服务无响应

**重启服务**：
```bash
# 重启指定服务
python3 start_microservices.py restart --service nlp

# 重启所有服务
python3 start_microservices.py restart
```

**检查日志**：
服务日志会输出到控制台，查看错误信息进行诊断。

### 3. API调用失败

**检查服务状态**：
```bash
python3 start_microservices.py status
```

**测试连通性**：
```bash
# 测试网关连通性
curl "http://127.0.0.1:8000/health"

# 测试各服务连通性
curl "http://127.0.0.1:8001/health"
curl "http://127.0.0.1:8002/health"
```

## 📚 API文档

### 在线文档

启动服务后，可以访问以下地址查看详细的API文档：

- **API网关文档**: http://127.0.0.1:8000/docs
- **归属地服务文档**: http://127.0.0.1:8001/docs
- **NLP服务文档**: http://127.0.0.1:8002/docs

### 响应格式

所有API都使用统一的响应格式：

```json
{
  "status": "success",
  "data": {
    // 具体数据
  },
  "processing_time_ms": 1.23,
  "cache_hit": false,
  "message": "可选的消息"
}
```

### 错误处理

错误响应格式：

```json
{
  "status": "error",
  "message": "错误描述",
  "processing_time_ms": 1.23,
  "cache_hit": false
}
```

## 🔄 集成到现有系统

### 1. 作为独立服务使用

微服务可以完全独立运行，通过HTTP API与现有系统集成。

### 2. 作为库使用

也可以直接导入相关模块：

```python
# 使用归属地服务
from location_microservice import LocationMicroservice
service = LocationMicroservice()
result = service.get_phone_location("13800138000")

# 使用NLP分析服务
from nlp_microservice import NLPMicroservice
service = NLPMicroservice()
result = service.analyze_text("推销电话", "13800138000")
```

### 3. 与原有系统兼容

所有微服务都保持与原有接口的完全兼容：

```python
# 原有方式仍然可用
from location_manager import LocationManager
from smart_text_analyzer import SmartTextAnalyzer

location_manager = LocationManager()
text_analyzer = SmartTextAnalyzer()

# 新的微服务方式
from location_microservice import LocationMicroservice
from nlp_microservice import NLPMicroservice

location_service = LocationMicroservice()
nlp_service = NLPMicroservice()
```

## 💡 最佳实践

### 1. 生产环境部署

- 使用进程管理器（如supervisor、systemd）
- 配置反向代理（如nginx）
- 设置日志轮转
- 配置监控告警

### 2. 性能优化

- 启用缓存机制
- 调整批处理大小
- 配置连接池
- 使用负载均衡

### 3. 安全考虑

- 配置防火墙规则
- 启用HTTPS
- 实施API认证
- 设置请求限流

---

**注意**: 这是一个渐进式的微服务架构实现，所有功能都经过充分测试，可以安全地在生产环境中使用。如有问题，请查看日志或联系技术支持。
