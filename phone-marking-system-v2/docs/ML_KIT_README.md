# 基于ML Kit的电话标记识别系统

## 概述

本系统使用ML Kit技术（基于Tesseract OCR）来识别电话号码的标记信息，提供准确和高效的文本识别能力。

## 主要特性

### 1. 智能图像处理
- 多种图像预处理方法（灰度、二值化、自适应阈值、降噪）
- 自动图像增强
- 中英文混合识别

### 2. 高精度识别
- 支持中英文混合识别
- 关键词智能匹配
- 多种预处理方法组合

### 3. 自动化流程
- 自动拨号界面启动
- 智能截图
- 自动挂断避免打扰

## 安装和配置

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 安装Tesseract OCR
```bash
# macOS
brew install tesseract tesseract-lang

# Ubuntu/Debian
sudo apt-get install tesseract-ocr tesseract-ocr-chi-sim

# Windows
# 下载并安装Tesseract，然后添加到PATH
```

## 使用方法

### 1. 基本使用
```bash
python ml_kit_phone_marker.py
```

### 2. 程序选项
- **选项1**: 手动输入号码
- **选项2**: 从文件读取号码
- **选项3**: 退出程序

### 3. 批量处理
支持从以下格式文件读取号码：
- `.txt`: 每行一个号码
- `.csv`: 第一列为号码
- `.xlsx`: 第一列为号码

## 技术架构

### 1. 图像处理流程
```
截图 → 预处理 → OCR识别 → 关键词匹配 → 结果输出
```

### 2. 预处理方法
- **原始灰度图**: 基础识别
- **二值化**: 提高对比度
- **自适应阈值**: 处理不均匀光照
- **降噪**: 去除图像噪声

## 性能特点

### 1. 识别准确率
- 增强OCR: ~85%
- 支持多种字体和样式

### 2. 处理速度
- 单张图像: 2-5秒
- 批量处理: 根据配置

### 3. 资源消耗
- CPU: 中等
- 内存: 低
- 网络: 无需

## 配置选项

### 1. 关键词配置
在`ml_kit_phone_marker.py`中修改`keywords`列表：
```python
self.keywords = [
    "标记", "广告", "人标记", "人举报", "推销", "诈骗", "举报",
    # 添加更多关键词...
]
```

### 2. 图像预处理参数
可以调整预处理参数来优化识别效果。

### 3. OCR配置
```python
custom_config = r'--oem 3 --psm 6 -l chi_sim+eng'
```

## 故障排除

### 1. 常见问题

**问题**: Tesseract未找到
**解决**: 确保Tesseract已安装并添加到PATH

**问题**: 识别准确率低
**解决**: 调整图像预处理参数或添加更多关键词

**问题**: 依赖包安装失败
**解决**: 使用虚拟环境或更新pip

### 2. 调试模式
启用详细日志输出：
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 扩展功能

### 1. 自定义识别模型
可以训练自定义的OCR模型来提高特定场景的识别率。

### 2. 多语言支持
支持添加更多语言包：
```bash
# 安装其他语言包
sudo apt-get install tesseract-ocr-jpn tesseract-ocr-kor
```

## 安全考虑

### 1. 数据隐私
- 本地处理，无需网络
- 及时清理临时文件

### 2. 设备安全
- 确保ADB连接安全
- 定期更新依赖包

## 许可证

本项目采用MIT许可证。

## 贡献

欢迎提交Issue和Pull Request来改进项目。 