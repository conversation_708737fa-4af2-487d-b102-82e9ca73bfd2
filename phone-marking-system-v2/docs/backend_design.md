# 电话标记后台管理系统设计

## 1. 系统架构

### 技术栈选择
- **后端**: Python Flask/FastAPI
- **前端**: Vue.js + Element UI
- **数据库**: SQLite (开发) / MySQL (生产)
- **部署**: Docker + Nginx

### 系统模块
```
├── 数据采集模块 (Python + ADB)
├── 数据处理模块 (OCR + 分析)
├── 数据存储模块 (SQLite/MySQL)
├── 后台管理模块 (Flask + Vue)
└── 数据展示模块 (图表 + 报表)
```

## 2. 数据库设计

### 核心表结构

#### 2.1 拨号记录表 (dial_records)
```sql
CREATE TABLE dial_records (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    phone_number VARCHAR(20) NOT NULL,
    mark_info TEXT,
    phone_model VARCHAR(100),
    android_version VARCHAR(20),
    dial_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(20) DEFAULT 'success',
    screenshot_path VARCHAR(255),
    ocr_text TEXT,
    processing_time REAL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 2.2 标记统计表 (mark_statistics)
```sql
CREATE TABLE mark_statistics (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    phone_number VARCHAR(20) UNIQUE,
    total_marks INTEGER DEFAULT 0,
    mark_types JSON,
    first_detected TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 2.3 手机型号表 (phone_models)
```sql
CREATE TABLE phone_models (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    brand VARCHAR(50),
    model VARCHAR(100),
    android_version VARCHAR(20),
    dialer_package VARCHAR(100),
    mark_area_config JSON,
    first_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_used TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 2.4 用户表 (users)
```sql
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    email VARCHAR(100),
    role VARCHAR(20) DEFAULT 'user',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP
);
```

#### 2.5 任务表 (tasks)
```sql
CREATE TABLE tasks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL,
    phone_list TEXT,
    status VARCHAR(20) DEFAULT 'pending',
    progress INTEGER DEFAULT 0,
    total_count INTEGER DEFAULT 0,
    completed_count INTEGER DEFAULT 0,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);
```

## 3. 后台管理功能

### 3.1 数据管理
- **拨号记录管理**
  - 查看所有拨号记录
  - 按条件筛选（号码、时间、状态、手机型号）
  - 批量导出
  - 删除记录

- **标记统计管理**
  - 查看标记统计
  - 按标记类型分析
  - 趋势分析

- **手机型号管理**
  - 查看支持的手机型号
  - 添加新手机型号配置
  - 修改现有配置

### 3.2 任务管理
- **批量任务**
  - 创建批量拨号任务
  - 上传号码文件
  - 设置任务参数
  - 监控任务进度

- **任务历史**
  - 查看历史任务
  - 任务结果分析
  - 重新执行任务

### 3.3 系统管理
- **用户管理**
  - 用户注册/登录
  - 权限管理
  - 操作日志

- **系统配置**
  - 拨号间隔设置
  - OCR参数配置
  - 数据库备份

### 3.4 数据分析
- **实时统计**
  - 今日拨号数量
  - 成功率统计
  - 标记类型分布

- **趋势分析**
  - 拨号趋势图
  - 标记变化趋势
  - 手机型号分布

- **报表生成**
  - 日报/周报/月报
  - 自定义报表
  - 数据导出

## 4. API接口设计

### 4.1 数据接口
```
GET    /api/records              # 获取拨号记录
POST   /api/records              # 创建拨号记录
GET    /api/records/{id}         # 获取单条记录
PUT    /api/records/{id}         # 更新记录
DELETE /api/records/{id}         # 删除记录

GET    /api/statistics           # 获取统计信息
GET    /api/statistics/trends    # 获取趋势数据

GET    /api/phone-models         # 获取手机型号
POST   /api/phone-models         # 添加手机型号
```

### 4.2 任务接口
```
GET    /api/tasks                # 获取任务列表
POST   /api/tasks                # 创建任务
GET    /api/tasks/{id}           # 获取任务详情
PUT    /api/tasks/{id}           # 更新任务
DELETE /api/tasks/{id}           # 删除任务
POST   /api/tasks/{id}/start     # 开始任务
POST   /api/tasks/{id}/stop      # 停止任务
```

### 4.3 用户接口
```
POST   /api/auth/login           # 用户登录
POST   /api/auth/logout          # 用户登出
GET    /api/auth/profile         # 获取用户信息
PUT    /api/auth/profile         # 更新用户信息
```

## 5. 前端页面设计

### 5.1 主要页面
- **仪表板** (Dashboard)
  - 实时统计卡片
  - 趋势图表
  - 快速操作

- **数据管理** (Data Management)
  - 拨号记录列表
  - 筛选和搜索
  - 批量操作

- **任务管理** (Task Management)
  - 任务列表
  - 创建任务
  - 任务监控

- **统计分析** (Analytics)
  - 统计图表
  - 趋势分析
  - 报表生成

- **系统设置** (Settings)
  - 用户管理
  - 系统配置
  - 数据备份

### 5.2 组件设计
- **数据表格组件**
  - 分页
  - 排序
  - 筛选
  - 批量操作

- **图表组件**
  - 柱状图
  - 折线图
  - 饼图
  - 热力图

- **表单组件**
  - 任务创建表单
  - 配置表单
  - 用户表单

## 6. 部署方案

### 6.1 开发环境
```yaml
# docker-compose.yml
version: '3.8'
services:
  backend:
    build: ./backend
    ports:
      - "5000:5000"
    volumes:
      - ./data:/app/data
    environment:
      - FLASK_ENV=development
  
  frontend:
    build: ./frontend
    ports:
      - "8080:80"
    depends_on:
      - backend
  
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
```

### 6.2 生产环境
- **负载均衡**: Nginx
- **应用服务器**: Gunicorn + Flask
- **数据库**: MySQL
- **缓存**: Redis
- **监控**: Prometheus + Grafana

## 7. 安全考虑

### 7.1 认证授权
- JWT Token认证
- 角色权限控制
- API访问限制

### 7.2 数据安全
- 数据库加密
- 敏感信息脱敏
- 操作日志记录

### 7.3 网络安全
- HTTPS加密
- API限流
- 防火墙配置

## 8. 扩展功能

### 8.1 智能分析
- 机器学习标记分类
- 异常检测
- 预测分析

### 8.2 移动端
- 手机APP
- 微信小程序
- 移动端管理

### 8.3 第三方集成
- 短信服务
- 邮件通知
- 数据同步

## 9. 开发计划

### 第一阶段 (2周)
- 数据库设计和实现
- 基础API开发
- 简单前端界面

### 第二阶段 (2周)
- 完整功能开发
- 前端界面完善
- 测试和优化

### 第三阶段 (1周)
- 部署和配置
- 文档编写
- 用户培训

## 10. 技术难点

### 10.1 OCR识别优化
- 多手机型号适配
- 识别准确率提升
- 处理速度优化

### 10.2 并发处理
- 多设备同时操作
- 任务队列管理
- 资源调度

### 10.3 数据一致性
- 事务管理
- 数据同步
- 备份恢复 