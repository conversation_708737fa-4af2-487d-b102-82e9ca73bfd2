# 电话号码标记识别系统

一个基于微服务架构的企业级电话号码标记识别管理系统。

## 🚀 快速开始

### 安装依赖
```bash
pip install -r requirements.txt
```

### 启动系统
```bash
python main.py
```

### 访问系统
- Web管理界面: http://127.0.0.1:8080
- API网关: http://127.0.0.1:8000
- API文档: http://127.0.0.1:8000/docs

## 📁 项目结构

```
phone-marking-system-v2/
├── src/                    # 源代码
│   ├── core/              # 核心模块
│   ├── services/          # 微服务
│   └── web/               # Web前端
├── config/                # 配置文件
├── data/                  # 数据目录
├── tests/                 # 测试目录
├── docs/                  # 文档目录
└── scripts/               # 脚本目录
```

## 🔧 功能特性

- 大规模批量处理
- 智能文本分析
- 完整权限管理
- 实时监控告警
- 高性能缓存
- 分布式追踪

## 📞 支持

如有问题，请查看docs目录下的文档或提交Issue。
