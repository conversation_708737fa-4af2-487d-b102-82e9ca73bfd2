"""
统一响应格式工具
"""
from typing import Any, Optional, Dict
from fastapi.responses import JSONResponse


def success(data: Any = None, message: str = "操作成功", code: int = 200) -> Dict[str, Any]:
    """
    成功响应
    
    Args:
        data: 响应数据
        message: 响应消息
        code: 响应代码
        
    Returns:
        响应字典
    """
    return {
        "code": code,
        "message": message,
        "data": data,
        "success": True
    }


def fail(message: str = "操作失败", code: int = 400, data: Any = None) -> Dict[str, Any]:
    """
    失败响应
    
    Args:
        message: 错误消息
        code: 错误代码
        data: 响应数据
        
    Returns:
        响应字典
    """
    return {
        "code": code,
        "message": message,
        "data": data,
        "success": False
    }


def paginate_response(
    rows: list, 
    total: int, 
    page: int, 
    page_size: int, 
    message: str = "查询成功"
) -> Dict[str, Any]:
    """
    分页响应
    
    Args:
        rows: 数据列表
        total: 总数量
        page: 当前页
        page_size: 页大小
        message: 响应消息
        
    Returns:
        分页响应字典
    """
    return success(
        data={
            "rows": rows,
            "total": total,
            "page": page,
            "page_size": page_size,
            "total_pages": (total + page_size - 1) // page_size
        },
        message=message
    )


class ResponseCode:
    """
    响应代码常量
    """
    SUCCESS = 200
    CREATED = 201
    BAD_REQUEST = 400
    UNAUTHORIZED = 401
    FORBIDDEN = 403
    NOT_FOUND = 404
    INTERNAL_ERROR = 500
    
    # 业务错误代码
    PHONE_EXISTS = 1001
    PHONE_NOT_FOUND = 1002
    INVALID_PHONE_FORMAT = 1003
    BATCH_TASK_NOT_FOUND = 1004
    DEVICE_NOT_FOUND = 1005
    DEVICE_OFFLINE = 1006
    IMPORT_FILE_ERROR = 1007
    EXPORT_ERROR = 1008
    SYNC_ERROR = 1009


def business_fail(code: int, message: str, data: Any = None) -> Dict[str, Any]:
    """
    业务失败响应
    
    Args:
        code: 业务错误代码
        message: 错误消息
        data: 响应数据
        
    Returns:
        响应字典
    """
    return {
        "code": code,
        "message": message,
        "data": data,
        "success": False
    }
