"""
电话号码处理器
集成原有的电话标记识别功能
"""
import asyncio
import json
import re
import random
from typing import Dict, Optional, Any
from datetime import datetime
import sys
import os

# 添加原有项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../../../../src'))

try:
    from core.phone_marker import PhoneMarker
    from core.location_parser import LocationParser
    from utils.config_manager import ConfigManager
except ImportError:
    # 如果导入失败，使用模拟实现
    PhoneMarker = None
    LocationParser = None
    ConfigManager = None


class PhoneProcessor:
    """
    电话号码处理器
    """
    
    def __init__(self):
        """
        初始化处理器
        """
        self.phone_marker = None
        self.location_parser = None
        self.config_manager = None
        
        # 尝试初始化原有组件
        try:
            if ConfigManager:
                self.config_manager = ConfigManager()
            if PhoneMarker:
                self.phone_marker = PhoneMarker()
            if LocationParser:
                self.location_parser = LocationParser()
        except Exception as e:
            print(f"初始化原有组件失败: {e}")
            # 使用模拟实现
            self._init_mock_components()
    
    def _init_mock_components(self):
        """
        初始化模拟组件（用于演示）
        """
        # 模拟的省市数据
        self.mock_locations = {
            '010': {'province': '北京', 'city': '北京'},
            '021': {'province': '上海', 'city': '上海'},
            '020': {'province': '广东', 'city': '广州'},
            '0755': {'province': '广东', 'city': '深圳'},
            '0571': {'province': '浙江', 'city': '杭州'},
            '025': {'province': '江苏', 'city': '南京'},
            '028': {'province': '四川', 'city': '成都'},
            '027': {'province': '湖北', 'city': '武汉'},
        }
        
        # 模拟的运营商数据
        self.mock_operators = {
            '130': '中国联通', '131': '中国联通', '132': '中国联通',
            '133': '中国电信', '134': '中国移动', '135': '中国移动',
            '136': '中国移动', '137': '中国移动', '138': '中国移动',
            '139': '中国移动', '150': '中国移动', '151': '中国移动',
            '152': '中国移动', '153': '中国电信', '155': '中国联通',
            '156': '中国联通', '157': '中国移动', '158': '中国移动',
            '159': '中国移动', '180': '中国电信', '181': '中国电信',
            '182': '中国移动', '183': '中国移动', '184': '中国移动',
            '185': '中国联通', '186': '中国联通', '187': '中国移动',
            '188': '中国移动', '189': '中国电信'
        }
        
        # 模拟的标记类型
        self.mock_mark_types = [
            '正常号码', '骚扰电话', '诈骗电话', '推销电话', 
            '企业电话', '客服电话', '外卖电话', '快递电话'
        ]
    
    async def process_phone(self, phone_number: str) -> Optional[Dict[str, Any]]:
        """
        处理电话号码，获取标记信息
        
        Args:
            phone_number: 电话号码
            
        Returns:
            处理结果字典
        """
        try:
            # 清理号码格式
            clean_phone = self._clean_phone_number(phone_number)
            if not clean_phone:
                return None
            
            # 判断号码类型
            phone_type = self._detect_phone_type(clean_phone)
            
            # 获取地理位置信息
            location_info = await self._get_location_info(clean_phone, phone_type)
            
            # 获取运营商信息
            operator_info = self._get_operator_info(clean_phone, phone_type)
            
            # 获取标记信息
            mark_info = await self._get_mark_info(clean_phone)
            
            # 组合结果
            result = {
                'phone_number': clean_phone,
                'phone_type': phone_type,
                'province': location_info.get('province', ''),
                'city': location_info.get('city', ''),
                'area_code': location_info.get('area_code', ''),
                'operator': operator_info,
                'mark_count': mark_info.get('mark_count', 0),
                'mark_type': mark_info.get('mark_type', ''),
                'confidence_score': mark_info.get('confidence_score', 0.0),
                'last_mark_time': mark_info.get('last_mark_time'),
                'tags': mark_info.get('tags', []),
                'processed_at': datetime.now().isoformat()
            }
            
            return result
            
        except Exception as e:
            print(f"处理号码 {phone_number} 失败: {e}")
            return None
    
    def _clean_phone_number(self, phone_number: str) -> Optional[str]:
        """
        清理电话号码格式
        """
        if not phone_number:
            return None
        
        # 移除所有非数字字符
        clean = re.sub(r'[^\d]', '', str(phone_number))
        
        # 验证号码格式
        if len(clean) == 11 and clean.startswith(('1', '2', '3', '4', '5', '6', '7', '8', '9')):
            return clean
        elif len(clean) >= 10 and clean.startswith('0'):
            return clean
        elif len(clean) >= 7:
            return clean
        
        return None
    
    def _detect_phone_type(self, phone_number: str) -> str:
        """
        检测号码类型
        """
        if len(phone_number) == 11 and phone_number.startswith('1'):
            return 'mobile'
        elif phone_number.startswith('0') or len(phone_number) <= 8:
            return 'landline'
        else:
            return 'unknown'
    
    async def _get_location_info(self, phone_number: str, phone_type: str) -> Dict[str, str]:
        """
        获取地理位置信息
        """
        try:
            if self.location_parser:
                # 使用原有的位置解析器
                result = await self.location_parser.parse_location(phone_number)
                return result or {}
            else:
                # 使用模拟实现
                return self._mock_get_location(phone_number, phone_type)
        except Exception as e:
            print(f"获取位置信息失败: {e}")
            return self._mock_get_location(phone_number, phone_type)
    
    def _mock_get_location(self, phone_number: str, phone_type: str) -> Dict[str, str]:
        """
        模拟获取位置信息
        """
        if phone_type == 'mobile' and len(phone_number) == 11:
            # 手机号码，根据前三位判断
            prefix = phone_number[:3]
            # 模拟根据号段分配地区
            mock_provinces = ['北京', '上海', '广东', '浙江', '江苏', '山东', '四川', '湖北']
            mock_cities = {
                '北京': '北京', '上海': '上海', '广东': '广州', 
                '浙江': '杭州', '江苏': '南京', '山东': '济南',
                '四川': '成都', '湖北': '武汉'
            }
            
            province = mock_provinces[int(prefix) % len(mock_provinces)]
            city = mock_cities.get(province, province)
            
            return {
                'province': province,
                'city': city,
                'area_code': ''
            }
            
        elif phone_type == 'landline':
            # 固定电话，根据区号判断
            for area_code, location in self.mock_locations.items():
                if phone_number.startswith(area_code):
                    return {
                        'province': location['province'],
                        'city': location['city'],
                        'area_code': area_code
                    }
            
            # 默认返回
            return {
                'province': '未知',
                'city': '未知',
                'area_code': phone_number[:4] if len(phone_number) > 4 else ''
            }
        
        return {'province': '', 'city': '', 'area_code': ''}
    
    def _get_operator_info(self, phone_number: str, phone_type: str) -> str:
        """
        获取运营商信息
        """
        if phone_type == 'mobile' and len(phone_number) == 11:
            prefix = phone_number[:3]
            return self.mock_operators.get(prefix, '未知运营商')
        
        return ''
    
    async def _get_mark_info(self, phone_number: str) -> Dict[str, Any]:
        """
        获取标记信息
        """
        try:
            if self.phone_marker:
                # 使用原有的标记器
                result = await self.phone_marker.get_mark_info(phone_number)
                return result or {}
            else:
                # 使用模拟实现
                return self._mock_get_mark_info(phone_number)
        except Exception as e:
            print(f"获取标记信息失败: {e}")
            return self._mock_get_mark_info(phone_number)
    
    def _mock_get_mark_info(self, phone_number: str) -> Dict[str, Any]:
        """
        模拟获取标记信息
        """
        # 模拟标记数据
        mark_count = random.randint(0, 50)
        mark_type = random.choice(self.mock_mark_types)
        confidence_score = round(random.uniform(70.0, 99.0), 2)
        
        # 模拟一些特殊号码
        if phone_number.endswith(('8888', '6666', '9999')):
            mark_type = '企业电话'
            mark_count = random.randint(5, 15)
        elif phone_number.endswith(('1234', '5678')):
            mark_type = '骚扰电话'
            mark_count = random.randint(20, 50)
        elif phone_number.startswith('400'):
            mark_type = '客服电话'
            mark_count = random.randint(1, 10)
        
        return {
            'mark_count': mark_count,
            'mark_type': mark_type,
            'confidence_score': confidence_score,
            'last_mark_time': datetime.now(),
            'tags': [mark_type] if mark_type != '正常号码' else []
        }
    
    async def batch_process_phones(self, phone_numbers: list) -> Dict[str, Any]:
        """
        批量处理电话号码
        
        Args:
            phone_numbers: 电话号码列表
            
        Returns:
            批量处理结果
        """
        results = []
        success_count = 0
        failed_count = 0
        
        for phone_number in phone_numbers:
            try:
                result = await self.process_phone(phone_number)
                if result:
                    results.append(result)
                    success_count += 1
                else:
                    failed_count += 1
            except Exception as e:
                print(f"处理号码 {phone_number} 失败: {e}")
                failed_count += 1
        
        return {
            'results': results,
            'total_count': len(phone_numbers),
            'success_count': success_count,
            'failed_count': failed_count,
            'processed_at': datetime.now().isoformat()
        }
