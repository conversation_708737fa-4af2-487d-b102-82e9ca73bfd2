"""
电话标记系统数据模型
"""
from sqlalchemy import Column, Integer, String, DateTime, Text, JSON, BigInteger, Numeric
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime

Base = declarative_base()


class PhoneData(Base):
    """
    电话号码数据表
    """
    __tablename__ = "phone_data"
    
    id = Column(BigInteger, primary_key=True, index=True, comment="主键ID")
    phone_number = Column(String(20), unique=True, nullable=False, comment="电话号码")
    phone_type = Column(String(20), default="mobile", comment="号码类型")
    province = Column(String(50), default="", comment="省份")
    city = Column(String(50), default="", comment="城市")
    area_code = Column(String(10), default="", comment="区号")
    operator = Column(String(20), default="", comment="运营商")
    mark_count = Column(Integer, default=0, comment="标记数量")
    mark_type = Column(String(50), default="", comment="标记类型")
    last_mark_time = Column(DateTime, comment="最后标记时间")
    recognition_result = Column(Text, comment="识别结果JSON")
    confidence_score = Column(Numeric(5, 2), default=0.00, comment="置信度分数")
    status = Column(String(20), default="pending", comment="处理状态")
    batch_id = Column(BigInteger, comment="批次ID")
    created_by = Column(BigInteger, comment="创建者")
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")


class BatchTask(Base):
    """
    批量任务表
    """
    __tablename__ = "batch_task"
    
    id = Column(BigInteger, primary_key=True, index=True, comment="任务ID")
    task_name = Column(String(100), nullable=False, comment="任务名称")
    task_type = Column(String(20), nullable=False, comment="任务类型")
    file_path = Column(String(500), default="", comment="文件路径")
    file_name = Column(String(200), default="", comment="文件名称")
    file_size = Column(BigInteger, default=0, comment="文件大小")
    total_count = Column(Integer, default=0, comment="总数量")
    processed_count = Column(Integer, default=0, comment="已处理数量")
    success_count = Column(Integer, default=0, comment="成功数量")
    failed_count = Column(Integer, default=0, comment="失败数量")
    progress = Column(Numeric(5, 2), default=0.00, comment="进度百分比")
    status = Column(String(20), default="pending", comment="任务状态")
    start_time = Column(DateTime, comment="开始时间")
    end_time = Column(DateTime, comment="结束时间")
    error_message = Column(Text, comment="错误信息")
    result_file_path = Column(String(500), default="", comment="结果文件路径")
    config = Column(JSON, comment="任务配置JSON")
    created_by = Column(BigInteger, comment="创建者")
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")


class Device(Base):
    """
    设备管理表
    """
    __tablename__ = "device"
    
    id = Column(BigInteger, primary_key=True, index=True, comment="设备ID")
    device_name = Column(String(100), nullable=False, comment="设备名称")
    device_id = Column(String(100), unique=True, nullable=False, comment="设备唯一标识")
    device_type = Column(String(20), default="android", comment="设备类型")
    device_model = Column(String(100), default="", comment="设备型号")
    system_version = Column(String(50), default="", comment="系统版本")
    app_version = Column(String(50), default="", comment="应用版本")
    status = Column(String(20), default="offline", comment="连接状态")
    last_heartbeat = Column(DateTime, comment="最后心跳时间")
    ip_address = Column(String(50), default="", comment="IP地址")
    mac_address = Column(String(50), default="", comment="MAC地址")
    config = Column(JSON, comment="设备配置JSON")
    performance_info = Column(JSON, comment="性能信息JSON")
    created_by = Column(BigInteger, comment="创建者")
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")


class SysUser(Base):
    """
    用户表
    """
    __tablename__ = "sys_user"
    
    user_id = Column(BigInteger, primary_key=True, index=True, comment="用户ID")
    username = Column(String(30), unique=True, nullable=False, comment="用户账号")
    nickname = Column(String(30), nullable=False, comment="用户昵称")
    email = Column(String(50), default="", comment="用户邮箱")
    phone = Column(String(11), default="", comment="手机号码")
    sex = Column(String(1), default="0", comment="用户性别")
    avatar = Column(String(100), default="", comment="头像地址")
    password = Column(String(100), default="", comment="密码")
    status = Column(String(1), default="0", comment="帐号状态")
    del_flag = Column(String(1), default="0", comment="删除标志")
    login_ip = Column(String(128), default="", comment="最后登录IP")
    login_date = Column(DateTime, comment="最后登录时间")
    create_by = Column(String(64), default="", comment="创建者")
    create_time = Column(DateTime, comment="创建时间")
    update_by = Column(String(64), default="", comment="更新者")
    update_time = Column(DateTime, comment="更新时间")
    remark = Column(String(500), comment="备注")


class SysRole(Base):
    """
    角色表
    """
    __tablename__ = "sys_role"
    
    role_id = Column(BigInteger, primary_key=True, index=True, comment="角色ID")
    role_name = Column(String(30), nullable=False, comment="角色名称")
    role_key = Column(String(100), nullable=False, comment="角色权限字符串")
    role_sort = Column(Integer, nullable=False, comment="显示顺序")
    data_scope = Column(String(1), default="1", comment="数据范围")
    menu_check_strictly = Column(Integer, default=1, comment="菜单树选择项是否关联显示")
    dept_check_strictly = Column(Integer, default=1, comment="部门树选择项是否关联显示")
    status = Column(String(1), nullable=False, comment="角色状态")
    del_flag = Column(String(1), default="0", comment="删除标志")
    create_by = Column(String(64), default="", comment="创建者")
    create_time = Column(DateTime, comment="创建时间")
    update_by = Column(String(64), default="", comment="更新者")
    update_time = Column(DateTime, comment="更新时间")
    remark = Column(String(500), comment="备注")


class SysMenu(Base):
    """
    菜单权限表
    """
    __tablename__ = "sys_menu"
    
    menu_id = Column(BigInteger, primary_key=True, index=True, comment="菜单ID")
    menu_name = Column(String(50), nullable=False, comment="菜单名称")
    parent_id = Column(BigInteger, default=0, comment="父菜单ID")
    order_num = Column(Integer, default=0, comment="显示顺序")
    path = Column(String(200), default="", comment="路由地址")
    component = Column(String(255), comment="组件路径")
    query = Column(String(255), comment="路由参数")
    is_frame = Column(Integer, default=1, comment="是否为外链")
    is_cache = Column(Integer, default=0, comment="是否缓存")
    menu_type = Column(String(1), default="", comment="菜单类型")
    visible = Column(String(1), default="0", comment="菜单状态")
    status = Column(String(1), default="0", comment="菜单状态")
    perms = Column(String(100), comment="权限标识")
    icon = Column(String(100), default="#", comment="菜单图标")
    create_by = Column(String(64), default="", comment="创建者")
    create_time = Column(DateTime, comment="创建时间")
    update_by = Column(String(64), default="", comment="更新者")
    update_time = Column(DateTime, comment="更新时间")
    remark = Column(String(500), comment="备注")


class SysLog(Base):
    """
    系统日志表
    """
    __tablename__ = "sys_log"
    
    id = Column(BigInteger, primary_key=True, index=True, comment="日志ID")
    log_type = Column(String(20), nullable=False, comment="日志类型")
    module = Column(String(50), default="", comment="模块名称")
    operation = Column(String(100), default="", comment="操作内容")
    method = Column(String(100), default="", comment="请求方法")
    request_url = Column(String(500), default="", comment="请求URL")
    request_ip = Column(String(50), default="", comment="请求IP")
    request_params = Column(Text, comment="请求参数")
    response_data = Column(Text, comment="响应数据")
    error_message = Column(Text, comment="错误信息")
    execution_time = Column(Integer, default=0, comment="执行时间")
    user_id = Column(BigInteger, comment="用户ID")
    username = Column(String(50), default="", comment="用户名")
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")


class SyncRecord(Base):
    """
    数据同步记录表
    """
    __tablename__ = "sync_record"
    
    id = Column(BigInteger, primary_key=True, index=True, comment="同步记录ID")
    sync_type = Column(String(20), nullable=False, comment="同步类型")
    table_name = Column(String(50), nullable=False, comment="表名")
    sync_count = Column(Integer, default=0, comment="同步数量")
    success_count = Column(Integer, default=0, comment="成功数量")
    failed_count = Column(Integer, default=0, comment="失败数量")
    status = Column(String(20), default="pending", comment="同步状态")
    start_time = Column(DateTime, comment="开始时间")
    end_time = Column(DateTime, comment="结束时间")
    error_message = Column(Text, comment="错误信息")
    created_by = Column(BigInteger, comment="创建者")
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
