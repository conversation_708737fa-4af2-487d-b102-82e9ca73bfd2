"""
电话标记系统数据模式
"""
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from decimal import Decimal


class PhoneDataBase(BaseModel):
    """
    电话号码数据基础模式
    """
    phone_number: str = Field(..., description="电话号码")
    phone_type: str = Field(default="mobile", description="号码类型")
    province: Optional[str] = Field(default="", description="省份")
    city: Optional[str] = Field(default="", description="城市")
    area_code: Optional[str] = Field(default="", description="区号")
    operator: Optional[str] = Field(default="", description="运营商")
    mark_count: Optional[int] = Field(default=0, description="标记数量")
    mark_type: Optional[str] = Field(default="", description="标记类型")
    last_mark_time: Optional[datetime] = Field(None, description="最后标记时间")
    recognition_result: Optional[str] = Field(None, description="识别结果JSON")
    confidence_score: Optional[Decimal] = Field(default=0.00, description="置信度分数")
    status: Optional[str] = Field(default="pending", description="处理状态")


class PhoneDataCreate(PhoneDataBase):
    """
    创建电话号码数据模式
    """
    pass


class PhoneDataUpdate(PhoneDataBase):
    """
    更新电话号码数据模式
    """
    id: int = Field(..., description="记录ID")
    phone_number: Optional[str] = Field(None, description="电话号码")


class PhoneDataResponse(PhoneDataBase):
    """
    电话号码数据响应模式
    """
    id: int = Field(..., description="记录ID")
    batch_id: Optional[int] = Field(None, description="批次ID")
    created_by: Optional[int] = Field(None, description="创建者")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    class Config:
        from_attributes = True


class BatchTaskBase(BaseModel):
    """
    批量任务基础模式
    """
    task_name: str = Field(..., description="任务名称")
    task_type: str = Field(..., description="任务类型")
    file_path: Optional[str] = Field(default="", description="文件路径")
    file_name: Optional[str] = Field(default="", description="文件名称")
    file_size: Optional[int] = Field(default=0, description="文件大小")
    total_count: Optional[int] = Field(default=0, description="总数量")
    processed_count: Optional[int] = Field(default=0, description="已处理数量")
    success_count: Optional[int] = Field(default=0, description="成功数量")
    failed_count: Optional[int] = Field(default=0, description="失败数量")
    progress: Optional[Decimal] = Field(default=0.00, description="进度百分比")
    status: Optional[str] = Field(default="pending", description="任务状态")
    start_time: Optional[datetime] = Field(None, description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
    error_message: Optional[str] = Field(None, description="错误信息")
    result_file_path: Optional[str] = Field(default="", description="结果文件路径")
    config: Optional[Dict[str, Any]] = Field(None, description="任务配置")


class BatchTaskCreate(BatchTaskBase):
    """
    创建批量任务模式
    """
    pass


class BatchTaskUpdate(BatchTaskBase):
    """
    更新批量任务模式
    """
    id: int = Field(..., description="任务ID")


class BatchTaskResponse(BatchTaskBase):
    """
    批量任务响应模式
    """
    id: int = Field(..., description="任务ID")
    created_by: Optional[int] = Field(None, description="创建者")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    class Config:
        from_attributes = True


class DeviceBase(BaseModel):
    """
    设备基础模式
    """
    device_name: str = Field(..., description="设备名称")
    device_id: str = Field(..., description="设备唯一标识")
    device_type: Optional[str] = Field(default="android", description="设备类型")
    device_model: Optional[str] = Field(default="", description="设备型号")
    system_version: Optional[str] = Field(default="", description="系统版本")
    app_version: Optional[str] = Field(default="", description="应用版本")
    status: Optional[str] = Field(default="offline", description="连接状态")
    last_heartbeat: Optional[datetime] = Field(None, description="最后心跳时间")
    ip_address: Optional[str] = Field(default="", description="IP地址")
    mac_address: Optional[str] = Field(default="", description="MAC地址")
    config: Optional[Dict[str, Any]] = Field(None, description="设备配置")
    performance_info: Optional[Dict[str, Any]] = Field(None, description="性能信息")


class DeviceCreate(DeviceBase):
    """
    创建设备模式
    """
    pass


class DeviceUpdate(DeviceBase):
    """
    更新设备模式
    """
    id: int = Field(..., description="设备ID")
    device_name: Optional[str] = Field(None, description="设备名称")
    device_id: Optional[str] = Field(None, description="设备唯一标识")


class DeviceResponse(DeviceBase):
    """
    设备响应模式
    """
    id: int = Field(..., description="设备ID")
    created_by: Optional[int] = Field(None, description="创建者")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    class Config:
        from_attributes = True


class StatisticsResponse(BaseModel):
    """
    统计数据响应模式
    """
    total_count: int = Field(..., description="总数量")
    completed_count: int = Field(..., description="已完成数量")
    pending_count: int = Field(..., description="待处理数量")
    province_stats: List[Dict[str, Any]] = Field(..., description="省份统计")
    mark_type_stats: List[Dict[str, Any]] = Field(..., description="标记类型统计")


class UserBase(BaseModel):
    """
    用户基础模式
    """
    username: str = Field(..., description="用户账号")
    nickname: str = Field(..., description="用户昵称")
    email: Optional[str] = Field(default="", description="用户邮箱")
    phone: Optional[str] = Field(default="", description="手机号码")
    sex: Optional[str] = Field(default="0", description="用户性别")
    avatar: Optional[str] = Field(default="", description="头像地址")
    status: Optional[str] = Field(default="0", description="帐号状态")
    remark: Optional[str] = Field(None, description="备注")


class UserCreate(UserBase):
    """
    创建用户模式
    """
    password: str = Field(..., description="密码")


class UserUpdate(UserBase):
    """
    更新用户模式
    """
    user_id: int = Field(..., description="用户ID")
    username: Optional[str] = Field(None, description="用户账号")
    nickname: Optional[str] = Field(None, description="用户昵称")
    password: Optional[str] = Field(None, description="密码")


class UserResponse(UserBase):
    """
    用户响应模式
    """
    user_id: int = Field(..., description="用户ID")
    del_flag: str = Field(..., description="删除标志")
    login_ip: Optional[str] = Field(None, description="最后登录IP")
    login_date: Optional[datetime] = Field(None, description="最后登录时间")
    create_by: Optional[str] = Field(None, description="创建者")
    create_time: Optional[datetime] = Field(None, description="创建时间")
    update_by: Optional[str] = Field(None, description="更新者")
    update_time: Optional[datetime] = Field(None, description="更新时间")
    
    class Config:
        from_attributes = True


class LoginRequest(BaseModel):
    """
    登录请求模式
    """
    username: str = Field(..., description="用户名")
    password: str = Field(..., description="密码")
    captcha: Optional[str] = Field(None, description="验证码")


class LoginResponse(BaseModel):
    """
    登录响应模式
    """
    access_token: str = Field(..., description="访问令牌")
    token_type: str = Field(default="bearer", description="令牌类型")
    expires_in: int = Field(..., description="过期时间（秒）")
    user_info: UserResponse = Field(..., description="用户信息")


class ImportConfig(BaseModel):
    """
    导入配置模式
    """
    batch_size: Optional[int] = Field(default=1000, description="批次大小")
    auto_process: Optional[bool] = Field(default=True, description="自动处理")
    phone_type: Optional[str] = Field(default="auto", description="号码类型")
    description: Optional[str] = Field(default="", description="描述")


class ExportConfig(BaseModel):
    """
    导出配置模式
    """
    format: Optional[str] = Field(default="xlsx", description="导出格式")
    fields: Optional[List[str]] = Field(None, description="导出字段")
    filters: Optional[Dict[str, Any]] = Field(None, description="筛选条件")


class SyncConfig(BaseModel):
    """
    同步配置模式
    """
    sync_type: str = Field(..., description="同步类型")
    table_name: str = Field(..., description="表名")
    target_url: Optional[str] = Field(None, description="目标URL")
    auth_token: Optional[str] = Field(None, description="认证令牌")
    batch_size: Optional[int] = Field(default=1000, description="批次大小")
