"""
电话号码数据管理API
"""
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form
from sqlalchemy.orm import Session
from typing import List, Optional
import json
import pandas as pd
from datetime import datetime, timedelta
import asyncio
import uuid
from fastapi.responses import StreamingResponse
import io

from config.get_db import get_db
from models.phone_models import PhoneData, BatchTask, Device
from schemas.phone_schemas import (
    PhoneDataResponse, PhoneDataCreate, PhoneDataUpdate,
    BatchTaskResponse, DeviceResponse, StatisticsResponse
)
from utils.response import success, fail
from utils.phone_processor import PhoneProcessor

router = APIRouter(prefix="/phone", tags=["电话数据管理"])

# 电话处理器实例
phone_processor = PhoneProcessor()


@router.get("/list", summary="获取电话号码列表")
async def get_phone_list(
    page: int = 1,
    page_size: int = 20,
    phone_number: Optional[str] = None,
    province: Optional[str] = None,
    city: Optional[str] = None,
    status: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    获取电话号码数据列表
    """
    try:
        query = db.query(PhoneData)
        
        # 添加筛选条件
        if phone_number:
            query = query.filter(PhoneData.phone_number.like(f"%{phone_number}%"))
        if province:
            query = query.filter(PhoneData.province == province)
        if city:
            query = query.filter(PhoneData.city == city)
        if status:
            query = query.filter(PhoneData.status == status)
        
        # 分页
        total = query.count()
        phones = query.offset((page - 1) * page_size).limit(page_size).all()
        
        return success(data={
            "rows": [PhoneDataResponse.from_orm(phone).dict() for phone in phones],
            "total": total,
            "page": page,
            "page_size": page_size
        })
        
    except Exception as e:
        return fail(message=f"获取数据失败: {str(e)}")


@router.get("/{phone_id}", summary="获取电话号码详情")
async def get_phone_detail(phone_id: int, db: Session = Depends(get_db)):
    """
    获取电话号码详情
    """
    try:
        phone = db.query(PhoneData).filter(PhoneData.id == phone_id).first()
        if not phone:
            return fail(message="电话号码不存在")
        
        return success(data=PhoneDataResponse.from_orm(phone).dict())
        
    except Exception as e:
        return fail(message=f"获取详情失败: {str(e)}")


@router.post("/add", summary="添加电话号码")
async def add_phone_data(phone_data: PhoneDataCreate, db: Session = Depends(get_db)):
    """
    添加电话号码数据
    """
    try:
        # 检查号码是否已存在
        existing = db.query(PhoneData).filter(
            PhoneData.phone_number == phone_data.phone_number
        ).first()
        
        if existing:
            return fail(message="电话号码已存在")
        
        # 创建新记录
        new_phone = PhoneData(**phone_data.dict())
        db.add(new_phone)
        db.commit()
        db.refresh(new_phone)
        
        return success(data=PhoneDataResponse.from_orm(new_phone).dict())
        
    except Exception as e:
        db.rollback()
        return fail(message=f"添加失败: {str(e)}")


@router.put("/edit", summary="编辑电话号码")
async def edit_phone_data(phone_data: PhoneDataUpdate, db: Session = Depends(get_db)):
    """
    编辑电话号码数据
    """
    try:
        phone = db.query(PhoneData).filter(PhoneData.id == phone_data.id).first()
        if not phone:
            return fail(message="电话号码不存在")
        
        # 更新数据
        for key, value in phone_data.dict(exclude_unset=True).items():
            if key != 'id':
                setattr(phone, key, value)
        
        phone.updated_at = datetime.now()
        db.commit()
        db.refresh(phone)
        
        return success(data=PhoneDataResponse.from_orm(phone).dict())
        
    except Exception as e:
        db.rollback()
        return fail(message=f"编辑失败: {str(e)}")


@router.delete("/delete", summary="删除电话号码")
async def delete_phone_data(ids: List[int], db: Session = Depends(get_db)):
    """
    批量删除电话号码数据
    """
    try:
        deleted_count = db.query(PhoneData).filter(PhoneData.id.in_(ids)).delete()
        db.commit()
        
        return success(data={"deleted_count": deleted_count})
        
    except Exception as e:
        db.rollback()
        return fail(message=f"删除失败: {str(e)}")


@router.get("/import_template", summary="下载电话号码导入模板")
async def download_phone_import_template():
    """
    下载电话号码导入模板
    """
    try:
        # 创建模板数据 - 符合页面说明：第一列为号码
        template_data = {
            '电话号码': [
                '13800138000',
                '13800138001',
                '13800138002',
                '13900139000',
                '13900139001',
                '010-12345678',
                '021-87654321',
                '0755-88888888',
                '0571-12345678',
                '0592-87654321'
            ]
        }

        # 创建DataFrame
        df = pd.DataFrame(template_data)

        # 生成Excel文件
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='电话号码导入模板', index=False)

            # 添加说明工作表
            instructions = pd.DataFrame({
                '导入说明': [
                    '支持格式：',
                    '• Excel (.xlsx, .xls)、CSV (.csv)、文本 (.txt) 格式',
                    '• Excel文件请将号码放在第一列，可包含标题行',
                    '• CSV文件请使用逗号分隔，第一列为号码',
                    '• 文本文件每行一个号码',
                    '',
                    '格式要求：',
                    '• 手机号码：11位数字，如13800138000',
                    '• 固定电话：区号-号码，如010-12345678',
                    '• 单次最多支持10万个号码',
                    '',
                    '系统功能：',
                    '• 系统会自动去重和格式验证',
                    '• 支持断点续传，意外中断可继续处理',
                    '• 文件大小不超过100MB'
                ]
            })
            instructions.to_excel(writer, sheet_name='导入说明', index=False)

        output.seek(0)

        # 返回文件流
        return StreamingResponse(
            io.BytesIO(output.getvalue()),
            media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            headers={
                "Content-Disposition": f"attachment; filename=电话号码导入模板_{datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx"
            }
        )

    except Exception as e:
        return fail(message=f"生成模板失败: {str(e)}")


@router.post("/batch_import", summary="批量导入电话号码")
async def batch_import_phone_data(
    file: UploadFile = File(...),
    config: str = Form("{}"),
    db: Session = Depends(get_db)
):
    """
    批量导入电话号码数据
    """
    try:
        # 解析配置
        import_config = json.loads(config)
        batch_size = import_config.get('batch_size', 1000)
        auto_process = import_config.get('auto_process', True)
        phone_type = import_config.get('phone_type', 'auto')
        description = import_config.get('description', '')
        
        # 创建批量任务
        task = BatchTask(
            task_name=f"批量导入-{datetime.now().strftime('%Y%m%d%H%M%S')}",
            task_type="import",
            file_name=file.filename,
            file_size=0,  # 实际应该获取文件大小
            status="pending",
            config=import_config,
            created_at=datetime.now()
        )
        
        db.add(task)
        db.commit()
        db.refresh(task)
        
        # 异步处理文件
        asyncio.create_task(process_import_file(
            file, task.id, batch_size, auto_process, phone_type, db
        ))
        
        return success(data={
            "task_id": task.id,
            "message": "导入任务已创建，正在后台处理"
        })
        
    except Exception as e:
        return fail(message=f"导入失败: {str(e)}")


async def process_import_file(
    file: UploadFile, 
    task_id: int, 
    batch_size: int, 
    auto_process: bool, 
    phone_type: str,
    db: Session
):
    """
    异步处理导入文件
    """
    try:
        # 更新任务状态
        task = db.query(BatchTask).filter(BatchTask.id == task_id).first()
        task.status = "running"
        task.start_time = datetime.now()
        db.commit()
        
        # 读取文件内容
        content = await file.read()
        
        # 根据文件类型解析数据
        if file.filename.endswith(('.xlsx', '.xls')):
            df = pd.read_excel(content)
        elif file.filename.endswith('.csv'):
            df = pd.read_csv(content)
        elif file.filename.endswith('.txt'):
            lines = content.decode('utf-8').strip().split('\n')
            df = pd.DataFrame({'phone_number': lines})
        else:
            raise ValueError("不支持的文件格式")
        
        # 获取电话号码列
        phone_numbers = df.iloc[:, 0].astype(str).tolist()
        total_count = len(phone_numbers)
        
        # 更新任务总数
        task.total_count = total_count
        db.commit()
        
        # 批量处理
        success_count = 0
        failed_count = 0
        
        for i in range(0, total_count, batch_size):
            batch_phones = phone_numbers[i:i + batch_size]
            
            for phone_number in batch_phones:
                try:
                    # 检查号码是否已存在
                    existing = db.query(PhoneData).filter(
                        PhoneData.phone_number == phone_number
                    ).first()
                    
                    if not existing:
                        # 创建新记录
                        phone_data = PhoneData(
                            phone_number=phone_number,
                            phone_type=phone_type if phone_type != 'auto' else 'mobile',
                            status='pending',
                            batch_id=task_id,
                            created_at=datetime.now()
                        )
                        
                        # 如果启用自动处理，进行号码识别
                        if auto_process:
                            result = await phone_processor.process_phone(phone_number)
                            if result:
                                phone_data.province = result.get('province', '')
                                phone_data.city = result.get('city', '')
                                phone_data.area_code = result.get('area_code', '')
                                phone_data.operator = result.get('operator', '')
                                phone_data.mark_count = result.get('mark_count', 0)
                                phone_data.mark_type = result.get('mark_type', '')
                                phone_data.recognition_result = json.dumps(result)
                                phone_data.confidence_score = result.get('confidence_score', 0)
                                phone_data.status = 'completed'
                        
                        db.add(phone_data)
                        success_count += 1
                    else:
                        failed_count += 1
                        
                except Exception as e:
                    failed_count += 1
                    print(f"处理号码 {phone_number} 失败: {str(e)}")
            
            # 更新进度
            processed_count = min(i + batch_size, total_count)
            progress = (processed_count / total_count) * 100
            
            task.processed_count = processed_count
            task.success_count = success_count
            task.failed_count = failed_count
            task.progress = progress
            db.commit()
        
        # 完成任务
        task.status = "completed"
        task.end_time = datetime.now()
        db.commit()
        
    except Exception as e:
        # 任务失败
        task.status = "failed"
        task.error_message = str(e)
        task.end_time = datetime.now()
        db.commit()


@router.get("/statistics", summary="获取统计数据")
async def get_phone_statistics(
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    获取电话号码统计数据
    """
    try:
        query = db.query(PhoneData)
        
        # 添加日期筛选
        if start_date:
            query = query.filter(PhoneData.created_at >= datetime.fromisoformat(start_date))
        if end_date:
            query = query.filter(PhoneData.created_at <= datetime.fromisoformat(end_date))
        
        # 基础统计
        total_count = query.count()
        completed_count = query.filter(PhoneData.status == 'completed').count()
        pending_count = query.filter(PhoneData.status == 'pending').count()
        
        # 按省份统计
        province_stats = db.execute("""
            SELECT province, COUNT(*) as count 
            FROM phone_data 
            WHERE province != '' 
            GROUP BY province 
            ORDER BY count DESC 
            LIMIT 10
        """).fetchall()
        
        # 按标记类型统计
        mark_type_stats = db.execute("""
            SELECT mark_type, COUNT(*) as count 
            FROM phone_data 
            WHERE mark_type != '' 
            GROUP BY mark_type 
            ORDER BY count DESC
        """).fetchall()
        
        return success(data={
            "total_count": total_count,
            "completed_count": completed_count,
            "pending_count": pending_count,
            "province_stats": [{"province": row[0], "count": row[1]} for row in province_stats],
            "mark_type_stats": [{"mark_type": row[0], "count": row[1]} for row in mark_type_stats]
        })
        
    except Exception as e:
        return fail(message=f"获取统计数据失败: {str(e)}")


@router.get("/export", summary="导出电话号码数据")
async def export_phone_data(
    format: str = "xlsx",
    phone_number: Optional[str] = None,
    province: Optional[str] = None,
    city: Optional[str] = None,
    status: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    导出电话号码数据为Excel或CSV文件
    支持按号码、地区、状态等筛选
    :param format: 导出文件格式（xlsx/csv）
    :param phone_number: 电话号码筛选
    :param province: 省份筛选
    :param city: 城市筛选
    :param status: 状态筛选
    :param db: 数据库会话
    :return: 文件流响应
    """
    # 1. 查询数据库，应用筛选条件
    query = db.query(PhoneData)
    if phone_number:
        query = query.filter(PhoneData.phone_number.like(f"%{phone_number}%"))
    if province:
        query = query.filter(PhoneData.province == province)
    if city:
        query = query.filter(PhoneData.city == city)
    if status:
        query = query.filter(PhoneData.status == status)
    phone_list = query.all()

    # 2. 转换为DataFrame
    data = [PhoneDataResponse.from_orm(phone).dict() for phone in phone_list]
    df = pd.DataFrame(data)

    # 3. 生成文件流
    output = io.BytesIO()
    if format == "csv":
        # 导出为CSV格式
        df.to_csv(output, index=False, encoding="utf-8-sig")
        content_type = "text/csv"
        file_ext = "csv"
    else:
        # 默认导出为Excel格式
        df.to_excel(output, index=False)
        content_type = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        file_ext = "xlsx"
    output.seek(0)

    # 4. 返回StreamingResponse，设置合适的响应头
    return StreamingResponse(
        output,
        media_type=content_type,
        headers={
            "Content-Disposition": f"attachment; filename=phone_data_export.{file_ext}"
        }
    )
