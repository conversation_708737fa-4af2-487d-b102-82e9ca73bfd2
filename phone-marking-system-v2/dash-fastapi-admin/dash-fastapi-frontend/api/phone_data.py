"""
电话号码数据管理API
"""
import requests
import json
from config.env import ApiConfig
from utils.request import api_request


def get_phone_data_list_api(page_obj, query_params=None):
    """
    获取电话号码数据列表
    """
    params = {
        'page': page_obj.get('page'),
        'page_size': page_obj.get('page_size'),
    }
    if query_params:
        params.update(query_params)
    
    return api_request(
        url=f"{ApiConfig.base_url}/phone/list",
        method='get',
        params=params
    )


def get_phone_data_detail_api(phone_id):
    """
    获取电话号码详情
    """
    return api_request(
        url=f"{ApiConfig.base_url}/phone/{phone_id}",
        method='get'
    )


def add_phone_data_api(phone_data):
    """
    添加电话号码数据
    """
    return api_request(
        url=f"{ApiConfig.base_url}/phone/add",
        method='post',
        json=phone_data
    )


def edit_phone_data_api(phone_data):
    """
    编辑电话号码数据
    """
    return api_request(
        url=f"{ApiConfig.base_url}/phone/edit",
        method='put',
        json=phone_data
    )


def delete_phone_data_api(phone_ids):
    """
    删除电话号码数据
    """
    return api_request(
        url=f"{ApiConfig.base_url}/phone/delete",
        method='delete',
        json={'ids': phone_ids}
    )


def download_phone_import_template_api():
    """
    下载电话号码导入模板
    """
    return api_request(
        url=f"{ApiConfig.base_url}/phone/import_template",
        method='get',
        is_headers=True,
        stream=True
    )


def batch_import_phone_data_api(file_data, config=None):
    """
    批量导入电话号码数据
    """
    files = {'file': file_data}
    data = {'config': json.dumps(config or {})}

    return api_request(
        url=f"{ApiConfig.base_url}/phone/batch_import",
        method='post',
        files=files,
        data=data
    )


def export_phone_data_api(query_params=None, export_format='xlsx'):
    """
    导出电话号码数据
    """
    params = {
        'format': export_format
    }
    if query_params:
        params.update(query_params)

    return api_request(
        url=f"{ApiConfig.base_url}/phone/export",
        method='get',
        params=params
    )


def get_phone_statistics_api(date_range=None):
    """
    获取电话号码统计数据
    """
    params = {}
    if date_range:
        params.update(date_range)

    return api_request(
        url=f"{ApiConfig.base_url}/phone/statistics",
        method='get',
        params=params
    )


def process_phone_batch_api(batch_id):
    """
    处理电话号码批次
    """
    return api_request(
        url=f"{ApiConfig.base_url}/phone/process_batch",
        method='post',
        json={'batch_id': batch_id}
    )


def get_batch_task_list_api(page_obj, query_params=None):
    """
    获取批量任务列表
    """
    params = {
        'page': page_obj.get('page'),
        'page_size': page_obj.get('page_size'),
    }
    if query_params:
        params.update(query_params)
    
    return api_request(
        url=f"{ApiConfig.base_url}/batch_task/list",
        method='get',
        params=params
    )


def get_batch_task_detail_api(task_id):
    """
    获取批量任务详情
    """
    return api_request(
        url=f"{ApiConfig.base_url}/batch_task/{task_id}",
        method='get'
    )


def cancel_batch_task_api(task_id):
    """
    取消批量任务
    """
    return api_request(
        url=f"{ApiConfig.base_url}/batch_task/{task_id}/cancel",
        method='post'
    )


def restart_batch_task_api(task_id):
    """
    重启批量任务
    """
    return api_request(
        url=f"{ApiConfig.base_url}/batch_task/{task_id}/restart",
        method='post'
    )


def get_device_list_api(page_obj, query_params=None):
    """
    获取设备列表
    """
    params = {
        'page': page_obj.get('page'),
        'page_size': page_obj.get('page_size'),
    }
    if query_params:
        params.update(query_params)
    
    return api_request(
        url=f"{ApiConfig.base_url}/device/list",
        method='get',
        params=params
    )


def get_device_status_api():
    """
    获取设备状态统计
    """
    return api_request(
        url=f"{ApiConfig.base_url}/device/status",
        method='get'
    )


def connect_device_api(device_id):
    """
    连接设备
    """
    return api_request(
        url=f"{ApiConfig.base_url}/device/{device_id}/connect",
        method='post'
    )


def disconnect_device_api(device_id):
    """
    断开设备连接
    """
    return api_request(
        url=f"{ApiConfig.base_url}/device/{device_id}/disconnect",
        method='post'
    )


def sync_data_api(sync_config):
    """
    数据同步
    """
    return api_request(
        url=f"{ApiConfig.base_url}/sync/start",
        method='post',
        json=sync_config
    )


def get_sync_status_api():
    """
    获取同步状态
    """
    return api_request(
        url=f"{ApiConfig.base_url}/sync/status",
        method='get'
    )


def get_system_logs_api(page_obj, query_params=None):
    """
    获取系统日志
    """
    params = {
        'page': page_obj.get('page'),
        'page_size': page_obj.get('page_size'),
    }
    if query_params:
        params.update(query_params)

    return api_request(
        url=f"{ApiConfig.base_url}/logs/list",
        method='get',
        params=params
    )
