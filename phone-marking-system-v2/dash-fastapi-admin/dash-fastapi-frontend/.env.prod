# -------- 应用配置 --------
# 应用运行环境
APP_ENV = 'prod'
# 应用名称
APP_NAME = '通用后台管理系统'
# 应用请求后端url
APP_BASE_URL = 'http://127.0.0.1:9099'
# 后端是否使用代理模式
APP_IS_PROXY = true
# 应用代理路径
APP_PROXY_PATH = '/prod-api'
# 应用秘钥
APP_SECRET_KEY = 'Dash-FastAPI-Admin'
# 应用主机
APP_HOST = '0.0.0.0'
# 应用端口
APP_PORT = 8088
# 应用是否开启debug模式
APP_DEBUG = false
# flask-compress压缩配置
APP_COMPRESS_ALGORITHM = 'br'
APP_COMPRESS_BR_LEVEL = 11