"""
电话标记系统视图模块
"""

from .phone_dashboard import render_phone_dashboard
from .batch_import import render_batch_import
from .device_management import render_device_management
from .phone_data import render_phone_data
from .task_monitor import render_task_monitor
from .data_export import render_data_export
from .data_statistics import render_data_statistics
from .task_history import render_task_history
from .device_monitor import render_device_monitor
from .system_config import render_system_config
from .data_sync import render_data_sync

# 为了兼容系统的动态调用，创建子模块类
class phone_data:
    @staticmethod
    def render(button_perms=None, role_perms=None):
        return render_phone_data(button_perms, role_perms)

class batch_import:
    @staticmethod
    def render(button_perms=None, role_perms=None):
        return render_batch_import(button_perms, role_perms)

class data_export:
    @staticmethod
    def render(button_perms=None, role_perms=None):
        return render_data_export(button_perms, role_perms)

class data_statistics:
    @staticmethod
    def render(button_perms=None, role_perms=None):
        return render_data_statistics(button_perms, role_perms)

class task_monitor:
    @staticmethod
    def render(button_perms=None, role_perms=None):
        return render_task_monitor(button_perms, role_perms)

class task_history:
    @staticmethod
    def render(button_perms=None, role_perms=None):
        return render_task_history(button_perms, role_perms)

class device_management:
    @staticmethod
    def render(button_perms=None, role_perms=None):
        return render_device_management(button_perms, role_perms)

class device_monitor:
    @staticmethod
    def render(button_perms=None, role_perms=None):
        return render_device_monitor(button_perms, role_perms)

class system_config:
    @staticmethod
    def render(button_perms=None, role_perms=None):
        return render_system_config(button_perms, role_perms)

class data_sync:
    @staticmethod
    def render(button_perms=None, role_perms=None):
        return render_data_sync(button_perms, role_perms)

__all__ = [
    'render_phone_dashboard',
    'render_batch_import',
    'render_device_management',
    'render_phone_data',
    'render_task_monitor',
    'render_data_export',
    'render_data_statistics',
    'render_task_history',
    'render_device_monitor',
    'render_system_config',
    'render_data_sync'
]
