"""
电话号码数据管理页面 - 支持高性能分页查询
"""
import dash
from dash import dcc, html, Input, Output, State, callback, ctx, ALL
import feffery_antd_components as fac
import feffery_utils_components as fuc
from datetime import datetime, date
import pandas as pd
import json
import webbrowser

from server import app
from api.phone_data import *


def render_phone_data(button_perms=None, role_perms=None):
    """
    渲染电话号码数据管理页面
    """
    return [
        # 页面标题
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdBreadcrumb(
                    items=[
                        {'title': '电话标记系统'},
                        {'title': '号码数据'}
                    ]
                )
            ], span=24)
        ], style={'marginBottom': 16}),
        
        # 查询表单
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdForm([
                        fac.AntdRow([
                            fac.AntdCol([
                                fac.AntdFormItem([
                                    fac.AntdInput(
                                        id='phone-data-phone-number-input',
                                        placeholder='请输入电话号码',
                                        allowClear=True
                                    )
                                ], label='电话号码')
                            ], span=6),
                            
                            fac.AntdCol([
                                fac.AntdFormItem([
                                    fac.AntdSelect(
                                        id='phone-data-province-select',
                                        placeholder='请选择省份',
                                        options=[
                                            {'label': '全部', 'value': ''},
                                            {'label': '北京', 'value': '北京'},
                                            {'label': '上海', 'value': '上海'},
                                            {'label': '广东', 'value': '广东'},
                                            {'label': '浙江', 'value': '浙江'},
                                            {'label': '江苏', 'value': '江苏'},
                                            {'label': '山东', 'value': '山东'},
                                            {'label': '四川', 'value': '四川'},
                                            {'label': '湖北', 'value': '湖北'}
                                        ],
                                        value='',
                                        allowClear=True
                                    )
                                ], label='省份')
                            ], span=6),
                            
                            fac.AntdCol([
                                fac.AntdFormItem([
                                    fac.AntdSelect(
                                        id='phone-data-status-select',
                                        placeholder='请选择状态',
                                        options=[
                                            {'label': '全部', 'value': ''},
                                            {'label': '待处理', 'value': 'pending'},
                                            {'label': '处理中', 'value': 'processing'},
                                            {'label': '已完成', 'value': 'completed'},
                                            {'label': '失败', 'value': 'failed'}
                                        ],
                                        value='',
                                        allowClear=True
                                    )
                                ], label='状态')
                            ], span=6),
                            
                            fac.AntdCol([
                                fac.AntdFormItem([
                                    fac.AntdSpace([
                                        fac.AntdButton(
                                            '查询',
                                            id='phone-data-search-btn',
                                            type='primary',
                                            icon=fac.AntdIcon(icon='antd-search')
                                        ),
                                        fac.AntdButton(
                                            '重置',
                                            id='phone-data-reset-btn',
                                            icon=fac.AntdIcon(icon='antd-sync')
                                        )
                                    ])
                                ])
                            ], span=6)
                        ], gutter=16)
                    ], layout='inline')
                ], size='small')
            ], span=24)
        ], style={'marginBottom': 16}),
        
        # 操作按钮区域
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdSpace([
                    fac.AntdButton(
                        '新增',
                        id='phone-data-add-btn',
                        type='primary',
                        icon=fac.AntdIcon(icon='antd-plus')
                    ),
                    fac.AntdButton(
                        '批量删除',
                        id='phone-data-batch-delete-btn',
                        danger=True,
                        icon=fac.AntdIcon(icon='antd-delete')
                    ),
                    fac.AntdButton(
                        '导出',
                        id='phone-data-export-btn',
                        icon=fac.AntdIcon(icon='antd-download')
                    ),
                    fac.AntdButton(
                        '刷新',
                        id='phone-data-refresh-btn',
                        icon=fac.AntdIcon(icon='antd-reload')
                    )
                ])
            ], span=24)
        ], style={'marginBottom': 16}),
        
        # 数据表格
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdTable(
                        id='phone-data-table',
                        columns=[
                            {
                                'title': '选择',
                                'dataIndex': 'selection',
                                'width': 50,
                                'renderOptions': {'renderType': 'checkbox'}
                            },
                            {'title': '电话号码', 'dataIndex': 'phone_number', 'width': 120},
                            {'title': '号码类型', 'dataIndex': 'phone_type', 'width': 80},
                            {'title': '省份', 'dataIndex': 'province', 'width': 80},
                            {'title': '城市', 'dataIndex': 'city', 'width': 80},
                            {'title': '运营商', 'dataIndex': 'operator', 'width': 80},
                            {'title': '标记数量', 'dataIndex': 'mark_count', 'width': 80},
                            {'title': '标记类型', 'dataIndex': 'mark_type', 'width': 100},
                            {'title': '置信度', 'dataIndex': 'confidence_score', 'width': 80},
                            {'title': '状态', 'dataIndex': 'status', 'width': 80},
                            {'title': '创建时间', 'dataIndex': 'created_at', 'width': 150},
                            {'title': '操作', 'dataIndex': 'operation', 'width': 150}
                        ],
                        data=[],
                        pagination={
                            'pageSize': 20,
                            'current': 1,
                            'showSizeChanger': True,
                            'pageSizeOptions': [10, 20, 50, 100],
                            'showQuickJumper': True,
                            'total': 0
                        },
                        rowSelectionType='checkbox',
                        selectedRowKeys=[],
                        bordered=True,
                        size='small'
                    )
                ])
            ], span=24)
        ]),
        
        # 新增/编辑模态框
        fac.AntdModal(
            id='phone-data-modal',
            title='新增号码数据',
            visible=False,
            width=600,
            children=[
                fac.AntdForm([
                    fac.AntdFormItem([
                        fac.AntdInput(
                            id='phone-data-phone-number-modal-input',
                            placeholder='请输入电话号码'
                        )
                    ], label='电话号码'),
                    fac.AntdFormItem([
                        fac.AntdSelect(
                            id='phone-data-phone-type-modal-select',
                            placeholder='请选择号码类型',
                            options=[
                                {'label': '手机', 'value': '手机'},
                                {'label': '固话', 'value': '固话'}
                            ]
                        )
                    ], label='号码类型'),
                    fac.AntdFormItem([
                        fac.AntdSelect(
                            id='phone-data-province-modal-select',
                            placeholder='请选择省份',
                            options=[
                                {'label': '北京', 'value': '北京'},
                                {'label': '上海', 'value': '上海'},
                                {'label': '广东', 'value': '广东'},
                                {'label': '浙江', 'value': '浙江'},
                                {'label': '江苏', 'value': '江苏'}
                            ]
                        )
                    ], label='省份'),
                    fac.AntdFormItem([
                        fac.AntdSelect(
                            id='phone-data-city-modal-select',
                            placeholder='请选择城市',
                            options=[
                                {'label': '北京', 'value': '北京'},
                                {'label': '上海', 'value': '上海'},
                                {'label': '广州', 'value': '广州'},
                                {'label': '杭州', 'value': '杭州'},
                                {'label': '南京', 'value': '南京'}
                            ]
                        )
                    ], label='城市'),
                    fac.AntdFormItem([
                        fac.AntdSelect(
                            id='phone-data-operator-modal-select',
                            placeholder='请选择运营商',
                            options=[
                                {'label': '中国移动', 'value': '中国移动'},
                                {'label': '中国联通', 'value': '中国联通'},
                                {'label': '中国电信', 'value': '中国电信'}
                            ]
                        )
                    ], label='运营商'),
                    fac.AntdFormItem([
                        fac.AntdInputNumber(
                            id='phone-data-mark-count-modal-input',
                            placeholder='请输入标记次数',
                            min=0
                        )
                    ], label='标记次数'),
                    fac.AntdFormItem([
                        fac.AntdInput(
                            id='phone-data-mark-type-modal-select',
                            placeholder='请输入标记类型'
                        )
                    ], label='标记类型')
                ], layout='vertical')
            ],
            okText='确定',
            cancelText='取消'
        ),
        
        # 消息提示容器
        html.Div(id='phone-data-message-container'),
        
        # 存储组件
        dcc.Store(id='phone-data-search-params', data={}),
        dcc.Store(id='phone-data-edit-id-store', data=None)  # 新增这行
    ]


# 高性能分页查询回调
@app.callback(
    [Output('phone-data-table', 'data'),
     Output('phone-data-table', 'pagination'),
     Output('phone-data-table-spin', 'spinning'),
     Output('phone-data-total-count', 'value'),
     Output('phone-data-completed-count', 'value'),
     Output('phone-data-processing-count', 'value'),
     Output('phone-data-failed-count', 'value'),
     Output('phone-data-search-params', 'data'),
     Output('phone-data-message-container', 'children')],
    [Input('phone-data-search-btn', 'n_clicks'),
     Input('phone-data-refresh-btn', 'n_clicks'),
     Input('phone-data-table', 'pagination'),
     Input('phone-data-table', 'sorter'),
     Input('phone-data-page-size-select', 'value'),
     Input('phone-data-auto-refresh', 'n_intervals')],
    [State('phone-data-phone-number-input', 'value'),
     State('phone-data-province-select', 'value'),
     State('phone-data-city-select', 'value'),
     State('phone-data-operator-select', 'value'),
     State('phone-data-mark-type-select', 'value'),
     State('phone-data-status-select', 'value'),
     State('phone-data-mark-count-range', 'value'),
     State('phone-data-date-range', 'value'),
     State('phone-data-search-params', 'data')],
    prevent_initial_call=False
)
def search_phone_data_with_performance(
    search_clicks, refresh_clicks, pagination, sorter, page_size_select, auto_refresh,
    phone_number, province, city, operator, mark_type, status, mark_count_range, date_range,
    stored_params
):
    """
    高性能分页查询电话号码数据
    支持排序、筛选、自动刷新等功能
    """
    try:
        # 开始加载
        spinning = True

        # 构建查询参数
        query_params = {}
        if phone_number:
            # 支持模糊查询和通配符
            query_params['phone_number'] = phone_number.replace('*', '%')
        if province:
            query_params['province'] = province
        if city:
            query_params['city'] = city
        if operator:
            query_params['operator'] = operator
        if mark_type:
            query_params['mark_type'] = mark_type
        if status:
            query_params['status'] = status
        if mark_count_range and len(mark_count_range) == 2:
            query_params['mark_count_min'] = mark_count_range[0]
            query_params['mark_count_max'] = mark_count_range[1]
        if date_range and len(date_range) == 2:
            query_params['start_date'] = date_range[0]
            query_params['end_date'] = date_range[1]

        # 分页参数
        page = pagination.get('current', 1) if pagination else 1
        page_size = page_size_select or pagination.get('pageSize', 20) if pagination else 20

        # 排序参数
        if sorter:
            if sorter.get('order') == 'ascend':
                query_params['order_by'] = sorter.get('field')
                query_params['order_direction'] = 'asc'
            elif sorter.get('order') == 'descend':
                query_params['order_by'] = sorter.get('field')
                query_params['order_direction'] = 'desc'

        # 性能优化：检查是否需要重新查询
        current_params = {
            'query': query_params,
            'page': page,
            'page_size': page_size
        }

        # 如果参数没有变化且不是手动刷新，使用缓存数据
        if (stored_params == current_params and
            ctx.triggered_id not in ['phone-data-search-btn', 'phone-data-refresh-btn']):
            return dash.no_update, dash.no_update, False, dash.no_update, dash.no_update, dash.no_update, dash.no_update, current_params, None

        # 调用真实API获取数据
        response = get_phone_data_list_api({'page': page, 'page_size': page_size}, query_params)

        if response and response.get('code') == 200:
            data = response.get('data', {})
            phones = data.get('rows', [])
            total_count = data.get('total', 0)

            # 转换数据格式
            table_data = []
            for phone in phones:
                table_data.append({
                    'key': str(phone.get('id')),
                    'phone_number': phone.get('phone_number', ''),
                    'phone_type': phone.get('phone_type', ''),
                    'province': phone.get('province', ''),
                    'city': phone.get('city', ''),
                    'area_code': phone.get('area_code', ''),
                    'operator': phone.get('operator', ''),
                    'mark_count': phone.get('mark_count', 0),
                    'mark_type': phone.get('mark_type', ''),
                    'confidence_score': f"{phone.get('confidence_score', 0):.1f}%",
                    'status': fac.AntdTag(
                        content={'pending': '待处理', 'processing': '处理中', 'completed': '已完成', 'failed': '失败'}.get(phone.get('status', 'pending'), phone.get('status', 'pending')),
                        color={'pending': 'orange', 'processing': 'blue', 'completed': 'green', 'failed': 'red'}.get(phone.get('status', 'pending'), 'default')
                    ),
                    'created_at': phone.get('created_at', ''),
                    'operation': fac.AntdSpace([
                        fac.AntdButton(
                            '详情',
                            id={'type': 'phone-data-detail-btn', 'index': phone.get('id')},
                            type='link',
                            size='small',
                            icon=fac.AntdIcon(icon='antd-eye')
                        ),
                        fac.AntdButton(
                            '编辑',
                            id={'type': 'phone-data-edit-btn', 'index': phone.get('id')},
                            type='link',
                            size='small',
                            icon=fac.AntdIcon(icon='antd-edit')
                        ),
                        fac.AntdButton(
                            '删除',
                            id={'type': 'phone-data-delete-btn', 'index': phone.get('id')},
                            type='link',
                            size='small',
                            danger=True,
                            icon=fac.AntdIcon(icon='antd-delete')
                        )
                    ])
                })

            # 计算统计数据
            completed_count = len([p for p in phones if p.get('status') == 'completed'])
            processing_count = len([p for p in phones if p.get('status') == 'processing'])
            failed_count = len([p for p in phones if p.get('status') == 'failed'])

        else:
            # API调用失败，返回空数据
            table_data = []
            total_count = 0
            completed_count = 0
            processing_count = 0
            failed_count = 0



        # 更新分页信息
        new_pagination = {
            'current': page,
            'pageSize': page_size,
            'total': total_count,
            'showSizeChanger': True,
            'showQuickJumper': True,
            'pageSizeOptions': [10, 20, 50, 100],
            'showLessItems': True
        }

        return (
            table_data,
            new_pagination,
            False,  # 停止加载
            total_count,
            completed_count,
            processing_count,
            failed_count,
            current_params,
            None
        )

    except Exception as e:
        return (
            [],
            {'current': 1, 'pageSize': 20, 'total': 0},
            False,
            0, 0, 0, 0,
            {},
            fac.AntdMessage(content=f'查询失败: {str(e)}', type='error')
        )


# 重置查询条件回调
@app.callback(
    [Output('phone-data-phone-number-input', 'value'),
     Output('phone-data-province-select', 'value'),
     Output('phone-data-city-select', 'value'),
     Output('phone-data-operator-select', 'value'),
     Output('phone-data-mark-type-select', 'value'),
     Output('phone-data-status-select', 'value'),
     Output('phone-data-mark-count-range', 'value'),
     Output('phone-data-date-range', 'value')],
    [Input('phone-data-reset-btn', 'n_clicks')],
    prevent_initial_call=True
)
def reset_search_form(n_clicks):
    """
    重置查询表单
    """
    if n_clicks:
        return '', '', '', '', '', '', [0, 100], None
    return dash.no_update, dash.no_update, dash.no_update, dash.no_update, dash.no_update, dash.no_update, dash.no_update, dash.no_update


# 省份城市联动回调
@app.callback(
    Output('phone-data-city-select', 'options'),
    [Input('phone-data-province-select', 'value')],
    prevent_initial_call=True
)
def update_city_options(province):
    """
    根据省份更新城市选项
    """
    city_map = {
        '北京': [{'label': '北京', 'value': '北京'}],
        '上海': [{'label': '上海', 'value': '上海'}],
        '广东': [
            {'label': '广州', 'value': '广州'},
            {'label': '深圳', 'value': '深圳'},
            {'label': '珠海', 'value': '珠海'},
            {'label': '东莞', 'value': '东莞'}
        ],
        '浙江': [
            {'label': '杭州', 'value': '杭州'},
            {'label': '宁波', 'value': '宁波'},
            {'label': '温州', 'value': '温州'}
        ],
        '江苏': [
            {'label': '南京', 'value': '南京'},
            {'label': '苏州', 'value': '苏州'},
            {'label': '无锡', 'value': '无锡'}
        ]
    }

    if province and province in city_map:
        return city_map[province]
    else:
        return []


# 批量操作相关回调
@app.callback(
    Output('phone-data-batch-delete-btn', 'disabled'),
    [Input('phone-data-table', 'selectedRowKeys')],
    prevent_initial_call=True
)
def update_batch_buttons(selected_keys):
    """
    根据选择的行数更新批量操作按钮状态
    """
    return len(selected_keys) == 0 if selected_keys else True


# 自动刷新控制回调
@app.callback(
    Output('phone-data-auto-refresh', 'disabled'),
    [Input('phone-data-search-btn', 'n_clicks')],
    prevent_initial_call=True
)
def control_auto_refresh(search_clicks):
    """
    控制自动刷新
    查询后启用自动刷新，30秒后自动更新数据
    """
    if search_clicks:
        return False  # 启用自动刷新
    return True


# 导出功能回调
@app.callback(
    Output('phone-data-message-container', 'children', allow_duplicate=True),
    [Input('phone-data-batch-export-btn', 'n_clicks')],
    [State('phone-data-table', 'selectedRowKeys'),
     State('phone-data-search-params', 'data')],
    prevent_initial_call=True
)
def export_phone_data(export_clicks, selected_keys, search_params):
    """
    导出电话号码数据，支持导出选中数据或当前查询结果。
    调用后端/phone/export接口，自动触发浏览器下载。
    """
    if export_clicks:
        try:
            # 1. 构建导出参数
            export_params = {}
            if selected_keys and len(selected_keys) > 0:
                # 导出选中的数据
                export_params['ids'] = selected_keys
            elif search_params and 'query' in search_params:
                # 导出当前查询结果
                export_params.update(search_params['query'])
            # 2. 默认导出为xlsx格式
            export_params['format'] = 'xlsx'

            # 3. 构造导出URL
            from urllib.parse import urlencode
            from dash import get_asset_url
            api_base = '/api' if '/api' in get_asset_url('') else ''
            query_string = urlencode(export_params, doseq=True)
            download_url = f"{api_base}/phone/export?{query_string}"

            # 4. 触发浏览器下载
            import dash
            dash.get_app().clientside_callback(
                "window.open(download_url, '_blank');",
                Output('phone-data-message-container', 'children'),
                Input('phone-data-batch-export-btn', 'n_clicks')
            )

            return fac.AntdMessage(
                content='导出任务已启动，请注意浏览器下载提示',
                type='success'
            )
        except Exception as e:
            return fac.AntdMessage(
                content=f'导出失败: {str(e)}',
                type='error'
            )
    return dash.no_update


# 批量处理回调
@app.callback(
    Output('phone-data-message-container', 'children', allow_duplicate=True),
    [Input('phone-data-batch-process-btn', 'n_clicks')],
    [State('phone-data-table', 'selectedRowKeys')],
    prevent_initial_call=True
)
def batch_process_phone_data(process_clicks, selected_keys):
    """
    批量处理电话号码
    """
    if process_clicks:
        if not selected_keys or len(selected_keys) == 0:
            return fac.AntdMessage(
                content='请先选择要处理的数据',
                type='warning'
            )

        try:
            # 模拟批量处理API调用
            # response = batch_process_phone_data_api(selected_keys)

            return fac.AntdMessage(
                content=f'已提交 {len(selected_keys)} 条记录进行批量处理',
                type='success'
            )

        except Exception as e:
            return fac.AntdMessage(
                content=f'批量处理失败: {str(e)}',
                type='error'
            )

    return dash.no_update

# 新增/编辑模态框控制回调
@app.callback(
    [
        Output('phone-data-modal', 'visible'),
        Output('phone-data-modal', 'title'),
        Output('phone-data-phone-number-modal-input', 'value'),
        Output('phone-data-phone-type-modal-select', 'value'),
        Output('phone-data-province-modal-select', 'value'),
        Output('phone-data-city-modal-select', 'value'),
        Output('phone-data-operator-modal-select', 'value'),
        Output('phone-data-mark-count-modal-input', 'value'),
        Output('phone-data-mark-type-modal-select', 'value'),
        Output('phone-data-edit-id-store', 'data')
    ],
    [
        Input('phone-data-add-btn', 'n_clicks'),
        Input({'type': 'phone-data-operation-button', 'index': 'edit'}, 'n_clicks'),
        Input('phone-data-table', 'nClicksButton')
    ],
    [
        State('phone-data-table', 'clickedContent'),
        State('phone-data-table', 'recentlyButtonClickedRow')
    ],
    prevent_initial_call=True
)
def control_phone_data_modal(add_clicks, edit_clicks, table_button_clicks, 
                           clicked_content, recently_clicked_row):
    """
    控制新增/编辑模态框的显示和数据填充
    """
    trigger_id = ctx.triggered_id
    
    if trigger_id == 'phone-data-add-btn':
        # 新增模式
        return (
            True,  # 显示模态框
            '新增号码数据',  # 标题
            '',    # 清空电话号码
            '',    # 清空号码类型
            '',    # 清空省份
            '',    # 清空城市
            '',    # 清空运营商
            0,     # 清空标记次数
            '',    # 清空标记类型
            None   # 清空编辑ID
        )
    
    elif trigger_id and trigger_id.get('type') == 'phone-data-operation-button' and trigger_id.get('index') == 'edit':
        # 编辑模式
        if recently_clicked_row:
            row_data = recently_clicked_row
            return (
                True,  # 显示模态框
                '编辑号码数据',  # 标题
                row_data.get('phone_number', ''),
                row_data.get('phone_type', ''),
                row_data.get('province', ''),
                row_data.get('city', ''),
                row_data.get('operator', ''),
                row_data.get('mark_count', 0),
                row_data.get('mark_type', ''),
                row_data.get('key')  # 编辑ID
            )
    
    return dash.no_update, dash.no_update, dash.no_update, dash.no_update, dash.no_update, dash.no_update, dash.no_update, dash.no_update, dash.no_update, dash.no_update


# 保存号码数据回调
@app.callback(
    [
        Output('phone-data-modal', 'visible', allow_duplicate=True),
        Output('phone-data-message-container', 'children', allow_duplicate=True),
        Output('phone-data-search-btn', 'n_clicks', allow_duplicate=True)
    ],
    [
        Input('phone-data-modal', 'okCounts')
    ],
    [
        State('phone-data-phone-number-modal-input', 'value'),
        State('phone-data-phone-type-modal-select', 'value'),
        State('phone-data-province-modal-select', 'value'),
        State('phone-data-city-modal-select', 'value'),
        State('phone-data-operator-modal-select', 'value'),
        State('phone-data-mark-count-modal-input', 'value'),
        State('phone-data-mark-type-modal-select', 'value'),
        State('phone-data-edit-id-store', 'data')
    ],
    prevent_initial_call=True
)
def save_phone_data(confirm_clicks, phone_number, phone_type, province, city, 
                   operator, mark_count, mark_type, edit_id):
    """
    保存号码数据（新增或编辑）
    """
    if confirm_clicks:
        try:
            # 数据验证
            if not phone_number:
                return dash.no_update, fac.AntdMessage(content='请输入电话号码', type='warning'), dash.no_update
            
            if not phone_type:
                return dash.no_update, fac.AntdMessage(content='请选择号码类型', type='warning'), dash.no_update
            
            # 构建保存数据
            save_data = {
                'phone_number': phone_number,
                'phone_type': phone_type,
                'province': province,
                'city': city,
                'operator': operator,
                'mark_count': mark_count or 0,
                'mark_type': mark_type
            }
            
            if edit_id:
                # 编辑模式
                save_data['id'] = edit_id
                response = edit_phone_data_api(save_data)
                if response and response.get('code') == 0:
                    message = '号码数据更新成功'
                else:
                    error_msg = response.get('msg', '号码数据更新失败') if response else '号码数据更新失败'
                    return dash.no_update, fac.AntdMessage(content=error_msg, type='error'), dash.no_update
            else:
                # 新增模式
                response = add_phone_data_api(save_data)
                if response and response.get('code') == 0:
                    message = '号码数据添加成功'
                else:
                    error_msg = response.get('msg', '号码数据添加失败') if response else '号码数据添加失败'
                    return dash.no_update, fac.AntdMessage(content=error_msg, type='error'), dash.no_update
            
            # 操作成功，关闭模态框并刷新表格
            return (
                False,  # 关闭模态框
                fac.AntdMessage(content=message, type='success'),
                1  # 触发数据刷新
            )
            
        except Exception as e:
            return (
                dash.no_update,
                fac.AntdMessage(content=f'保存失败: {str(e)}', type='error'),
                dash.no_update
            )
    
    return dash.no_update, dash.no_update, dash.no_update


# 取消模态框回调
@app.callback(
    Output('phone-data-modal', 'visible', allow_duplicate=True),
    [Input('phone-data-modal-cancel-btn', 'n_clicks')],
    prevent_initial_call=True
)
def cancel_phone_data_modal(cancel_counts):
    if cancel_counts:
        return False
    return dash.no_update


# 添加存储组件（如果不存在）
# dcc.Store(id='phone-data-edit-id-store', data=None)

# 在模态框的 renderFooter 部分，确保按钮ID正确
renderFooter=[
    fac.AntdButton(
        '取消',
        id='phone-data-modal-cancel-btn',
        style={'marginRight': 8}
    ),
    fac.AntdButton(
        '确定',
        id='phone-data-modal-confirm-btn',
        type='primary'
    )
]

# 单条删除回调
@app.callback(
    [
        Output('phone-data-message-container', 'children', allow_duplicate=True),
        Output('phone-data-search-btn', 'n_clicks', allow_duplicate=True)
    ],
    [
        Input({'type': 'phone-data-delete-btn', 'index': ALL}, 'n_clicks')
    ],
    [
        State('phone-data-table', 'data'),
        State('phone-data-table', 'pagination')
    ],
    prevent_initial_call=True
)
def delete_single_phone_data(delete_clicks, table_data, pagination):
    ctx_trigger = ctx.triggered_id
    if not ctx_trigger or not table_data:
        return dash.no_update, dash.no_update
    if isinstance(ctx_trigger, dict) and ctx_trigger.get('type') == 'phone-data-delete-btn':
        idx = ctx_trigger.get('index')
        if idx is not None and idx < len(table_data):
            row = table_data[idx]
            phone_id = row.get('key') or row.get('id')
            if not phone_id:
                return fac.AntdMessage(content='未找到要删除的数据ID', type='error'), dash.no_update
            try:
                response = delete_phone_data_api([phone_id])
                if response and response.get('code') == 0:
                    return fac.AntdMessage(content='删除成功', type='success'), 1
                else:
                    error_msg = response.get('msg', '删除失败') if response else '删除失败'
                    return fac.AntdMessage(content=error_msg, type='error'), dash.no_update
            except Exception as e:
                return fac.AntdMessage(content=f'删除异常: {str(e)}', type='error'), dash.no_update
    return dash.no_update, dash.no_update

# 批量删除回调
@app.callback(
    [
        Output('phone-data-message-container', 'children', allow_duplicate=True),
        Output('phone-data-search-btn', 'n_clicks', allow_duplicate=True)
    ],
    [
        Input('phone-data-batch-delete-btn', 'n_clicks')
    ],
    [
        State('phone-data-table', 'selectedRowKeys')
    ],
    prevent_initial_call=True
)
def delete_batch_phone_data(batch_delete_clicks, selected_keys):
    if batch_delete_clicks:
        if not selected_keys or len(selected_keys) == 0:
            return fac.AntdMessage(content='请先选择要删除的数据', type='warning'), dash.no_update
        try:
            response = delete_phone_data_api(selected_keys)
            if response and response.get('code') == 0:
                return fac.AntdMessage(content='批量删除成功', type='success'), 1
            else:
                error_msg = response.get('msg', '批量删除失败') if response else '批量删除失败'
                return fac.AntdMessage(content=error_msg, type='error'), dash.no_update
        except Exception as e:
            return fac.AntdMessage(content=f'批量删除异常: {str(e)}', type='error'), dash.no_update
    return dash.no_update, dash.no_update
