"""
数据导出页面 - 支持多种格式导出
"""
import dash
from dash import dcc, html, Input, Output, State, callback, ctx
from server import app
import feffery_antd_components as fac
import feffery_utils_components as fuc
from datetime import datetime, date
import pandas as pd
import json

# from utils.common_util import validate_data_not_empty


def render_data_export(button_perms=None, role_perms=None):
    """
    渲染数据导出页面
    """
    return [
        # 页面标题
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdBreadcrumb(
                    items=[
                        {'title': '电话标记系统'},
                        {'title': '数据导出'}
                    ]
                )
            ], span=24)
        ], style={'marginBottom': 16}),
        
        # 导出配置卡片
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdTitle('导出配置', level=4),
                    fac.AntdForm([
                        fac.AntdRow([
                            fac.AntdCol([
                                fac.AntdFormItem([
                                    fac.AntdSelect(
                                        id='export-data-type-select',
                                        placeholder='请选择导出数据类型',
                                        options=[
                                            {'label': '全部数据', 'value': 'all'},
                                            {'label': '已完成数据', 'value': 'completed'},
                                            {'label': '处理中数据', 'value': 'processing'},
                                            {'label': '失败数据', 'value': 'failed'},
                                            {'label': '自定义筛选', 'value': 'custom'}
                                        ],
                                        value='all'
                                    )
                                ], label='数据类型')
                            ], span=8),
                            
                            fac.AntdCol([
                                fac.AntdFormItem([
                                    fac.AntdSelect(
                                        id='export-format-select',
                                        placeholder='请选择导出格式',
                                        options=[
                                            {'label': 'Excel (.xlsx)', 'value': 'xlsx'},
                                            {'label': 'CSV (.csv)', 'value': 'csv'},
                                            {'label': 'JSON (.json)', 'value': 'json'},
                                            {'label': 'TXT (.txt)', 'value': 'txt'}
                                        ],
                                        value='xlsx'
                                    )
                                ], label='导出格式')
                            ], span=8),
                            
                            fac.AntdCol([
                                fac.AntdFormItem([
                                    fac.AntdInputNumber(
                                        id='export-limit-input',
                                        placeholder='0表示不限制',
                                        min=0,
                                        max=1000000,
                                        value=10000
                                    )
                                ], label='导出数量限制')
                            ], span=8)
                        ], gutter=16),
                        
                        fac.AntdRow([
                            fac.AntdCol([
                                fac.AntdFormItem([
                                    fac.AntdDatePicker(
                                        id='export-date-range',
                                        placeholder='选择日期范围'
                                    )
                                ], label='时间范围')
                            ], span=12),
                            
                            fac.AntdCol([
                                fac.AntdFormItem([
                                    fac.AntdCheckboxGroup(
                                        id='export-fields-checkbox',
                                        options=[
                                            {'label': '电话号码', 'value': 'phone_number'},
                                            {'label': '号码类型', 'value': 'phone_type'},
                                            {'label': '省份城市', 'value': 'location'},
                                            {'label': '运营商', 'value': 'operator'},
                                            {'label': '标记信息', 'value': 'mark_info'},
                                            {'label': '处理状态', 'value': 'status'},
                                            {'label': '时间信息', 'value': 'time_info'}
                                        ],
                                        value=['phone_number', 'phone_type', 'location', 'mark_info', 'status']
                                    )
                                ], label='导出字段')
                            ], span=12)
                        ], gutter=16)
                    ], layout='vertical')
                ], title='导出设置')
            ], span=24)
        ], style={'marginBottom': 16}),
        
        # 自定义筛选条件（当选择自定义筛选时显示）
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdTitle('筛选条件', level=4),
                    fac.AntdForm([
                        fac.AntdRow([
                            fac.AntdCol([
                                fac.AntdFormItem([
                                    fac.AntdInput(
                                        id='export-phone-filter',
                                        placeholder='支持通配符，如138*'
                                    )
                                ], label='电话号码')
                            ], span=6),
                            
                            fac.AntdCol([
                                fac.AntdFormItem([
                                    fac.AntdSelect(
                                        id='export-province-filter',
                                        placeholder='请选择省份',
                                        options=[
                                            {'label': '全部', 'value': ''},
                                            {'label': '北京', 'value': '北京'},
                                            {'label': '上海', 'value': '上海'},
                                            {'label': '广东', 'value': '广东'},
                                            {'label': '浙江', 'value': '浙江'},
                                            {'label': '江苏', 'value': '江苏'}
                                        ],
                                        allowClear=True
                                    )
                                ], label='省份')
                            ], span=6),
                            
                            fac.AntdCol([
                                fac.AntdFormItem([
                                    fac.AntdSelect(
                                        id='export-mark-type-filter',
                                        placeholder='请选择标记类型',
                                        options=[
                                            {'label': '全部', 'value': ''},
                                            {'label': '正常号码', 'value': '正常号码'},
                                            {'label': '骚扰电话', 'value': '骚扰电话'},
                                            {'label': '诈骗电话', 'value': '诈骗电话'},
                                            {'label': '推销电话', 'value': '推销电话'},
                                            {'label': '企业电话', 'value': '企业电话'}
                                        ],
                                        allowClear=True
                                    )
                                ], label='标记类型')
                            ], span=6),
                            
                            fac.AntdCol([
                                fac.AntdFormItem([
                                    fac.AntdSlider(
                                        id='export-confidence-range',
                                        range=True,
                                        min=0,
                                        max=100,
                                        value=[60, 100],
                                        marks={
                                            0: '0%',
                                            50: '50%',
                                            100: '100%'
                                        }
                                    )
                                ], label='置信度范围')
                            ], span=6)
                        ], gutter=16)
                    ], layout='inline')
                ], 
                id='export-custom-filter-card',
                style={'display': 'none'}  # 默认隐藏
                )
            ], span=24)
        ], style={'marginBottom': 16}),
        
        # 导出预览和操作
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdSpace([
                        fac.AntdButton(
                            '预览数据',
                            id='export-preview-btn',
                            type='default',
                            icon=fac.AntdIcon(icon='antd-eye')
                        ),
                        fac.AntdButton(
                            '开始导出',
                            id='export-start-btn',
                            type='primary',
                            icon=fac.AntdIcon(icon='antd-download')
                        ),
                        fac.AntdButton(
                            '清空配置',
                            id='export-reset-btn',
                            icon=fac.AntdIcon(icon='antd-clear')
                        )
                    ], size='large'),
                    
                    # 导出进度
                    html.Div([
                        fac.AntdProgress(
                            id='export-progress',
                            percent=0,
                            status='normal',
                            style={'marginTop': 16, 'display': 'none'}
                        ),
                        fac.AntdText(
                            id='export-status-text',
                            style={'marginTop': 8, 'display': 'block'}
                        )
                    ])
                ], title='导出操作')
            ], span=24)
        ], style={'marginBottom': 16}),
        
        # 导出历史
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdTable(
                        id='export-history-table',
                        columns=[
                            {'title': '导出时间', 'dataIndex': 'export_time', 'width': 150},
                            {'title': '数据类型', 'dataIndex': 'data_type', 'width': 100},
                            {'title': '导出格式', 'dataIndex': 'format', 'width': 80},
                            {'title': '记录数量', 'dataIndex': 'record_count', 'width': 100},
                            {'title': '文件大小', 'dataIndex': 'file_size', 'width': 100},
                            {'title': '状态', 'dataIndex': 'status', 'width': 80},
                            {'title': '操作', 'dataIndex': 'operation', 'width': 150}
                        ],
                        data=[],
                        pagination={
                            'pageSize': 10,
                            'current': 1,
                            'showSizeChanger': True,
                            'showQuickJumper': True
                        },
                        bordered=True,
                        size='small'
                    )
                ], title='导出历史')
            ], span=24)
        ]),
        
        # 预览模态框
        fac.AntdModal(
            id='export-preview-modal',
            title='数据预览',
            visible=False,
            width=1000,
            children=[
                fac.AntdTable(
                    id='export-preview-table',
                    columns=[],
                    data=[],
                    pagination={'pageSize': 10},
                    bordered=True,
                    size='small'
                )
            ],
            okText='确认导出',
            cancelText='取消'
        ),
        
        # 消息提示容器
        html.Div(id='export-message-container'),
        
        # 存储组件
        dcc.Store(id='export-config-store', data={}),
        dcc.Store(id='export-preview-data-store', data=[])
    ]


# 数据类型变化回调
@app.callback(
    Output('export-custom-filter-card', 'style'),
    [Input('export-data-type-select', 'value')],
    prevent_initial_call=True
)
def toggle_custom_filter(data_type):
    """
    根据数据类型显示/隐藏自定义筛选条件
    """
    if data_type == 'custom':
        return {'display': 'block'}
    else:
        return {'display': 'none'}


# 预览数据回调
@app.callback(
    [Output('export-preview-modal', 'visible'),
     Output('export-preview-table', 'columns'),
     Output('export-preview-table', 'data'),
     Output('export-message-container', 'children')],
    [Input('export-preview-btn', 'n_clicks')],
    [State('export-data-type-select', 'value'),
     State('export-fields-checkbox', 'value'),
     State('export-date-range', 'value'),
     State('export-phone-filter', 'value'),
     State('export-province-filter', 'value'),
     State('export-mark-type-filter', 'value'),
     State('export-confidence-range', 'value')],
    prevent_initial_call=True
)
def preview_export_data(preview_clicks, data_type, selected_fields, date_range, 
                       phone_filter, province_filter, mark_type_filter, confidence_range):
    """
    预览导出数据
    """
    if preview_clicks:
        try:
            # 构建预览数据的列定义
            column_map = {
                'phone_number': {'title': '电话号码', 'dataIndex': 'phone_number', 'key': 'phone_number'},
                'phone_type': {'title': '号码类型', 'dataIndex': 'phone_type', 'key': 'phone_type'},
                'location': {'title': '省份城市', 'dataIndex': 'location', 'key': 'location'},
                'operator': {'title': '运营商', 'dataIndex': 'operator', 'key': 'operator'},
                'mark_info': {'title': '标记信息', 'dataIndex': 'mark_info', 'key': 'mark_info'},
                'status': {'title': '状态', 'dataIndex': 'status', 'key': 'status'},
                'time_info': {'title': '创建时间', 'dataIndex': 'time_info', 'key': 'time_info'}
            }
            
            preview_columns = [column_map[field] for field in selected_fields if field in column_map]
            
            # 模拟预览数据
            preview_data = []
            for i in range(10):  # 预览前10条
                row_data = {}
                if 'phone_number' in selected_fields:
                    row_data['phone_number'] = f'1380013{i:04d}'
                if 'phone_type' in selected_fields:
                    row_data['phone_type'] = '手机' if i % 2 == 0 else '固话'
                if 'location' in selected_fields:
                    row_data['location'] = f'广东-广州'
                if 'operator' in selected_fields:
                    row_data['operator'] = ['中国移动', '中国联通', '中国电信'][i % 3]
                if 'mark_info' in selected_fields:
                    row_data['mark_info'] = f'标记{i*2}次-正常号码'
                if 'status' in selected_fields:
                    row_data['status'] = '已完成'
                if 'time_info' in selected_fields:
                    row_data['time_info'] = f'2025-07-{(i % 30) + 1:02d} 12:00:00'
                
                row_data['key'] = str(i)
                preview_data.append(row_data)
            
            return True, preview_columns, preview_data, None
            
        except Exception as e:
            return False, [], [], fac.AntdMessage(content=f'预览失败: {str(e)}', type='error')
    
    return dash.no_update, dash.no_update, dash.no_update, dash.no_update
