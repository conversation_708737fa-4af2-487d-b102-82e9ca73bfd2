/*
电话号码标记识别管理系统初始数据
包含默认用户、角色、菜单权限等
*/

USE phone_marking_system;

-- ----------------------------
-- 插入默认用户
-- ----------------------------
INSERT INTO `sys_user` VALUES 
(1, 'admin', '系统管理员', '<EMAIL>', '13800138000', '0', '', '$2b$12$7.8vgZJ5YqZ5YqZ5YqZ5YqZ5YqZ5YqZ5YqZ5YqZ5YqZ5YqZ5YqZ5Y', '0', '0', '127.0.0.1', NOW(), 'admin', NOW(), 'admin', NOW(), '管理员账户'),
(2, 'operator', '操作员', '<EMAIL>', '***********', '0', '', '$2b$12$7.8vgZJ5YqZ5YqZ5YqZ5YqZ5YqZ5YqZ5YqZ5YqZ5YqZ5YqZ5YqZ5Y', '0', '0', '', NULL, 'admin', NOW(), 'admin', NOW(), '普通操作员');

-- ----------------------------
-- 插入默认角色
-- ----------------------------
INSERT INTO `sys_role` VALUES 
(1, '超级管理员', 'admin', 1, '1', 1, 1, '0', '0', 'admin', NOW(), '', NULL, '超级管理员'),
(2, '操作员', 'operator', 2, '2', 1, 1, '0', '0', 'admin', NOW(), '', NULL, '普通操作员'),
(3, '查看员', 'viewer', 3, '3', 1, 1, '0', '0', 'admin', NOW(), '', NULL, '只读权限用户');

-- ----------------------------
-- 插入菜单权限
-- ----------------------------
INSERT INTO `sys_menu` VALUES 
-- 主菜单
(1, '系统管理', 0, 1, 'system', NULL, '', 1, 0, 'M', '0', '0', '', 'system', 'admin', NOW(), '', NULL, '系统管理目录'),
(2, '数据管理', 0, 2, 'data', NULL, '', 1, 0, 'M', '0', '0', '', 'database', 'admin', NOW(), '', NULL, '数据管理目录'),
(3, '任务管理', 0, 3, 'task', NULL, '', 1, 0, 'M', '0', '0', '', 'schedule', 'admin', NOW(), '', NULL, '任务管理目录'),
(4, '设备管理', 0, 4, 'device', NULL, '', 1, 0, 'M', '0', '0', '', 'mobile', 'admin', NOW(), '', NULL, '设备管理目录'),
(5, '统计分析', 0, 5, 'statistics', NULL, '', 1, 0, 'M', '0', '0', '', 'chart', 'admin', NOW(), '', NULL, '统计分析目录'),

-- 系统管理子菜单
(100, '用户管理', 1, 1, 'user', 'system/user/index', '', 1, 0, 'C', '0', '0', 'system:user:list', 'user', 'admin', NOW(), '', NULL, '用户管理菜单'),
(101, '角色管理', 1, 2, 'role', 'system/role/index', '', 1, 0, 'C', '0', '0', 'system:role:list', 'peoples', 'admin', NOW(), '', NULL, '角色管理菜单'),
(102, '菜单管理', 1, 3, 'menu', 'system/menu/index', '', 1, 0, 'C', '0', '0', 'system:menu:list', 'tree-table', 'admin', NOW(), '', NULL, '菜单管理菜单'),
(103, '系统日志', 1, 4, 'log', 'system/log/index', '', 1, 0, 'C', '0', '0', 'system:log:list', 'log', 'admin', NOW(), '', NULL, '系统日志菜单'),

-- 数据管理子菜单
(200, '号码数据', 2, 1, 'phone', 'data/phone/index', '', 1, 0, 'C', '0', '0', 'data:phone:list', 'phone', 'admin', NOW(), '', NULL, '号码数据菜单'),
(201, '批量导入', 2, 2, 'import', 'data/import/index', '', 1, 0, 'C', '0', '0', 'data:import:upload', 'upload', 'admin', NOW(), '', NULL, '批量导入菜单'),
(202, '数据导出', 2, 3, 'export', 'data/export/index', '', 1, 0, 'C', '0', '0', 'data:export:download', 'download', 'admin', NOW(), '', NULL, '数据导出菜单'),
(203, '数据同步', 2, 4, 'sync', 'data/sync/index', '', 1, 0, 'C', '0', '0', 'data:sync:manage', 'refresh', 'admin', NOW(), '', NULL, '数据同步菜单'),

-- 任务管理子菜单
(300, '批量任务', 3, 1, 'batch', 'task/batch/index', '', 1, 0, 'C', '0', '0', 'task:batch:list', 'job', 'admin', NOW(), '', NULL, '批量任务菜单'),
(301, '任务监控', 3, 2, 'monitor', 'task/monitor/index', '', 1, 0, 'C', '0', '0', 'task:monitor:view', 'monitor', 'admin', NOW(), '', NULL, '任务监控菜单'),

-- 设备管理子菜单
(400, '设备列表', 4, 1, 'list', 'device/list/index', '', 1, 0, 'C', '0', '0', 'device:list:view', 'list', 'admin', NOW(), '', NULL, '设备列表菜单'),
(401, '设备监控', 4, 2, 'monitor', 'device/monitor/index', '', 1, 0, 'C', '0', '0', 'device:monitor:view', 'dashboard', 'admin', NOW(), '', NULL, '设备监控菜单'),

-- 统计分析子菜单
(500, '数据统计', 5, 1, 'data', 'statistics/data/index', '', 1, 0, 'C', '0', '0', 'statistics:data:view', 'chart', 'admin', NOW(), '', NULL, '数据统计菜单'),
(501, '趋势分析', 5, 2, 'trend', 'statistics/trend/index', '', 1, 0, 'C', '0', '0', 'statistics:trend:view', 'line-chart', 'admin', NOW(), '', NULL, '趋势分析菜单'),
(502, '报表管理', 5, 3, 'report', 'statistics/report/index', '', 1, 0, 'C', '0', '0', 'statistics:report:view', 'documentation', 'admin', NOW(), '', NULL, '报表管理菜单');

-- ----------------------------
-- 用户角色关联
-- ----------------------------
INSERT INTO `sys_user_role` VALUES 
(1, 1),  -- admin用户 -> 超级管理员角色
(2, 2);  -- operator用户 -> 操作员角色

-- ----------------------------
-- 角色菜单关联（超级管理员拥有所有权限）
-- ----------------------------
INSERT INTO `sys_role_menu` VALUES 
-- 超级管理员权限
(1, 1), (1, 2), (1, 3), (1, 4), (1, 5),
(1, 100), (1, 101), (1, 102), (1, 103),
(1, 200), (1, 201), (1, 202), (1, 203),
(1, 300), (1, 301),
(1, 400), (1, 401),
(1, 500), (1, 501), (1, 502),

-- 操作员权限（除了用户管理、角色管理、菜单管理）
(2, 2), (2, 3), (2, 4), (2, 5),
(2, 103),
(2, 200), (2, 201), (2, 202), (2, 203),
(2, 300), (2, 301),
(2, 400), (2, 401),
(2, 500), (2, 501), (2, 502),

-- 查看员权限（只读权限）
(3, 2), (3, 4), (3, 5),
(3, 103),
(3, 200),
(3, 300), (3, 301),
(3, 400), (3, 401),
(3, 500), (3, 501), (3, 502);

-- ----------------------------
-- 插入示例电话号码数据
-- ----------------------------
INSERT INTO `phone_data` VALUES 
(1, '13800138000', 'mobile', '北京', '北京', '010', '中国移动', 15, '骚扰电话', NOW(), '{"type": "harassment", "tags": ["推销", "骚扰"]}', 95.50, 'completed', NULL, 1, NOW(), NOW()),
(2, '***********', 'mobile', '上海', '上海', '021', '中国联通', 8, '正常号码', NOW(), '{"type": "normal", "tags": []}', 88.20, 'completed', NULL, 1, NOW(), NOW()),
(3, '***********', 'mobile', '广东', '广州', '020', '中国电信', 22, '诈骗电话', NOW(), '{"type": "fraud", "tags": ["诈骗", "虚假"]}', 98.80, 'completed', NULL, 1, NOW(), NOW()),
(4, '***********', 'landline', '北京', '北京', '010', '', 5, '企业电话', NOW(), '{"type": "business", "tags": ["企业", "客服"]}', 85.30, 'completed', NULL, 1, NOW(), NOW()),
(5, '***********', 'landline', '广东', '广州', '020', '', 12, '推销电话', NOW(), '{"type": "marketing", "tags": ["推销", "广告"]}', 92.10, 'completed', NULL, 1, NOW(), NOW());

-- ----------------------------
-- 插入示例批量任务
-- ----------------------------
INSERT INTO `batch_task` VALUES 
(1, '批量导入-20250701', 'import', '/uploads/phone_data_20250701.xlsx', 'phone_data_20250701.xlsx', 1024000, 1000, 850, 800, 50, 85.00, 'running', NOW(), NULL, '', '', '{"batch_size": 100, "auto_process": true}', 1, NOW(), NOW()),
(2, '批量导入-20250630', 'import', '/uploads/phone_data_20250630.csv', 'phone_data_20250630.csv', 512000, 500, 500, 480, 20, 100.00, 'completed', DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_SUB(NOW(), INTERVAL 23 HOUR), '', '/exports/result_20250630.xlsx', '{"batch_size": 50, "auto_process": true}', 1, DATE_SUB(NOW(), INTERVAL 1 DAY), NOW());

-- ----------------------------
-- 插入示例设备数据
-- ----------------------------
INSERT INTO `device` VALUES 
(1, '测试设备1', 'device_001', 'android', 'Samsung Galaxy S21', 'Android 12', '1.0.0', 'online', NOW(), '192.168.1.100', '00:11:22:33:44:55', '{"auto_connect": true, "timeout": 30}', '{"cpu_usage": 45, "memory_usage": 60, "battery": 85}', 1, NOW(), NOW()),
(2, '测试设备2', 'device_002', 'android', 'Xiaomi Mi 11', 'Android 11', '1.0.0', 'offline', DATE_SUB(NOW(), INTERVAL 30 MINUTE), '192.168.1.101', '00:11:22:33:44:56', '{"auto_connect": false, "timeout": 60}', '{"cpu_usage": 0, "memory_usage": 0, "battery": 0}', 1, NOW(), NOW()),
(3, '测试设备3', 'device_003', 'android', 'Huawei P40', 'Android 10', '1.0.0', 'online', NOW(), '192.168.1.102', '00:11:22:33:44:57', '{"auto_connect": true, "timeout": 45}', '{"cpu_usage": 30, "memory_usage": 40, "battery": 92}', 1, NOW(), NOW());

-- ----------------------------
-- 插入示例系统日志
-- ----------------------------
INSERT INTO `sys_log` VALUES 
(1, 'login', '用户管理', '用户登录', 'POST', '/api/auth/login', '127.0.0.1', '{"username": "admin"}', '{"code": 200, "message": "登录成功"}', '', 150, 1, 'admin', NOW()),
(2, 'operation', '数据管理', '批量导入', 'POST', '/api/data/import', '127.0.0.1', '{"file": "phone_data.xlsx"}', '{"code": 200, "task_id": 1}', '', 2500, 1, 'admin', NOW()),
(3, 'operation', '设备管理', '设备连接检测', 'GET', '/api/device/check', '127.0.0.1', '{}', '{"code": 200, "online_count": 2}', '', 800, 1, 'admin', NOW());

-- ----------------------------
-- 插入示例同步记录
-- ----------------------------
INSERT INTO `sync_record` VALUES 
(1, 'upload', 'phone_data', 100, 95, 5, 'completed', DATE_SUB(NOW(), INTERVAL 2 HOUR), DATE_SUB(NOW(), INTERVAL 1 HOUR), '5条数据格式错误', 1, DATE_SUB(NOW(), INTERVAL 2 HOUR)),
(2, 'download', 'phone_data', 50, 50, 0, 'completed', DATE_SUB(NOW(), INTERVAL 1 HOUR), DATE_SUB(NOW(), INTERVAL 30 MINUTE), '', 1, DATE_SUB(NOW(), INTERVAL 1 HOUR));
