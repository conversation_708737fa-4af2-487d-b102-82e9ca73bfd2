# 开发环境配置文件

# 应用配置
APP_ENV=dev
APP_NAME=电话号码标记识别管理系统
APP_ROOT_PATH=/dev-api
APP_HOST=127.0.0.1
APP_PORT=9099
APP_VERSION=2.0.0
APP_RELOAD=true
APP_IP_LOCATION_QUERY=true
APP_SAME_TIME_LOGIN=true

# JWT配置
JWT_SECRET_KEY=phone_marking_system_secret_key_2025
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=1440
JWT_REDIS_EXPIRE_MINUTES=30

# 数据库配置（MySQL）
DB_HOST=127.0.0.1
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=
DB_DATABASE=phone_marking_system
DB_ECHO=false
DB_MAX_OVERFLOW=20
DB_POOL_SIZE=50
DB_POOL_RECYCLE=3600
DB_POOL_TIMEOUT=30

# Redis配置
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_USERNAME=
REDIS_PASSWORD=
REDIS_DATABASE=0

# 电话标记系统特定配置
PHONE_MARKER_CONFIG_PATH=../config/config.json
PHONE_MARKER_DATA_PATH=../data
PHONE_MARKER_UPLOAD_PATH=../data/uploads
PHONE_MARKER_EXPORT_PATH=../data/exports
PHONE_MARKER_LOG_PATH=../logs

# 设备管理配置
DEVICE_CHECK_INTERVAL=30
DEVICE_TIMEOUT=60
DEVICE_MAX_RETRY=3

# 批量处理配置
BATCH_SIZE=1000
MAX_CONCURRENT_TASKS=5
TASK_TIMEOUT=3600

# 文件上传配置
MAX_FILE_SIZE=100MB
ALLOWED_FILE_TYPES=xlsx,xls,csv,txt
