/*
电话号码标记识别管理系统数据库结构
支持完整的RBAC权限管理、批量处理、设备管理、数据统计等功能
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 创建数据库
CREATE DATABASE IF NOT EXISTS phone_marking_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE phone_marking_system;

-- ----------------------------
-- 用户表
-- ----------------------------
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user` (
  `user_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(30) NOT NULL COMMENT '用户账号',
  `nickname` varchar(30) NOT NULL COMMENT '用户昵称',
  `email` varchar(50) DEFAULT '' COMMENT '用户邮箱',
  `phone` varchar(11) DEFAULT '' COMMENT '手机号码',
  `sex` char(1) DEFAULT '0' COMMENT '用户性别（0男 1女 2未知）',
  `avatar` varchar(100) DEFAULT '' COMMENT '头像地址',
  `password` varchar(100) DEFAULT '' COMMENT '密码',
  `status` char(1) DEFAULT '0' COMMENT '帐号状态（0正常 1停用）',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `login_ip` varchar(128) DEFAULT '' COMMENT '最后登录IP',
  `login_date` datetime DEFAULT NULL COMMENT '最后登录时间',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`user_id`),
  UNIQUE KEY `uk_username` (`username`)
) ENGINE=InnoDB AUTO_INCREMENT=100 CHARACTER SET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户信息表';

-- ----------------------------
-- 角色表
-- ----------------------------
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role` (
  `role_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `role_name` varchar(30) NOT NULL COMMENT '角色名称',
  `role_key` varchar(100) NOT NULL COMMENT '角色权限字符串',
  `role_sort` int(4) NOT NULL COMMENT '显示顺序',
  `data_scope` char(1) DEFAULT '1' COMMENT '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）',
  `menu_check_strictly` tinyint(1) DEFAULT '1' COMMENT '菜单树选择项是否关联显示',
  `dept_check_strictly` tinyint(1) DEFAULT '1' COMMENT '部门树选择项是否关联显示',
  `status` char(1) NOT NULL COMMENT '角色状态（0正常 1停用）',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`role_id`)
) ENGINE=InnoDB AUTO_INCREMENT=100 CHARACTER SET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色信息表';

-- ----------------------------
-- 菜单权限表
-- ----------------------------
DROP TABLE IF EXISTS `sys_menu`;
CREATE TABLE `sys_menu` (
  `menu_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `menu_name` varchar(50) NOT NULL COMMENT '菜单名称',
  `parent_id` bigint(20) DEFAULT '0' COMMENT '父菜单ID',
  `order_num` int(4) DEFAULT '0' COMMENT '显示顺序',
  `path` varchar(200) DEFAULT '' COMMENT '路由地址',
  `component` varchar(255) DEFAULT NULL COMMENT '组件路径',
  `query` varchar(255) DEFAULT NULL COMMENT '路由参数',
  `is_frame` int(1) DEFAULT '1' COMMENT '是否为外链（0是 1否）',
  `is_cache` int(1) DEFAULT '0' COMMENT '是否缓存（0缓存 1不缓存）',
  `menu_type` char(1) DEFAULT '' COMMENT '菜单类型（M目录 C菜单 F按钮）',
  `visible` char(1) DEFAULT '0' COMMENT '菜单状态（0显示 1隐藏）',
  `status` char(1) DEFAULT '0' COMMENT '菜单状态（0正常 1停用）',
  `perms` varchar(100) DEFAULT NULL COMMENT '权限标识',
  `icon` varchar(100) DEFAULT '#' COMMENT '菜单图标',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`menu_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2000 CHARACTER SET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='菜单权限表';

-- ----------------------------
-- 用户和角色关联表
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_role`;
CREATE TABLE `sys_user_role` (
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `role_id` bigint(20) NOT NULL COMMENT '角色ID',
  PRIMARY KEY (`user_id`,`role_id`)
) ENGINE=InnoDB CHARACTER SET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户和角色关联表';

-- ----------------------------
-- 角色和菜单关联表
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_menu`;
CREATE TABLE `sys_role_menu` (
  `role_id` bigint(20) NOT NULL COMMENT '角色ID',
  `menu_id` bigint(20) NOT NULL COMMENT '菜单ID',
  PRIMARY KEY (`role_id`,`menu_id`)
) ENGINE=InnoDB CHARACTER SET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色和菜单关联表';

-- ----------------------------
-- 电话号码数据表
-- ----------------------------
DROP TABLE IF EXISTS `phone_data`;
CREATE TABLE `phone_data` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `phone_number` varchar(20) NOT NULL COMMENT '电话号码',
  `phone_type` varchar(20) DEFAULT 'mobile' COMMENT '号码类型（mobile手机 landline固话）',
  `province` varchar(50) DEFAULT '' COMMENT '省份',
  `city` varchar(50) DEFAULT '' COMMENT '城市',
  `area_code` varchar(10) DEFAULT '' COMMENT '区号',
  `operator` varchar(20) DEFAULT '' COMMENT '运营商',
  `mark_count` int(11) DEFAULT '0' COMMENT '标记数量',
  `mark_type` varchar(50) DEFAULT '' COMMENT '标记类型',
  `last_mark_time` datetime DEFAULT NULL COMMENT '最后标记时间',
  `recognition_result` text COMMENT '识别结果JSON',
  `confidence_score` decimal(5,2) DEFAULT '0.00' COMMENT '置信度分数',
  `status` varchar(20) DEFAULT 'pending' COMMENT '处理状态（pending待处理 processing处理中 completed已完成 failed失败）',
  `batch_id` bigint(20) DEFAULT NULL COMMENT '批次ID',
  `created_by` bigint(20) DEFAULT NULL COMMENT '创建者',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_phone_number` (`phone_number`),
  KEY `idx_province_city` (`province`,`city`),
  KEY `idx_status` (`status`),
  KEY `idx_batch_id` (`batch_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=1 CHARACTER SET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='电话号码数据表';

-- ----------------------------
-- 批量任务表
-- ----------------------------
DROP TABLE IF EXISTS `batch_task`;
CREATE TABLE `batch_task` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `task_name` varchar(100) NOT NULL COMMENT '任务名称',
  `task_type` varchar(20) NOT NULL COMMENT '任务类型（import导入 process处理 export导出）',
  `file_path` varchar(500) DEFAULT '' COMMENT '文件路径',
  `file_name` varchar(200) DEFAULT '' COMMENT '文件名称',
  `file_size` bigint(20) DEFAULT '0' COMMENT '文件大小',
  `total_count` int(11) DEFAULT '0' COMMENT '总数量',
  `processed_count` int(11) DEFAULT '0' COMMENT '已处理数量',
  `success_count` int(11) DEFAULT '0' COMMENT '成功数量',
  `failed_count` int(11) DEFAULT '0' COMMENT '失败数量',
  `progress` decimal(5,2) DEFAULT '0.00' COMMENT '进度百分比',
  `status` varchar(20) DEFAULT 'pending' COMMENT '任务状态（pending待处理 running运行中 completed已完成 failed失败 cancelled已取消）',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `error_message` text COMMENT '错误信息',
  `result_file_path` varchar(500) DEFAULT '' COMMENT '结果文件路径',
  `config` json DEFAULT NULL COMMENT '任务配置JSON',
  `created_by` bigint(20) DEFAULT NULL COMMENT '创建者',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_task_type` (`task_type`),
  KEY `idx_created_by` (`created_by`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=1 CHARACTER SET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='批量任务表';

-- ----------------------------
-- 设备管理表
-- ----------------------------
DROP TABLE IF EXISTS `device`;
CREATE TABLE `device` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '设备ID',
  `device_name` varchar(100) NOT NULL COMMENT '设备名称',
  `device_id` varchar(100) NOT NULL COMMENT '设备唯一标识',
  `device_type` varchar(20) DEFAULT 'android' COMMENT '设备类型（android安卓 ios苹果）',
  `device_model` varchar(100) DEFAULT '' COMMENT '设备型号',
  `system_version` varchar(50) DEFAULT '' COMMENT '系统版本',
  `app_version` varchar(50) DEFAULT '' COMMENT '应用版本',
  `status` varchar(20) DEFAULT 'offline' COMMENT '连接状态（online在线 offline离线 error错误）',
  `last_heartbeat` datetime DEFAULT NULL COMMENT '最后心跳时间',
  `ip_address` varchar(50) DEFAULT '' COMMENT 'IP地址',
  `mac_address` varchar(50) DEFAULT '' COMMENT 'MAC地址',
  `config` json DEFAULT NULL COMMENT '设备配置JSON',
  `performance_info` json DEFAULT NULL COMMENT '性能信息JSON',
  `created_by` bigint(20) DEFAULT NULL COMMENT '创建者',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_device_id` (`device_id`),
  KEY `idx_status` (`status`),
  KEY `idx_last_heartbeat` (`last_heartbeat`)
) ENGINE=InnoDB AUTO_INCREMENT=1 CHARACTER SET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='设备管理表';

-- ----------------------------
-- 系统日志表
-- ----------------------------
DROP TABLE IF EXISTS `sys_log`;
CREATE TABLE `sys_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `log_type` varchar(20) NOT NULL COMMENT '日志类型（login登录 operation操作 error错误 system系统）',
  `module` varchar(50) DEFAULT '' COMMENT '模块名称',
  `operation` varchar(100) DEFAULT '' COMMENT '操作内容',
  `method` varchar(100) DEFAULT '' COMMENT '请求方法',
  `request_url` varchar(500) DEFAULT '' COMMENT '请求URL',
  `request_ip` varchar(50) DEFAULT '' COMMENT '请求IP',
  `request_params` text COMMENT '请求参数',
  `response_data` text COMMENT '响应数据',
  `error_message` text COMMENT '错误信息',
  `execution_time` int(11) DEFAULT '0' COMMENT '执行时间（毫秒）',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
  `username` varchar(50) DEFAULT '' COMMENT '用户名',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_log_type` (`log_type`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=1 CHARACTER SET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统日志表';

-- ----------------------------
-- 数据同步记录表
-- ----------------------------
DROP TABLE IF EXISTS `sync_record`;
CREATE TABLE `sync_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '同步记录ID',
  `sync_type` varchar(20) NOT NULL COMMENT '同步类型（upload上传 download下载）',
  `table_name` varchar(50) NOT NULL COMMENT '表名',
  `sync_count` int(11) DEFAULT '0' COMMENT '同步数量',
  `success_count` int(11) DEFAULT '0' COMMENT '成功数量',
  `failed_count` int(11) DEFAULT '0' COMMENT '失败数量',
  `status` varchar(20) DEFAULT 'pending' COMMENT '同步状态（pending待处理 running运行中 completed已完成 failed失败）',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `error_message` text COMMENT '错误信息',
  `created_by` bigint(20) DEFAULT NULL COMMENT '创建者',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_sync_type` (`sync_type`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=1 CHARACTER SET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据同步记录表';

SET FOREIGN_KEY_CHECKS = 1;
